#!/bin/bash

# 部署脚本 - 智能质检规则配置系统
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev, prod (默认: prod)
# 操作: build, up, down, restart, logs (默认: up)

set -e

# 默认参数
ENV=${1:-prod}
ACTION=${2:-up}
IMAGE_NAME="rule-configuration-system"
CONTAINER_NAME="rule-config-app"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 构建镜像
build_image() {
    log_info "开始构建Docker镜像..."
    docker build -t $IMAGE_NAME:latest .
    log_success "镜像构建完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "服务启动成功！"
        log_info "访问地址: http://localhost:3055"
        log_info "健康检查: http://localhost:3055/health"
    else
        log_error "服务启动失败，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    docker-compose down
    docker-compose up -d
    log_success "服务重启完成"
}

# 查看日志
view_logs() {
    log_info "查看服务日志..."
    docker-compose logs -f
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    docker-compose down --rmi all --volumes --remove-orphans
    log_success "清理完成"
}

# 主函数
main() {
    log_info "智能质检规则配置系统 Docker 部署脚本"
    log_info "环境: $ENV, 操作: $ACTION"
    
    check_docker
    
    case $ACTION in
        "build")
            build_image
            ;;
        "up")
            build_image
            start_service
            ;;
        "down")
            stop_service
            ;;
        "restart")
            restart_service
            ;;
        "logs")
            view_logs
            ;;
        "cleanup")
            cleanup
            ;;
        *)
            log_error "未知操作: $ACTION"
            log_info "支持的操作: build, up, down, restart, logs, cleanup"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"