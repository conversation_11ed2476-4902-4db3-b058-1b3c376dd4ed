# 质检SOP规则配置与学习平台

## 项目概述

这是一个"AI质检规则配置与学习平台"。其核心目标是为非技术人员（如业务分析师、运营人员）提供一个可视化的界面，用来学习、配置和测试各种"质检算子"（即规则模块），最终组合成复杂的业务质检SOP（标准操作程序）。

## 技术栈

- **前端框架**: React 19 + TypeScript
- **构建工具**: Vite
- **路由**: React Router v6
- **UI与样式**: 自定义组件 + Tailwind CSS
- **动画**: Framer Motion
- **图标**: Lucide React
- **工具库**: axios, lodash-es, dayjs

## 项目架构

- **模块化设计**: 项目在架构上遵循了良好的模块化实践。每个"质检算子"（如关键词检查、正则检查等）都是一个独立的页面级组件。
- **组件化开发**: 每个算子页面都由更小的、可复用的子组件构成，通常遵循一个固定模式：
    - `Concepts`：解释算子的核心概念和原理。
    - `Config`：提供算子的配置界面。
    - `Tester`：用于实时测试算子效果。
    - `Cases`：提供预设的典型案例库。
    - `Tips`：给出使用该算子的技巧和建议。
- **统一布局与路由**: 所有页面共享一个 `MainLayout`，包含统一的导航栏和侧边栏。`App.tsx` 中定义了清晰的路由规则，将URL路径映射到具体的算子页面。
- **强类型定义**: `types.ts` 文件为整个应用提供了严格的数据结构定义（如 `Condition`, `BasicInfo`），确保了代码的健壮性和可维护性。

## 核心功能

1.  **算子学习与演示**: 用户可以通过侧边栏导航到各个算子的专属页面，学习其定义、原理和应用场景。
2.  **可视化配置**: 每个算子页面都提供了丰富的配置项，用户可以通过表单交互来调整算子的行为。
3.  **实时测试**: 用户可以在页面上直接输入测试文本，立即看到算子的检测结果，方便快速验证和迭代。
4.  **复杂规则组合**: `ComplexExamplePage.tsx` 页面展示了如何将多个独立的算子条件通过逻辑表达式组合起来，形成一个完整的、复杂的业务规则。

## 开发规范与约定

根据项目内置的《开发规范.md》，所有开发活动需遵循以下核心约定：

- **目录结构**:
  - 所有算子页面均存放在 `components/pages/operators/` 目录下。
  - 每个算子的主页面 (`[算子名]Page.tsx`) 与其子组件目录 (`[算子名]/`) 为同级。

- **组件开发**:
  - **命名**: 组件和文件名均使用大驼峰命名法 (PascalCase)。
  - **导出/导入**: **项目中禁止使用默认导出 (`export default`)**。所有模块必须使用命名导出 (`export const ...`) 和命名导入 (`import { ... }`)，以保证代码一致性。

- **代码风格**:
  - **格式化**: 使用 `ESLint` + `Prettier` 统一代码风格。
  - **Git提交**: Git commit message 需遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范。

- **测试**:
  - **单元测试**: 使用 `Jest` + `React Testing Library`。
  - **E2E测试**: 使用 `Playwright`。

## 本地运行

**环境要求:** Node.js

1.  安装依赖:
    ```bash
    npm install
    ```
2.  启动开发服务器:
    ```bash
    npm run dev
    ```
