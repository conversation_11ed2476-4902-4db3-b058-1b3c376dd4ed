{"name": "rule-configuration-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.4.0", "@ant-design/icons": "^6.0.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@types/flatpickr": "^3.0.2", "@types/react-router-dom": "^5.3.3", "@types/react-select": "^5.0.0", "@types/recharts": "^1.8.29", "antd": "^5.26.2", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3-cloud": "^1.2.7", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "flatpickr": "^4.6.13", "framer-motion": "^12.23.0", "lucide-react": "^0.523.0", "mermaid": "^11.7.0", "react": "^18.2.0", "react-d3-cloud": "^1.0.6", "react-dom": "^18.2.0", "react-router-dom": "^7.6.2", "react-select": "^5.10.1", "react-tagcloud": "^2.3.3", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "tailwind-scrollbar-hide": "^4.0.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react-tagcloud": "^2.3.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "shadcn-ui": "^0.9.5", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.4", "typescript": "~5.7.2", "vite": "^6.2.0"}}