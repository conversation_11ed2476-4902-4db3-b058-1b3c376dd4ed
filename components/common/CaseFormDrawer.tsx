import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, Plus, Trash2 } from 'lucide-react';

type CaseType = 'excellent' | 'violation' | 'pending';

export interface CaseConfig {
  label: string;
  value: string;
}

export interface CaseDialogue {
  speaker: '客服' | '客户';
  text: string;
  highlight?: boolean;
}

export interface CaseFormData {
  id?: number;
  type: CaseType;
  title: string;
  description: string;
  tags: string[];
  config: CaseConfig[];
  dialogue: CaseDialogue[];
  analysis: string;
}

interface CaseFormDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (caseData: CaseFormData) => void;
  caseData: Omit<CaseFormData, 'icon' | 'iconColor'> | null;
}

const libraryTypes = {
    excellent: { label: '优秀服务案例' },
    violation: { label: '典型违规案例' },
    pending: { label: '待分析问题' },
}

const emptyState: CaseFormData = {
    type: 'excellent',
    title: '',
    description: '',
    tags: [],
    config: [{ label: '', value: '' }],
    dialogue: [{ speaker: '客服', text: '' }],
    analysis: '',
};

export const CaseFormDrawer: React.FC<CaseFormDrawerProps> = ({ isOpen, onClose, onSave, caseData }) => {
    const [formData, setFormData] = useState<CaseFormData>(emptyState);
    const [tagInput, setTagInput] = useState('');

    useEffect(() => {
        if (caseData && isOpen) {
            setFormData(caseData);
            setTagInput(caseData.tags.join(', '));
        } else {
            setFormData(emptyState);
            setTagInput('');
        }
    }, [caseData, isOpen]);

    const handleFieldChange = <K extends keyof CaseFormData>(field: K, value: CaseFormData[K]) => {
        setFormData(prev => ({ ...prev, [field]: value }));
    };

    const handleDynamicChange = <T,>(list: T[], index: number, field: keyof T, value: any, setter: (list: T[]) => void) => {
        const newList = [...list];
        newList[index] = { ...newList[index], [field]: value };
        setter(newList);
    };

    const addDynamicItem = <T,>(list: T[], newItem: T, setter: (list: T[]) => void) => {
        setter([...list, newItem]);
    };
    
    const removeDynamicItem = <T,>(list: T[], index: number, setter: (list: T[]) => void) => {
        setter(list.filter((_, i) => i !== index));
    };


    const handleSave = (e: React.FormEvent) => {
        e.preventDefault();
        const tags = tagInput.split(',').map(t => t.trim()).filter(t => t);
        onSave({ ...formData, tags });
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black bg-opacity-50 z-40"
                    onClick={onClose}
                >
                    <motion.div
                        initial={{ x: '100%' }}
                        animate={{ x: 0 }}
                        exit={{ x: '100%' }}
                        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                        className="fixed top-0 right-0 w-full max-w-2xl h-full bg-white shadow-xl z-50 flex flex-col"
                        onClick={(e) => e.stopPropagation()}
                    >
                       <form onSubmit={handleSave} className="flex flex-col h-full">
                            <div className="flex justify-between items-center p-6 border-b">
                                <h2 className="text-xl font-bold text-gray-800">
                                    {caseData ? '编辑案例' : '新建案例'}
                                </h2>
                                <button type="button" onClick={onClose} className="p-2 text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded-full">
                                    <X size={24} />
                                </button>
                            </div>
                            <div className="flex-grow p-6 overflow-y-auto space-y-8">
                                {/* Basic Info */}
                                <div className="p-5 border rounded-lg">
                                    <h3 className="font-semibold text-lg mb-4">基本信息</h3>
                                    <div className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">案例类型</label>
                                            <select
                                                name="type"
                                                value={formData.type}
                                                onChange={(e) => handleFieldChange('type', e.target.value as CaseType)}
                                                className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                                {Object.entries(libraryTypes).map(([key, { label }]) => (
                                                    <option key={key} value={key}>{label}</option>
                                                ))}
                                            </select>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">案例标题</label>
                                            <input
                                                type="text"
                                                value={formData.title}
                                                onChange={(e) => handleFieldChange('title', e.target.value)}
                                                required
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">案例描述</label>
                                            <textarea
                                                rows={3}
                                                value={formData.description}
                                                onChange={(e) => handleFieldChange('description', e.target.value)}
                                                required
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            />
                                        </div>
                                         <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">标签</label>
                                            <input
                                                type="text"
                                                value={tagInput}
                                                onChange={(e) => setTagInput(e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="多个标签请用英文逗号分隔"
                                            />
                                        </div>
                                    </div>
                                </div>
                                
                                {/* Config Section */}
                                <div className="p-5 border rounded-lg">
                                     <h3 className="font-semibold text-lg mb-4">规则配置要点</h3>
                                     <div className="space-y-4">
                                        {formData.config.map((item, index) => (
                                            <div key={index} className="flex items-start gap-2 p-3 bg-gray-50 rounded-md">
                                                <div className="flex-grow space-y-2">
                                                    <input 
                                                        type="text"
                                                        placeholder="标签 (如: 核心规则)"
                                                        value={item.label}
                                                        onChange={(e) => handleDynamicChange(formData.config, index, 'label', e.target.value, (l) => handleFieldChange('config', l))}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                                    />
                                                    <textarea 
                                                        placeholder="内容"
                                                        rows={2}
                                                        value={item.value}
                                                        onChange={(e) => handleDynamicChange(formData.config, index, 'value', e.target.value, (l) => handleFieldChange('config', l))}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                                    />
                                                </div>
                                                <button type="button" onClick={() => removeDynamicItem(formData.config, index, (l) => handleFieldChange('config', l))} className="p-2 text-red-500 hover:bg-red-100 rounded-full mt-1">
                                                    <Trash2 size={18}/>
                                                </button>
                                            </div>
                                        ))}
                                        <button type="button" onClick={() => addDynamicItem(formData.config, {label: '', value: ''}, (l) => handleFieldChange('config', l))} className="flex items-center text-sm text-blue-600 font-semibold">
                                            <Plus size={16} className="mr-1"/> 添加配置项
                                        </button>
                                     </div>
                                </div>
                                
                                {/* Dialogue Section */}
                                <div className="p-5 border rounded-lg">
                                     <h3 className="font-semibold text-lg mb-4">对话场景再现</h3>
                                     <div className="space-y-4">
                                        {formData.dialogue.map((item, index) => (
                                            <div key={index} className="flex items-start gap-2 p-3 bg-gray-50 rounded-md">
                                                <div className="flex-grow space-y-2">
                                                    <div className="flex items-center gap-4">
                                                        <select
                                                            value={item.speaker}
                                                            onChange={(e) => handleDynamicChange(formData.dialogue, index, 'speaker', e.target.value, (l) => handleFieldChange('dialogue', l))}
                                                            className="px-3 py-2 border border-gray-300 rounded-md"
                                                        >
                                                            <option>客服</option>
                                                            <option>客户</option>
                                                        </select>
                                                         <div className="flex items-center">
                                                            <input
                                                                type="checkbox"
                                                                id={`highlight-${index}`}
                                                                checked={!!item.highlight}
                                                                onChange={(e) => handleDynamicChange(formData.dialogue, index, 'highlight', e.target.checked, (l) => handleFieldChange('dialogue', l))}
                                                                className="h-4 w-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
                                                            />
                                                            <label htmlFor={`highlight-${index}`} className="ml-2 text-sm text-gray-700">高亮显示</label>
                                                        </div>
                                                    </div>
                                                    <textarea 
                                                        placeholder="对话内容"
                                                        rows={2}
                                                        value={item.text}
                                                        onChange={(e) => handleDynamicChange(formData.dialogue, index, 'text', e.target.value, (l) => handleFieldChange('dialogue', l))}
                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                                                    />
                                                </div>
                                                <button type="button" onClick={() => removeDynamicItem(formData.dialogue, index, (l) => handleFieldChange('dialogue', l))} className="p-2 text-red-500 hover:bg-red-100 rounded-full mt-1">
                                                    <Trash2 size={18}/>
                                                </button>
                                            </div>
                                        ))}
                                         <button type="button" onClick={() => addDynamicItem(formData.dialogue, {speaker: '客服', text: ''}, (l) => handleFieldChange('dialogue', l))} className="flex items-center text-sm text-blue-600 font-semibold">
                                            <Plus size={16} className="mr-1"/> 添加对话
                                        </button>
                                     </div>
                                </div>

                                {/* Analysis Section */}
                                <div className="p-5 border rounded-lg">
                                    <h3 className="font-semibold text-lg mb-4">案例分析与解读</h3>
                                    <textarea
                                        rows={4}
                                        value={formData.analysis}
                                        onChange={(e) => handleFieldChange('analysis', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="请输入对这个案例的专业分析和解读"
                                    />
                                </div>

                            </div>
                            <div className="flex justify-end items-center p-6 border-t bg-gray-50">
                                <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 mr-3">
                                    取消
                                </button>
                                <button
                                    type="submit"
                                    className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700"
                                >
                                    <Save size={16} className="mr-2" />
                                    保存
                                </button>
                            </div>
                       </form>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}; 