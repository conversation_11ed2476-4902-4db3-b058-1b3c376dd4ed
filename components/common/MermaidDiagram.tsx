import React, { useEffect, useState } from 'react';
import mermaid from 'mermaid';
import { X } from 'lucide-react';

interface MermaidDiagramProps {
  chart: string;
}

// Initialize mermaid once, outside of the component
mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    securityLevel: 'loose',
    fontFamily: 'inherit',
});

/**
 * Renders a Mermaid diagram from a chart string.
 * This component uses mermaid.render() to generate an SVG and then displays it using a React state.
 * It also includes a modal to view the diagram in a larger size.
 * @param {string} chart The Mermaid diagram definition string.
 */
const MermaidDiagram: React.FC<MermaidDiagramProps> = ({ chart }) => {
    const [svg, setSvg] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    useEffect(() => {
        if (!chart) return;

        const renderDiagram = async () => {
            try {
                const id = `mermaid-diagram-${Math.random().toString(36).substr(2, 9)}`;
                const { svg: svgCode } = await mermaid.render(id, chart);
                setSvg(svgCode);
                setError(null);
            } catch (e: any) {
                console.error("Mermaid rendering error:", e.message || e);
                setError("无法渲染流程图，请检查语法。");
                setSvg(null);
            }
        };

        renderDiagram();
    }, [chart]);

    const DiagramModal = () => (
        <div
            className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-[100]"
            onClick={() => setIsModalOpen(false)}
        >
            <div
                className="bg-white p-6 rounded-xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-auto relative"
                onClick={(e) => e.stopPropagation()}
            >
                <button
                    onClick={() => setIsModalOpen(false)}
                    className="absolute top-3 right-3 p-2 text-gray-500 hover:bg-gray-100 rounded-full transition-colors"
                    aria-label="关闭图表"
                >
                    <X size={20} />
                </button>
                <div
                    className="w-full h-full flex items-center justify-center p-4"
                    dangerouslySetInnerHTML={{ __html: svg || '' }}
                />
            </div>
        </div>
    );

    if (error) {
        return (
            <div className="p-4 text-center text-red-500 bg-red-50 rounded-lg">
                <p>{error}</p>
            </div>
        );
    }

    if (!svg) {
        return (
            <div className="p-4 text-center text-gray-500">
                正在加载图表...
            </div>
        );
    }

    return (
        <>
            <div 
                className="mermaid-diagram flex justify-center cursor-zoom-in hover:opacity-80 transition-opacity" 
                onClick={() => setIsModalOpen(true)}
                title="点击放大查看"
                dangerouslySetInnerHTML={{ __html: svg }} 
            />
            {isModalOpen && <DiagramModal />}
        </>
    );
};

export default MermaidDiagram; 