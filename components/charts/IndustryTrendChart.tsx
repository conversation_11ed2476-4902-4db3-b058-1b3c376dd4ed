import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface IndustryTrendChartProps {
  className?: string;
}

/**
 * 行业趋势图表组件
 * 展示AI质检技术采用率增长趋势
 */
const IndustryTrendChart: React.FC<IndustryTrendChartProps> = ({ className = '' }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    // 行业趋势数据
    const years = ['2020', '2021', '2022', '2023', '2024', '2025', '2026'];
    const adoptionRate = [5, 12, 25, 42, 58, 75, 88]; // AI质检采用率
    const marketSize = [2.1, 3.8, 6.5, 11.2, 18.5, 28.3, 42.1]; // 市场规模(亿元)
    const efficiency = [15, 28, 45, 68, 85, 95, 98]; // 效率提升百分比

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            color: '#374151'
          }
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: 'rgba(255, 255, 255, 0.3)',
        borderWidth: 1,
        textStyle: {
          color: '#374151'
        },
        formatter: function(params: any) {
          let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValue}年</div>`;
          params.forEach((param: any) => {
            const unit = param.seriesName === '市场规模' ? '亿元' : '%';
            result += `<div style="margin: 4px 0;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              ${param.seriesName}: ${param.value}${unit}
            </div>`;
          });
          return result;
        }
      },
      legend: {
        data: ['AI质检采用率', '市场规模', '效率提升'],
        top: 15,
        textStyle: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 12
        },
        itemGap: 20
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '8%',
        top: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: years,
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 11
        },
        axisTick: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '采用率/效率(%)',
          position: 'left',
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)'
            }
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.8)',
            formatter: '{value}%',
            fontSize: 11
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed'
            }
          },
          nameTextStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            fontSize: 11
          }
        },
        {
          type: 'value',
          name: '市场规模(亿元)',
          position: 'right',
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)'
            }
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.8)',
            formatter: '{value}',
            fontSize: 11
          },
          nameTextStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            fontSize: 11
          }
        }
      ],
      series: [
        {
          name: 'AI质检采用率',
          type: 'line',
          yAxisIndex: 0,
          data: adoptionRate,
          smooth: true,
          lineStyle: {
            color: '#60a5fa',
            width: 3,
            shadowColor: 'rgba(96, 165, 250, 0.3)',
            shadowBlur: 10
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(96, 165, 250, 0.4)'
              },
              {
                offset: 1,
                color: 'rgba(96, 165, 250, 0.05)'
              }
            ])
          },
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#60a5fa',
            borderColor: '#ffffff',
            borderWidth: 2
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(96, 165, 250, 0.5)'
            }
          }
        },
        {
          name: '市场规模',
          type: 'bar',
          yAxisIndex: 1,
          data: marketSize,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(168, 85, 247, 0.8)'
              },
              {
                offset: 1,
                color: 'rgba(168, 85, 247, 0.4)'
              }
            ]),
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(168, 85, 247, 0.5)'
            }
          }
        },
        {
          name: '效率提升',
          type: 'line',
          yAxisIndex: 0,
          data: efficiency,
          smooth: true,
          lineStyle: {
            color: '#34d399',
            width: 3,
            type: 'dashed',
            shadowColor: 'rgba(52, 211, 153, 0.3)',
            shadowBlur: 10
          },
          symbol: 'diamond',
          symbolSize: 8,
          itemStyle: {
            color: '#34d399',
            borderColor: '#ffffff',
            borderWidth: 2
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(52, 211, 153, 0.5)'
            }
          }
        }
      ],
      animation: true,
      animationDuration: 2000,
      animationEasing: 'cubicOut'
    };

    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  return (
    <div 
      ref={chartRef} 
      className={`w-full h-48 ${className}`}
      style={{ minHeight: '192px' }}
    />
  );
};

export default IndustryTrendChart;
