import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface RuleAnalysisChartProps {
  className?: string;
}

/**
 * 质检规则分析饼图组件
 * 展示各类质检规则的失分分布
 */
const RuleAnalysisChart: React.FC<RuleAnalysisChartProps> = ({ className = '' }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    // 质检规则失分数据
    const ruleData = [
      { value: 35, name: '服务态度', itemStyle: { color: '#ff6b6b' } },
      { value: 28, name: '业务规范', itemStyle: { color: '#4ecdc4' } },
      { value: 22, name: '沟通技巧', itemStyle: { color: '#45b7d1' } },
      { value: 15, name: '合规要求', itemStyle: { color: '#f9ca24' } },
      { value: 12, name: '开场结束', itemStyle: { color: '#6c5ce7' } },
      { value: 8, name: '其他问题', itemStyle: { color: '#a0a0a0' } }
    ];

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          return `<div style="font-weight: bold; margin-bottom: 8px;">${params.name}</div>
                  <div>失分次数: ${params.value}</div>
                  <div>占比: ${params.percent}%</div>`;
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: {
          color: '#374151'
        }
      },
      legend: {
        orient: 'vertical',
        left: 'right',
        top: 'center',
        textStyle: {
          color: '#6b7280',
          fontSize: 12
        },
        itemGap: 12,
        formatter: function(name: string) {
          const item = ruleData.find(d => d.name === name);
          return `${name} (${item?.value || 0})`;
        }
      },
      series: [
        {
          name: '质检规则失分分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['35%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold',
              color: '#333',
              formatter: function(params: any) {
                return `${params.name}\n${params.percent}%`;
              }
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          data: ruleData,
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx: number) {
            return Math.random() * 200;
          }
        }
      ]
    };

    chartInstance.current.setOption(option);

    // 响应式处理
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  return (
    <div 
      ref={chartRef} 
      className={`w-full h-64 ${className}`}
      style={{ minHeight: '256px' }}
    />
  );
};

export default RuleAnalysisChart;
