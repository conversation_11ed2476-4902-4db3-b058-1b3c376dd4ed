import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { generateTimeSeriesData, generateScoreData, chartTheme, animationConfig } from '../../utils/chartUtils';

interface RealTimeChartProps {
  className?: string;
}

/**
 * 实时数据趋势图表组件
 * 展示质检量和平均分的实时趋势
 */
const RealTimeChart: React.FC<RealTimeChartProps> = ({ className = '' }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    // 生成模拟数据
    const timeData = generateTimeSeriesData(24);
    const hours = timeData.labels;
    const qualityData = timeData.data;
    const scoreData = generateScoreData(24);

    const option = {
      ...chartTheme,
      ...animationConfig,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: {
          color: '#374151'
        }
      },
      legend: {
        data: ['质检量', '平均分'],
        top: 10,
        textStyle: {
          color: '#6b7280'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: hours,
          axisLine: {
            lineStyle: {
              color: '#e5e7eb'
            }
          },
          axisLabel: {
            color: '#6b7280'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '质检量',
          position: 'left',
          axisLine: {
            lineStyle: {
              color: '#3b82f6'
            }
          },
          axisLabel: {
            color: '#6b7280',
            formatter: '{value}'
          },
          splitLine: {
            lineStyle: {
              color: '#f3f4f6'
            }
          }
        },
        {
          type: 'value',
          name: '平均分',
          position: 'right',
          axisLine: {
            lineStyle: {
              color: '#10b981'
            }
          },
          axisLabel: {
            color: '#6b7280',
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: '质检量',
          type: 'line',
          yAxisIndex: 0,
          data: qualityData,
          smooth: true,
          lineStyle: {
            color: '#3b82f6',
            width: 3
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(59, 130, 246, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(59, 130, 246, 0.05)'
              }
            ])
          },
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#3b82f6'
          }
        },
        {
          name: '平均分',
          type: 'line',
          yAxisIndex: 1,
          data: scoreData,
          smooth: true,
          lineStyle: {
            color: '#10b981',
            width: 3
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(16, 185, 129, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(16, 185, 129, 0.05)'
              }
            ])
          },
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#10b981'
          }
        }
      ]
    };

    chartInstance.current.setOption(option);

    // 模拟实时数据更新
    const interval = setInterval(() => {
      if (chartInstance.current) {
        const newHour = new Date().getHours() + ':00';
        const newQualityData = Math.floor(Math.random() * 1000) + 500;
        const newScoreData = (Math.random() * 15 + 80).toFixed(1);

        // 更新数据
        hours.shift();
        hours.push(newHour);
        qualityData.shift();
        qualityData.push(newQualityData);
        scoreData.shift();
        scoreData.push(parseFloat(newScoreData));

        const newOption = {
          xAxis: [{ data: hours }],
          series: [
            { data: qualityData },
            { data: scoreData }
          ]
        };

        chartInstance.current.setOption(newOption);
      }
    }, 5000); // 每5秒更新一次

    // 响应式处理
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      clearInterval(interval);
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  return (
    <div 
      ref={chartRef} 
      className={`w-full h-64 ${className}`}
      style={{ minHeight: '256px' }}
    />
  );
};

export default RealTimeChart;
