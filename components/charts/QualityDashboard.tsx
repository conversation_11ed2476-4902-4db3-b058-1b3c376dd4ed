import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface QualityDashboardProps {
  className?: string;
}

/**
 * 质检数据仪表盘组件
 * 展示质检合格率、AI准确率等关键指标
 */
const QualityDashboard: React.FC<QualityDashboardProps> = ({ className = '' }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);

    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        formatter: function(params: any) {
          return `<div style="font-weight: bold;">${params.seriesName}</div>
                  <div style="margin-top: 8px;">当前值: ${params.value}%</div>`;
        }
      },
      series: [
        {
          name: '质检合格率',
          type: 'gauge',
          center: ['25%', '50%'],
          radius: '80%',
          min: 0,
          max: 100,
          splitNumber: 10,
          axisLine: {
            lineStyle: {
              color: [
                [0.6, '#ff6b6b'],
                [0.8, '#feca57'],
                [1, '#48dbfb']
              ],
              width: 8
            }
          },
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisTick: {
            length: 8,
            lineStyle: {
              color: 'auto'
            }
          },
          splitLine: {
            length: 12,
            lineStyle: {
              color: 'auto'
            }
          },
          pointer: {
            width: 4,
            length: '70%',
            itemStyle: {
              color: 'auto'
            }
          },
          detail: {
            fontSize: 14,
            fontWeight: 'bold',
            color: '#333',
            formatter: '{value}%',
            offsetCenter: [0, '70%']
          },
          title: {
            fontSize: 12,
            color: '#666',
            offsetCenter: [0, '-80%']
          },
          data: [
            {
              value: 92.5,
              name: '质检合格率'
            }
          ]
        },
        {
          name: 'AI准确率',
          type: 'gauge',
          center: ['75%', '50%'],
          radius: '80%',
          min: 0,
          max: 100,
          splitNumber: 10,
          axisLine: {
            lineStyle: {
              color: [
                [0.6, '#ff6b6b'],
                [0.8, '#feca57'],
                [1, '#1dd1a1']
              ],
              width: 8
            }
          },
          axisLabel: {
            fontSize: 10,
            color: '#666'
          },
          axisTick: {
            length: 8,
            lineStyle: {
              color: 'auto'
            }
          },
          splitLine: {
            length: 12,
            lineStyle: {
              color: 'auto'
            }
          },
          pointer: {
            width: 4,
            length: '70%',
            itemStyle: {
              color: 'auto'
            }
          },
          detail: {
            fontSize: 14,
            fontWeight: 'bold',
            color: '#333',
            formatter: '{value}%',
            offsetCenter: [0, '70%']
          },
          title: {
            fontSize: 12,
            color: '#666',
            offsetCenter: [0, '-80%']
          },
          data: [
            {
              value: 96.8,
              name: 'AI准确率'
            }
          ]
        }
      ]
    };

    chartInstance.current.setOption(option);

    // 模拟数据更新
    const interval = setInterval(() => {
      if (chartInstance.current) {
        const qualityRate = 88 + Math.random() * 10; // 88-98之间
        const aiAccuracy = 94 + Math.random() * 5; // 94-99之间

        const newOption = {
          series: [
            {
              data: [{ value: parseFloat(qualityRate.toFixed(1)), name: '质检合格率' }]
            },
            {
              data: [{ value: parseFloat(aiAccuracy.toFixed(1)), name: 'AI准确率' }]
            }
          ]
        };

        chartInstance.current.setOption(newOption);
      }
    }, 3000); // 每3秒更新一次

    // 响应式处理
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      clearInterval(interval);
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  return (
    <div 
      ref={chartRef} 
      className={`w-full h-64 ${className}`}
      style={{ minHeight: '256px' }}
    />
  );
};

export default QualityDashboard;
