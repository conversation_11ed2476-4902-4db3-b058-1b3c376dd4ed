import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Trash2, Users, Bell } from 'lucide-react';
import { UserSelectionModal } from './UserSelectionModal';

// --- Data Structures ---

interface Condition {
    id: string;
    subject: string;
    operator: string;
    value: any;
}

interface Action {
    type: string;
    target: string;
}

interface NotificationConfig {
    enabled: boolean;
    target: string;
    message: string;
}

interface Strategy {
    id?: string;
    name: string;
    description: string;
    priority: '高' | '中' | '低';
    conditions: Condition[];
    action: Action;
    notification?: NotificationConfig;
}

interface CreateStrategyFormProps {
    onClose: () => void;
    onSubmit: (strategy: Strategy) => void;
    initialData?: Strategy;
}


// --- Form Configuration ---

export const conditionSubjects = {
    totalScore: { label: '质总分', operators: ['<', '>', '=', '!=', '<=', '>='], valueType: 'number' },
    ruleImportance: { label: '命中规则重要程度', operators: ['=', '!='], valueType: 'select', options: ['严重违规', '中度违规', '轻度违规'] },
    ruleNeedsReview: { label: '命中规则需要人工复核', operators: ['='], valueType: 'select', options: ['是', '否'] },
    agentTag: { label: '坐席标签', operators: ['=', '!='], valueType: 'text' },
    schemeId: { label: '所用质检方案', operators: ['=', '!='], valueType: 'select', options: ['服务规范通用方案', '金融产品销售合规方案', '客户体验优化方案'] },
    randomSampling: { label: '随机抽样', operators: ['='], valueType: 'percentage' },
};

export const actionTypes = {
    assignToUser: { label: '分配给指定人员', targetType: 'user-select', targetLabel: '人员姓名', multiSelect: false },
    assignToGroup: { label: '分配到指定复核池', targetType: 'select', targetLabel: '复核池', options: ['低分组复核池', '合规审查组', '新人导师组', '质检质量抽检池', '优先复核池'], multiSelect: false },
    pushToCaseLibrary: { label: '推送至案例库', targetType: 'select', targetLabel: '目标案例库', options: ['优秀服务案例库', '典型违规案例库', '待分析问题池'], multiSelect: false },
    assignToUserAverage: { label: '平均分配给指定人员', targetType: 'user-select', targetLabel: '人员列表', multiSelect: true },
    assignToUserRandom: { label: '随机分配给指定人员', targetType: 'user-select', targetLabel: '人员列表', multiSelect: true },
    markAsAutoClosed: { label: '标记为已自动关闭', targetType: 'none', targetLabel: '', multiSelect: false },
};


// --- Main Component ---

export const CreateStrategyForm: React.FC<CreateStrategyFormProps> = ({ onClose, onSubmit, initialData }) => {
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [priority, setPriority] = useState<'高' | '中' | '低'>('中');
    const [conditions, setConditions] = useState<Condition[]>([]);
    const [action, setAction] = useState<Action>({ type: 'assignToUser', target: '' });
    const [notification, setNotification] = useState<NotificationConfig>({ enabled: false, target: '', message: '' });
    const [isUserModalOpen, setIsUserModalOpen] = useState(false);
    const [isNotificationUserModalOpen, setIsNotificationUserModalOpen] = useState(false);
    
    useEffect(() => {
        if (initialData) {
            setName(initialData.name || '');
            setDescription(initialData.description || '');
            setPriority(initialData.priority || '中');
            setConditions(initialData.conditions || [{ id: `cond-${Date.now()}`, subject: 'totalScore', operator: '<', value: '60' }]);
            setAction(initialData.action || { type: 'assignToUser', target: '' });
            setNotification(initialData.notification || { enabled: false, target: '', message: '' });
        }
    }, [initialData]);

    const handleAddCondition = () => {
        const newCondition: Condition = {
            id: `cond-${Date.now()}`,
            subject: 'totalScore',
            operator: '<',
            value: '',
        };
        setConditions(prev => [...prev, newCondition]);
    };
    
    const handleRemoveCondition = (id: string) => {
        setConditions(prev => prev.filter(c => c.id !== id));
    };

    const handleConditionChange = (id: string, field: keyof Condition, value: any) => {
        setConditions(prev => prev.map(c => {
            if (c.id === id) {
                const updatedCondition = { ...c, [field]: value };
                // Reset operator and value if subject changes
                if (field === 'subject') {
                    const newSubjectConfig = conditionSubjects[value as keyof typeof conditionSubjects];
                    updatedCondition.operator = newSubjectConfig.operators[0];
                    updatedCondition.value = '';
                }
                return updatedCondition;
            }
            return c;
        }));
    };
    
    const handleActionChange = (field: keyof Action, value: any) => {
        setAction(prev => {
            const updatedAction = { ...prev, [field]: value };
            if (field === 'type') {
                updatedAction.target = ''; // Reset target when type changes
            }
            return updatedAction;
        });
    };

    const handleNotificationChange = (field: keyof NotificationConfig, value: any) => {
        setNotification(prev => ({ ...prev, [field]: value }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit({ id: initialData?.id, name, description, priority, conditions, action, notification });
    };

    const renderValueInput = (condition: Condition) => {
        const config = conditionSubjects[condition.subject as keyof typeof conditionSubjects];
        switch (config.valueType) {
            case 'number':
                return <input type="number" value={condition.value} onChange={e => handleConditionChange(condition.id, 'value', e.target.value)} className="w-full px-2 py-1.5 border border-gray-300 rounded-md text-sm" />;
            case 'percentage':
                return (
                    <div className="relative">
                        <input type="number" min="0" max="100" value={condition.value} onChange={e => handleConditionChange(condition.id, 'value', e.target.value)} className="w-full px-2 py-1.5 border border-gray-300 rounded-md text-sm pr-6" />
                        <span className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 text-sm">%</span>
                    </div>
                );
            case 'select':
                if ('options' in config && Array.isArray(config.options)) {
                    return (
                        <select value={condition.value} onChange={e => handleConditionChange(condition.id, 'value', e.target.value)} className="w-full px-2 py-1.5 border border-gray-300 rounded-md text-sm bg-white">
                            <option value="">请选择</option>
                            {config.options.map((opt: string) => <option key={opt} value={opt}>{opt}</option>)}
                        </select>
                    );
                }
                return null;
            case 'text':
            default:
                return <input type="text" value={condition.value} onChange={e => handleConditionChange(condition.id, 'value', e.target.value)} className="w-full px-2 py-1.5 border border-gray-300 rounded-md text-sm" />;
        }
    };
    
    const renderActionTargetInput = () => {
        const config = actionTypes[action.type as keyof typeof actionTypes];
        switch (config.targetType) {
            case 'user-select':
                const selectedUsers = action.target ? action.target.split(',').map(u => u.trim()) : [];
                return (
                    <div>
                        <div className="flex flex-wrap gap-2 items-center p-2 border border-gray-300 rounded-lg min-h-[42px]">
                            {selectedUsers.length > 0 ? (
                                selectedUsers.map(user => (
                                    <span key={user} className="flex items-center bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-1 rounded-full">
                                        {user}
                                        <button 
                                            type="button" 
                                            onClick={() => handleActionChange('target', selectedUsers.filter(u => u !== user).join(','))}
                                            className="ml-1.5 -mr-1 text-blue-600 hover:text-blue-800"
                                        >
                                            <X className="w-3 h-3" />
                                        </button>
                                    </span>
                                ))
                            ) : (
                                <span className="text-sm text-gray-500 px-1">未选择任何人员</span>
                            )}
                        </div>
                        <button 
                            type="button" 
                            onClick={() => setIsUserModalOpen(true)}
                            className="mt-2 flex items-center justify-center gap-2 w-full p-2 text-sm bg-gray-100 text-gray-700 border-2 border-transparent rounded-lg hover:bg-gray-200"
                        >
                            <Users className="w-4 h-4" />
                            选择人员
                        </button>
                    </div>
                );
            case 'select':
                 if ('options' in config && Array.isArray(config.options)) {
                    return (
                         <select value={action.target} onChange={e => handleActionChange('target', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white">
                            <option value="">请选择{config.targetLabel}</option>
                            {config.options.map((opt: string) => <option key={opt} value={opt}>{opt}</option>)}
                        </select>
                    );
                }
                return null;
            case 'textarea':
                return <textarea value={action.target} onChange={e => handleActionChange('target', e.target.value)} rows={3} className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm" placeholder={`请输入${config.targetLabel}`} />;
            case 'text':
            default:
                return <input type="text" placeholder={`请输入${config.targetLabel}`} value={action.target} onChange={e => handleActionChange('target', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm" />;
        }
    };

    return (
        <motion.div className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-40" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <motion.div className="bg-white h-full w-full max-w-2xl shadow-2xl flex flex-col" initial={{ x: '100%' }} animate={{ x: 0 }} exit={{ x: '100%' }} transition={{ type: 'spring', stiffness: 300, damping: 30 }}>
                <div className="flex-shrink-0 flex items-center justify-between p-6 border-b">
                    <h2 className="text-xl font-semibold">{initialData ? '编辑策略' : '创建新策略'}</h2>
                    <button onClick={onClose} className="p-2 text-gray-400 hover:text-gray-600 rounded-full"><X /></button>
                </div>

                <form onSubmit={handleSubmit} className="flex-grow flex flex-col overflow-hidden">
                    <div className="flex-grow p-6 space-y-6 overflow-y-auto">
                        {/* --- Basic Info --- */}
                        <div className="space-y-4 p-4 border rounded-lg bg-white">
                             <h3 className="text-lg font-medium">基本信息</h3>
                             <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">策略名称 <span className="text-red-500">*</span></label>
                                    <input type="text" value={name} onChange={(e) => setName(e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                                    <select value={priority} onChange={(e) => setPriority(e.target.value as '高' | '中' | '低')} className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white">
                                        <option value="高">高</option>
                                        <option value="中">中</option>
                                        <option value="低">低</option>
                                    </select>
                                </div>
                                <div className="col-span-2">
                                     <label className="block text-sm font-medium text-gray-700 mb-1">策略描述</label>
                                     <textarea value={description} onChange={(e) => setDescription(e.target.value)} rows={3} className="w-full px-3 py-2 border border-gray-300 rounded-lg" />
                                 </div>
                             </div>
                        </div>

                        {/* --- Conditions (IF) --- */}
                        <div className="space-y-4 p-4 border rounded-lg bg-white">
                            <h3 className="text-lg font-medium">触发条件 (IF)</h3>
                            <div className="space-y-3">
                                {conditions.map((condition, index) => (
                                    <div key={condition.id} className="grid grid-cols-12 gap-3 items-center">
                                        <div className="col-span-1 text-sm text-gray-500">{index === 0 ? '当' : '且'}</div>
                                        <div className="col-span-4">
                                            <select value={condition.subject} onChange={e => handleConditionChange(condition.id, 'subject', e.target.value)} className="w-full px-2 py-1.5 border border-gray-300 rounded-md text-sm bg-white">
                                                {Object.entries(conditionSubjects).map(([key, { label }]) => <option key={key} value={key}>{label}</option>)}
                                            </select>
                                        </div>
                                        <div className="col-span-2">
                                            <select value={condition.operator} onChange={e => handleConditionChange(condition.id, 'operator', e.target.value)} className="w-full px-2 py-1.5 border border-gray-300 rounded-md text-sm bg-white">
                                               {conditionSubjects[condition.subject as keyof typeof conditionSubjects].operators.map(op => <option key={op} value={op}>{op}</option>)}
                                            </select>
                                        </div>
                                        <div className="col-span-4">
                                            {renderValueInput(condition)}
                                        </div>
                                        <div className="col-span-1">
                                            <button type="button" onClick={() => handleRemoveCondition(condition.id)} className="p-1.5 text-gray-400 hover:text-red-600 rounded-full hover:bg-red-50"><Trash2 className="w-4 h-4" /></button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                             <button type="button" onClick={handleAddCondition} className="w-full flex items-center justify-center gap-2 mt-3 p-2 text-sm bg-blue-50 text-blue-700 border-2 border-dashed border-blue-200 rounded-lg hover:bg-blue-100">
                                <Plus className="w-4 h-4" />
                                <span>添加条件</span>
                            </button>
                        </div>
                        
                        {/* --- Action (THEN) --- */}
                        <div className="space-y-4 p-4 border rounded-lg bg-white">
                            <h3 className="text-lg font-medium">执行动作 (THEN)</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">动作类型</label>
                                    <select value={action.type} onChange={e => handleActionChange('type', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white">
                                        {Object.entries(actionTypes).map(([key, { label }]) => <option key={key} value={key}>{label}</option>)}
                                    </select>
                                </div>
                                {(() => {
                                    const config = actionTypes[action.type as keyof typeof actionTypes];
                                    if (config.targetType === 'none') {
                                        return null;
                                    }
                                    // At this point, config must have a targetLabel
                                    const labeledConfig = config as { targetLabel: string };
                                    return (
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">{labeledConfig.targetLabel}</label>
                                            {renderActionTargetInput()}
                                        </div>
                                    );
                                })()}
                            </div>
                        </div>

                        {/* --- Notifications --- */}
                        <div className="space-y-4 p-4 border rounded-lg bg-white">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-medium">实时预警与通知</h3>
                                <div className="flex items-center gap-2">
                                    <span className={`text-sm ${notification.enabled ? 'text-green-600' : 'text-gray-500'}`}>
                                        {notification.enabled ? '已启用' : '已禁用'}
                                    </span>
                                    <button
                                        type="button"
                                        onClick={() => handleNotificationChange('enabled', !notification.enabled)}
                                        className={`${
                                            notification.enabled ? 'bg-green-500' : 'bg-gray-300'
                                        } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out`}
                                        role="switch"
                                        aria-checked={notification.enabled}
                                    >
                                        <span
                                            aria-hidden="true"
                                            className={`${
                                                notification.enabled ? 'translate-x-5' : 'translate-x-0'
                                            } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                                        />
                                    </button>
                                </div>
                            </div>

                            {notification.enabled && (
                                <AnimatePresence>
                                <motion.div 
                                    className="space-y-4 pt-4 border-t"
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    exit={{ opacity: 0, height: 0 }}
                                >
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">通知人员</label>
                                        <div>
                                            <div className="flex flex-wrap gap-2 items-center p-2 border border-gray-300 rounded-lg min-h-[42px]">
                                                {(notification.target ? notification.target.split(',').map(u => u.trim()) : []).map(user => (
                                                    <span key={user} className="flex items-center bg-amber-100 text-amber-800 text-sm font-medium px-2.5 py-1 rounded-full">
                                                        {user}
                                                        <button 
                                                            type="button" 
                                                            onClick={() => handleNotificationChange('target', (notification.target ? notification.target.split(',').map(u => u.trim()) : []).filter(u => u !== user).join(','))}
                                                            className="ml-1.5 -mr-1 text-amber-600 hover:text-amber-800"
                                                        >
                                                            <X className="w-3 h-3" />
                                                        </button>
                                                    </span>
                                                ))}
                                            </div>
                                            <button 
                                                type="button" 
                                                onClick={() => setIsNotificationUserModalOpen(true)}
                                                className="mt-2 flex items-center justify-center gap-2 w-full p-2 text-sm bg-gray-100 text-gray-700 border-2 border-transparent rounded-lg hover:bg-gray-200"
                                            >
                                                <Users className="w-4 h-4" />
                                                选择通知人员
                                            </button>
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">自定义通知内容 (可选)</label>
                                        <textarea 
                                            value={notification.message} 
                                            onChange={(e) => handleNotificationChange('message', e.target.value)} 
                                            rows={3} 
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                                            placeholder="例如：检测到高风险对话，请立即介入处理。"
                                        />
                                    </div>
                                </motion.div>
                                </AnimatePresence>
                            )}
                        </div>

                    </div>
                    <div className="flex-shrink-0 px-6 py-4 border-t bg-gray-50 flex justify-end gap-3">
                        <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">取消</button>
                        <button type="submit" className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">保存策略</button>
                    </div>
                </form>
            </motion.div>
             <AnimatePresence>
                {isUserModalOpen && (
                    <UserSelectionModal
                        onClose={() => setIsUserModalOpen(false)}
                        onConfirm={(users) => {
                            handleActionChange('target', users.join(','));
                            setIsUserModalOpen(false);
                        }}
                        initialSelectedUsers={action.target ? action.target.split(',').map(u => u.trim()) : []}
                        multiSelect={actionTypes[action.type as keyof typeof actionTypes].multiSelect}
                    />
                )}
                {isNotificationUserModalOpen && (
                    <UserSelectionModal
                        onClose={() => setIsNotificationUserModalOpen(false)}
                        onConfirm={(users) => {
                            handleNotificationChange('target', users.join(','));
                            setIsNotificationUserModalOpen(false);
                        }}
                        initialSelectedUsers={notification.target ? notification.target.split(',').map(u => u.trim()) : []}
                        multiSelect={true}
                    />
                )}
            </AnimatePresence>
        </motion.div>
    );
}; 