import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X } from 'lucide-react';

interface NavigationProps {
  isCollapsed: boolean;
  onToggleSidebar: () => void;
}

/**
 * 顶部导航栏组件
 * 显示系统名称和主要导航链接
 * 支持侧边栏收缩控制
 */
const Navigation: React.FC<NavigationProps> = ({ isCollapsed, onToggleSidebar }) => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200 flex">
      {/* 左侧logo区域，跟随侧边栏宽度 */}
      <div className={`flex items-center border-r border-gray-200 transition-all duration-300 ${
        isCollapsed ? 'w-16' : 'w-72'
      }`}>
        <div className="w-full px-4">
          <div className="flex items-center justify-between h-16">
            {/* 收缩切换按钮 */}
            <button
              onClick={onToggleSidebar}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              aria-label={isCollapsed ? '展开菜单' : '收缩菜单'}
            >
              {isCollapsed ? (
                <Menu className="w-5 h-5 text-gray-600" />
              ) : (
                <X className="w-5 h-5 text-gray-600" />
              )}
            </button>

            {/* Logo和系统名称 - 收缩时隐藏 */}
            {!isCollapsed && (
              <Link to="/" className="flex items-center space-x-3 ml-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">质</span>
                </div>
                <span className="text-xl font-semibold text-gray-900">
                  智能客服质检系统
                </span>
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className="flex-1">
        <div className="max-w-full mx-auto px-6">
          <div className="flex justify-between items-center h-16">
            {/* 当收缩时，在右侧显示系统名称 */}
            {isCollapsed && (
              <Link to="/" className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">质</span>
                </div>
                <span className="text-xl font-semibold text-gray-900">
                  智能客服质检系统
                </span>
              </Link>
            )}

            {/* 右侧工具栏 */}
            <div className="flex items-center space-x-4 ml-auto">
              {/* <span className="text-sm text-gray-500">
                演示版本 v1.0
              </span> */}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation; 