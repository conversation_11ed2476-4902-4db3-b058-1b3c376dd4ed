import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, EyeIcon } from './icons';
import type { Condition } from '../types';
import { PrerequisiteCondition, ConditionType } from '../types';

interface RuleVisualizationProps {
  conditions: Condition[];
  detectionLogic: string;
}

/**
 * 规则可视化组件
 * 提供条件依赖关系图、逻辑流程图和决策树等可视化功能
 */
export const RuleVisualization: React.FC<RuleVisualizationProps> = ({ 
  conditions, 
  detectionLogic 
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeTab, setActiveTab] = useState<'dependency' | 'flow' | 'tree'>('dependency');

  // 获取条件的前置依赖关系
  const getDependencyMap = () => {
    const dependencyMap: Record<string, string[]> = {};
    conditions.forEach(condition => {
      const prereq = condition.prerequisite;
      if (prereq !== PrerequisiteCondition.NONE) {
        const prereqId = prereq.toLowerCase();
        if (!dependencyMap[prereqId]) {
          dependencyMap[prereqId] = [];
        }
        dependencyMap[prereqId].push(condition.id);
      }
    });
    return dependencyMap;
  };

  // 解析逻辑表达式中的条件关系
  const parseLogicExpression = (logic: string) => {
    // 简单解析逻辑表达式，提取条件组合
    const groups = logic.split('||').map(group => 
      group.replace(/[()]/g, '').split('&&').map(item => item.trim())
    );
    return groups;
  };

  // 渲染条件依赖关系图
  const renderDependencyGraph = () => {
    const dependencyMap = getDependencyMap();
    
    return (
      <div className="bg-white p-4 rounded-lg border">
        <h5 className="font-medium text-gray-800 mb-4">📊 条件依赖关系图</h5>
        <div className="space-y-4">
          {conditions.map(condition => {
            const hasChildren = dependencyMap[condition.id]?.length > 0;
            const prerequisite = condition.prerequisite !== PrerequisiteCondition.NONE 
              ? condition.prerequisite.toLowerCase() 
              : null;
            
            return (
              <div key={condition.id} className="relative">
                {/* 当前条件 */}
                <div className="flex items-center">
                  {prerequisite && (
                    <div className="flex items-center mr-4">
                      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-sm font-bold text-gray-700">
                        {prerequisite}
                      </div>
                      <div className="w-8 h-0.5 bg-gray-400"></div>
                      <div className="w-2 h-2 border-t-2 border-r-2 border-gray-400 rotate-45 -ml-1"></div>
                    </div>
                  )}
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg ${
                    condition.type === ConditionType.KEYWORD ? 'bg-blue-500' : 'bg-purple-500'
                  }`}>
                    {condition.id}
                  </div>
                  <div className="ml-3">
                    <div className="font-medium text-gray-800">{condition.name}</div>
                    <div className="text-xs text-gray-600">
                      {condition.type === ConditionType.KEYWORD ? '关键词检查' : '正则表达式检查'}
                    </div>
                  </div>
                </div>
                
                {/* 子条件连接线 */}
                {hasChildren && (
                  <div className="ml-6 mt-2">
                    <div className="border-l-2 border-gray-300 pl-4 space-y-3">
                      {dependencyMap[condition.id].map(childId => {
                        const childCondition = conditions.find(c => c.id === childId);
                        if (!childCondition) return null;
                        
                        return (
                          <div key={childId} className="flex items-center">
                            <div className="w-2 h-2 border-l-2 border-b-2 border-gray-300 -ml-4 mr-2"></div>
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                              childCondition.type === ConditionType.KEYWORD ? 'bg-blue-400' : 'bg-purple-400'
                            }`}>
                                                              {childId}
                            </div>
                            <div className="ml-2 text-sm text-gray-700">{childCondition.name}</div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // 渲染逻辑流程图
  const renderFlowChart = () => {
    const logicGroups = parseLogicExpression(detectionLogic);
    
    return (
      <div className="bg-white p-4 rounded-lg border">
        <h5 className="font-medium text-gray-800 mb-4">🔄 逻辑流程图</h5>
        <div className="space-y-6">
          <div className="text-center">
            <div className="inline-block bg-green-500 text-white px-4 py-2 rounded-lg font-medium">
              开始检测
            </div>
          </div>
          
          <div className="flex justify-center">
            <div className="w-0.5 h-8 bg-gray-400"></div>
          </div>
          
          {logicGroups.map((group, groupIndex) => (
            <div key={groupIndex} className="relative">
              <div className="flex items-center justify-center">
                <div className="bg-blue-50 border-2 border-blue-300 rounded-lg p-4 min-w-0 flex-1 max-w-md">
                  <div className="text-center font-medium text-blue-800 mb-2">
                    违规路径 {groupIndex + 1}
                  </div>
                  <div className="flex flex-wrap justify-center gap-2">
                    {group.map((condition, condIndex) => {
                      const isNegated = condition.startsWith('!');
                      const conditionId = condition.replace(/[!()]/g, '');
                      const conditionObj = conditions.find(c => c.id === conditionId);
                      
                      return (
                        <React.Fragment key={condIndex}>
                          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                            isNegated 
                              ? 'bg-red-100 text-red-700 border border-red-300' 
                              : 'bg-green-100 text-green-700 border border-green-300'
                          }`}>
                            {isNegated ? '!' : ''}
                            {conditionId}
                            {conditionObj && (
                              <div className="text-xs opacity-75">
                                {conditionObj.name.substring(0, 8)}...
                              </div>
                            )}
                          </div>
                          {condIndex < group.length - 1 && (
                            <div className="flex items-center text-gray-500 text-sm font-bold">
                              AND
                            </div>
                          )}
                        </React.Fragment>
                      );
                    })}
                  </div>
                </div>
              </div>
              
              {groupIndex < logicGroups.length - 1 && (
                <div className="flex justify-center mt-4">
                  <div className="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm font-medium">
                    OR
                  </div>
                </div>
              )}
            </div>
          ))}
          
          <div className="flex justify-center">
            <div className="w-0.5 h-8 bg-gray-400"></div>
          </div>
          
          <div className="text-center">
            <div className="inline-block bg-red-500 text-white px-4 py-2 rounded-lg font-medium">
              触发违规
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染决策树
  const renderDecisionTree = () => {
    return (
      <div className="bg-white p-4 rounded-lg border">
        <h5 className="font-medium text-gray-800 mb-4">🌳 决策树可视化</h5>
        <div className="space-y-4">
          {/* 根节点 */}
          <div className="text-center">
            <div className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-medium shadow-lg">
              检测到信用卡话题 (a)
            </div>
          </div>
          
          {/* 第一层分支 */}
          <div className="flex justify-center space-x-8">
            <div className="text-center">
              <div className="w-0.5 h-8 bg-gray-400 mx-auto"></div>
              <div className="inline-block bg-green-500 text-white px-4 py-2 rounded-lg font-medium">
                客户说"办过" (b)
              </div>
              <div className="w-0.5 h-4 bg-gray-400 mx-auto mt-2"></div>
              <div className="space-y-2">
                <div className="bg-yellow-100 border border-yellow-400 px-3 py-2 rounded text-sm">
                  <div className="font-medium text-yellow-800">询问发卡行额度 (c)?</div>
                  <div className="flex justify-center space-x-4 mt-2">
                    <div className="text-xs">
                      <div className="bg-green-200 px-2 py-1 rounded">是 → 合规</div>
                    </div>
                    <div className="text-xs">
                      <div className="bg-red-200 px-2 py-1 rounded">否 → 违规</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="text-center">
              <div className="w-0.5 h-8 bg-gray-400 mx-auto"></div>
              <div className="inline-block bg-purple-500 text-white px-4 py-2 rounded-lg font-medium">
                询问工作信息 (d)
              </div>
              <div className="w-0.5 h-4 bg-gray-400 mx-auto mt-2"></div>
              <div className="bg-orange-100 border border-orange-400 px-3 py-2 rounded text-sm">
                <div className="font-medium text-orange-800 mb-2">完整收集信息?</div>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center space-x-1">
                    <span className="w-2 h-2 bg-indigo-400 rounded-full"></span>
                    <span>年收入 (e)</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="w-2 h-2 bg-pink-400 rounded-full"></span>
                    <span>税前收入 (f)</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="w-2 h-2 bg-red-400 rounded-full"></span>
                    <span>名下资产 (g)</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                    <span>真实准确 (h)</span>
                  </div>
                </div>
                <div className="mt-2 text-center">
                  <div className="text-xs text-red-700 bg-red-100 px-2 py-1 rounded">
                    缺少 e&f → 违规
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* 底部说明 */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm text-gray-700">
              <div className="font-medium mb-2">决策逻辑说明：</div>
              <div className="space-y-1 text-xs">
                <div>• 路径1：a ✓ && b ✓ && c ✗ → 违规</div>
                <div>• 路径2：a ✓ && d ✓ && (e&f 不完整) && g ✓ && h ✓ → 违规</div>
                <div>• 其他情况：合规</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 shadow">
      <div 
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-2">
          <EyeIcon className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-blue-800">规则可视化</h3>
        </div>
        <button className="flex items-center space-x-1 text-blue-600 hover:text-blue-800">
          <span className="text-sm">{isExpanded ? '收起' : '展开'}</span>
          {isExpanded ? (
            <ChevronUpIcon className="w-4 h-4" />
          ) : (
            <ChevronDownIcon className="w-4 h-4" />
          )}
        </button>
      </div>

      {isExpanded && (
        <div className="mt-4">
          {/* 标签页切换 */}
          <div className="flex space-x-1 mb-4 bg-white rounded-lg p-1">
            <button
              onClick={() => setActiveTab('dependency')}
              className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'dependency'
                  ? 'bg-blue-500 text-white'
                  : 'text-blue-600 hover:bg-blue-50'
              }`}
            >
              依赖关系图
            </button>
            <button
              onClick={() => setActiveTab('flow')}
              className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'flow'
                  ? 'bg-blue-500 text-white'
                  : 'text-blue-600 hover:bg-blue-50'
              }`}
            >
              逻辑流程图
            </button>
            <button
              onClick={() => setActiveTab('tree')}
              className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'tree'
                  ? 'bg-blue-500 text-white'
                  : 'text-blue-600 hover:bg-blue-50'
              }`}
            >
              决策树
            </button>
          </div>

          {/* 内容区域 */}
          <div className="space-y-4">
            {activeTab === 'dependency' && renderDependencyGraph()}
            {activeTab === 'flow' && renderFlowChart()}
            {activeTab === 'tree' && renderDecisionTree()}
          </div>
        </div>
      )}
    </div>
  );
}; 