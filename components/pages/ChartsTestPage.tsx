import React from 'react';
import RealTimeChart from '../charts/RealTimeChart';
import IndustryTrendChart from '../charts/IndustryTrendChart';
import QualityDashboard from '../charts/QualityDashboard';
import RuleAnalysisChart from '../charts/RuleAnalysisChart';

/**
 * 图表测试页面
 * 用于测试所有图表组件的显示效果
 */
const ChartsTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">图表组件测试页面</h1>
          <p className="text-xl text-gray-600">测试所有ECharts图表组件的显示效果</p>
        </div>

        <div className="space-y-12">
          {/* 实时数据趋势图 */}
          <div className="bg-white rounded-3xl p-8 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">实时数据趋势图</h2>
            <div className="bg-gray-50 rounded-2xl p-4">
              <RealTimeChart />
            </div>
          </div>

          {/* 行业趋势图 */}
          <div className="bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 rounded-3xl p-8 shadow-lg">
            <h2 className="text-2xl font-bold text-white mb-6">行业趋势图</h2>
            <div className="bg-white/10 rounded-2xl p-4">
              <IndustryTrendChart />
            </div>
          </div>

          {/* 质检仪表盘 */}
          <div className="bg-white rounded-3xl p-8 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">质检仪表盘</h2>
            <div className="bg-gray-50 rounded-2xl p-4">
              <QualityDashboard />
            </div>
          </div>

          {/* 规则分析饼图 */}
          <div className="bg-white rounded-3xl p-8 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">规则分析饼图</h2>
            <div className="bg-gray-50 rounded-2xl p-4">
              <RuleAnalysisChart />
            </div>
          </div>

          {/* 组合展示 */}
          <div className="grid lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-3xl p-8 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-4">质检趋势</h3>
              <RealTimeChart />
            </div>
            <div className="bg-white rounded-3xl p-8 shadow-lg">
              <h3 className="text-xl font-bold text-gray-900 mb-4">失分分析</h3>
              <RuleAnalysisChart />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChartsTestPage;
