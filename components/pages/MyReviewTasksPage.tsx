import React, { useState, useMemo } from 'react';
import { PageHeader } from '../PageHeader';
import { <PERSON> } from 'react-router-dom';
import { ListFilter, Search, Clock, FileText, ArrowRight, CheckCircle, Clock4, Loader } from 'lucide-react';
import { Tooltip } from '../common/Tooltip';

// --- Data Structures ---
type TaskStatus = 'pending' | 'in-progress' | 'completed';

interface ReviewTask {
  id: string;
  sessionId: string;
  assignedTime: string;
  source: 'Auto-Assigned' | 'Manager-Assigned';
  agentName: string;
  machineScore: number;
  finalScore?: number;
  status: TaskStatus;
  summary?: string;
}

// --- Mock Data ---
const mockTasks: ReviewTask[] = [
  { id: 'T001', sessionId: 'S_A001_01', assignedTime: '2023-10-28 10:00', source: 'Auto-Assigned', agentName: '王伟', machineScore: 72, status: 'pending', summary: '通话中客户情绪激动，可能存在服务态度问题。' },
  { id: 'T002', sessionId: 'S_B012_03', assignedTime: '2023-10-28 09:30', source: 'Manager-Assigned', agentName: '李娜', machineScore: 85, status: 'pending', summary: '涉及金融产品推荐，需核实合规性。' },
  { id: 'T003', sessionId: 'S_G701_04', assignedTime: '2023-10-27 16:00', source: 'Auto-Assigned', agentName: '白龙马', machineScore: 90, finalScore: 88, status: 'completed', summary: '复核发现有轻微违规，已扣分。' },
  { id: 'T004', sessionId: 'S_H801_05', assignedTime: '2023-10-28 11:00', source: 'Auto-Assigned', agentName: '唐三藏', machineScore: 92, status: 'in-progress', summary: '长时间静默，需确认原因。' },
  { id: 'T005', sessionId: 'S_E501_02', assignedTime: '2023-10-26 14:00', source: 'Manager-Assigned', agentName: '猪八戒', machineScore: 88, finalScore: 90, status: 'completed', summary: '申诉成功，分数已修正。' },
];

const statusConfig: Record<TaskStatus, { text: string; color: string; icon: React.ElementType }> = {
  pending: { text: '待处理', color: 'text-orange-500', icon: Clock4 },
  'in-progress': { text: '处理中', color: 'text-blue-500', icon: Loader },
  completed: { text: '已完成', color: 'text-green-600', icon: CheckCircle },
};

// --- Sub-components ---
const TaskCard: React.FC<{ task: ReviewTask }> = ({ task }) => {
    const Icon = statusConfig[task.status].icon;
    return (
        <div className="bg-white p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
                <div>
                    <span className={`text-sm font-semibold flex items-center ${statusConfig[task.status].color}`}>
                        <Icon className={`w-4 h-4 mr-1.5 ${task.status === 'in-progress' ? 'animate-spin' : ''}`} />
                        {statusConfig[task.status].text}
                    </span>
                    <p className="text-lg font-bold text-gray-800 mt-1">复核任务: {task.sessionId}</p>
                </div>
                <div className="text-right">
                    {task.finalScore !== undefined ? (
                        <div className="flex items-baseline justify-end gap-2">
                            <Tooltip text="机审得分"><span className="text-lg text-gray-400 font-medium">{task.machineScore}</span></Tooltip>
                            <ArrowRight className="w-4 h-4 text-gray-400" />
                            <span className="text-2xl font-bold text-green-600">{task.finalScore}</span>
                        </div>
                    ) : (
                        <>
                            <span className="text-xs text-gray-500">机审得分</span>
                            <p className="text-2xl font-bold text-blue-600">{task.machineScore}</p>
                        </>
                    )}
                </div>
            </div>
            {task.summary && (
                <div className="mt-3 text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                    <strong>任务摘要: </strong>{task.summary}
                </div>
            )}
            <div className="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center text-sm text-gray-500">
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1.5">
                        <Tooltip text="坐席"><FileText className="w-4 h-4" /></Tooltip>
                        <span>{task.agentName}</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                        <Tooltip text="分配时间"><Clock className="w-4 h-4" /></Tooltip>
                        <span>{task.assignedTime}</span>
                    </div>
                </div>
                <Link to={`/session-detail/${task.sessionId}`} className="px-4 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
                    开始复核
                </Link>
            </div>
        </div>
    );
};


// --- Main Component ---
export const MyReviewTasksPage: React.FC = () => {
  const [tasks, setTasks] = useState(mockTasks);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const sortedTasks = useMemo(() => {
    return [...tasks].sort((a, b) => {
      // "pending" always comes first
      if (a.status === 'pending' && b.status !== 'pending') return -1;
      if (a.status !== 'pending' && b.status === 'pending') return 1;
      // then sort by time
      return new Date(b.assignedTime).getTime() - new Date(a.assignedTime).getTime();
    });
  }, [tasks]);

  const filteredTasks = useMemo(() => {
    return sortedTasks.filter(task => {
        const statusMatch = filter === 'all' || task.status === filter;
        const searchMatch = searchTerm === '' || task.sessionId.includes(searchTerm) || task.agentName.includes(searchTerm);
        return statusMatch && searchMatch;
    });
  }, [sortedTasks, filter, searchTerm]);

  const taskCounts = useMemo(() => {
      return tasks.reduce((acc, task) => {
          acc.all++;
          acc[task.status]++;
          return acc;
      }, { all: 0, pending: 0, 'in-progress': 0, completed: 0 });
  }, [tasks]);

  return (
    <div className="min-h-screen bg-gray-50 pb-10">
      <PageHeader
        title="我的复核任务"
        description="在这里处理所有分配给您的质检复核任务。"
        badge="质检工作台"
      />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Toolbar */}
        <div className="bg-white p-4 rounded-xl shadow-sm border mb-6 flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center bg-gray-100 rounded-lg">
                 {Object.entries(taskCounts).map(([status, count]) => (
                    <button 
                        key={status} 
                        onClick={() => setFilter(status)}
                        className={`px-4 py-2 text-sm font-semibold rounded-md transition-colors ${
                            filter === status ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-200'
                        }`}
                    >
                        {statusConfig[status as TaskStatus]?.text || '全部'} ({count})
                    </button>
                 ))}
            </div>
            <div className="relative w-full md:w-64">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input 
                    type="text"
                    placeholder="搜索会话ID或坐席"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 outline-none"
                />
            </div>
        </div>

        {/* Task List */}
        <div className="space-y-4">
            {filteredTasks.length > 0 ? (
                filteredTasks.map(task => <TaskCard key={task.id} task={task} />)
            ) : (
                <div className="text-center py-12 bg-white rounded-lg shadow-sm">
                    <p className="text-gray-500">没有找到匹配的任务。</p>
                </div>
            )}
        </div>
      </main>
    </div>
  );
};

export default MyReviewTasksPage; 