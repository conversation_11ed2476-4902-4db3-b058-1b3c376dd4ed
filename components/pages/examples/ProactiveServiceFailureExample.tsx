import React from 'react';
import { HelpCircle, MessageSquare, CheckSquare, XSquare, Search, Ban, ListChecks, ArrowRight, Clock, Sliders, Settings, Radio } from 'lucide-react';

/**
 * 规则配置项展示组件
 */
const ConfigItem: React.FC<{
  icon: React.ElementType;
  label: string;
  value: string | React.ReactNode;
  reason: string;
}> = ({ icon: Icon, label, value, reason }) => (
  <div className="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
    <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
      <Icon className="w-5 h-5 text-gray-600" />
    </div>
    <div className="flex-1">
      <div className="flex justify-between items-center">
        <p className="text-sm font-semibold text-gray-800">{label}</p>
        <div className="text-sm font-mono bg-blue-50 text-blue-800 px-2 py-0.5 rounded">{value}</div>
      </div>
      <p className="mt-1 text-xs text-gray-600 leading-relaxed">
        <span className="font-semibold">配置原因:</span> {reason}
      </p>
    </div>
  </div>
);


/**
 * 综合示例：主动服务缺失场景（使用前置条件和检测范围）
 */
export const ProactiveServiceFailureExample: React.FC = () => {

  return (
    <div className="p-6 bg-gray-50 min-h-full">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">主动服务缺失质检示例</h1>
          <p className="mt-2 text-md text-gray-600">
            演示如何使用 <strong>前置条件</strong> 与 <strong>检测范围</strong> 实现精准监督
          </p>
        </div>

        {/* 场景与逻辑 */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <HelpCircle className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold">业务场景与逻辑规则</h2>
            </div>
            <p className="text-sm text-gray-700 mb-4">
              <strong>场景：</strong> 客户在完成某项业务（如提交材料）后主动询问后续流程，这是体现客服专业性和主动性的关键节点。我们需要检测客服是否给出了有效、清晰的指引。
            </p>
            <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg flex items-center justify-center space-x-4">
                <div className="text-center">
                    <p className="font-semibold text-blue-800">A. 前置条件</p>
                    <p className="text-xs text-gray-600">(客户询问后续流程)</p>
                </div>
                <ArrowRight className="w-8 h-8 text-blue-400" />
                <div className="text-center">
                    <p className="font-semibold text-blue-800">执行检测 !B</p>
                    <p className="text-xs text-gray-600">(客服在后续2句内未告知)</p>
                </div>
            </div>
             <p className="text-center mt-2 text-sm text-gray-600">规则逻辑：<strong>A && !B</strong>。当客户询问后续流程(A)后，如果客服在紧接着的2句话内没有给出有效解答(!B)，则规则命中。</p>
        </div>
        
        {/* 示例对话 */}
        <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-3 flex items-center"><MessageSquare className="w-5 h-5 mr-2 text-gray-500"/>示例对话分析</h2>
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <div className="border-b border-dashed pb-2">
                    <p className="text-sm"><strong>客户：</strong> 你好，我已经按照你们的要求，把所有的申请材料都在线提交了。</p>
                    <p className="text-sm mt-2"><strong>客服：</strong> 好的，女士，我们已经收到了。</p>
                </div>
                <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm"><strong>客户：</strong> <span className="bg-yellow-200 px-1 rounded">那接下来我需要做什么？</span><span className="bg-yellow-200 px-1 rounded ml-1">然后呢？</span></p>
                    <p className="text-xs text-yellow-800 mt-1">
                        <strong>[规则A命中]</strong>: 客户使用了关键词"接下来"和"然后呢"，触发前置条件。
                    </p>
                </div>
                 <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm"><strong>客服：</strong> 好的，请您留意后续的电话或者短信就行。</p>
                     <p className="text-xs text-red-800 mt-1">
                        <strong>[规则!B命中]</strong>: 在前置条件触发后的第一句，客服未使用"审核"、"流程"等有效关键词，回答模糊，未能提供有效信息。由于检测范围是"后续2句内"，且此句已符合"未包含"条件，规则最终命中，判定为服务疏漏。
                    </p>
                </div>
                <div className="mt-2 border-t border-dashed pt-2">
                     <p className="text-sm"><strong>客户：</strong> 好吧，谢谢。</p>
                </div>
            </div>
        </div>

        {/* 详细配置 */}
        <div>
          <h2 className="text-2xl font-bold text-center text-gray-800 mb-6">规则配置详解</h2>
          <div className="space-y-8">
            {/* A. 前置条件 */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="w-8 h-8 rounded-full bg-yellow-100 text-yellow-800 flex items-center justify-center font-bold mr-3">A</span>
                前置条件: 客户询问后续流程
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ConfigItem icon={Search} label="检测角色" value="客户" reason="前置条件是基于客户的发言来触发的，因此检测角色必须是客户。" />
                <ConfigItem icon={HelpCircle} label="前置条件" value="无" reason="此条件是整个逻辑的起点，因此它本身没有更前置的条件。" />
                <ConfigItem icon={Sliders} label="检测范围" value="全文" reason="客户可能在对话的任何部分提出后续流程的疑问，需要检测整个通话过程来捕获这个触发信号。" />
                <ConfigItem icon={Ban} label="关键词" value="接下来, 然后呢, 怎么办" reason="这些是客户在寻求下一步指引时最常用的典型用词。" />
                <ConfigItem icon={Radio} label="分析方式" value="单句分析" reason="客户的询问通常是简短的句子，单句分析能最快、最直接地命中关键词，效率高。" />
                <ConfigItem icon={CheckSquare} label="检测类型" value="包含任意一个" reason="客户可能只说其中一个词，只要包含任意一个，就代表其有询问意图。" />
                <ConfigItem icon={Settings} label="扩展功能" value="默认关闭" reason="此场景下无需'单句话内生效'或'限定命中次数'等复杂功能，默认配置即可满足需求。" />
              </div>
            </div>

            {/* B. 检测项 */}
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="w-8 h-8 rounded-full bg-red-100 text-red-700 flex items-center justify-center font-bold mr-3">!B</span>
                检测项: 客服未提供有效解答
                <span className="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">必须未命中</span>
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ConfigItem icon={Search} label="检测角色" value="客服" reason="我们需要检查的是客服的答复，因此检测角色是客服。" />
                <ConfigItem icon={HelpCircle} label="前置条件" value="客户询问后续流程 (条件A)" reason="我们的目标不是检测所有客服未提供解答的情况，而是只关心'当客户主动问起时，客服未解答'的特定场景。因此，将条件A设置为本检测项的触发前提，可以极大地提升规则的准确性，避免误报。" />
                <ConfigItem icon={Clock} label="检测范围" value="生效范围：后续2句内" reason="这是此规则的核心。它将检测限定在前置条件触发后的2句话内，确保只评估客服的即时反应，如果客服过了很久才想起补充，那也算服务不及时。" />
                <ConfigItem icon={ListChecks} label="关键词" value="审核, 流程, 等待, 通知" reason="这些词汇是客服在解释后续步骤时应该使用的有效关键词。" />
                <ConfigItem icon={Radio} label="分析方式" value="单句分析" reason="客服的解答也应该是清晰的单句，单句分析能有效判断每一句回复是否包含有效信息。" />
                <ConfigItem icon={XSquare} label="检测类型" value="包含任意一个" reason="只要客服的回答中包含了任意一个有效关键词，我们就认为他做出了有效解答。因为我们的规则目标是找出'未解答'的失败场景(!B)，所以当这个'包含任意一个'的条件未命中时，整个规则才会成立。" />
                <ConfigItem icon={Settings} label="扩展功能" value="默认关闭" reason="检测范围已经精确控制了检测时机，无需额外的扩展功能进行限制。" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProactiveServiceFailureExample; 