import React from 'react';
import { 
    Lightbulb, AlertCircle, CheckCircle, ListChecks, ArrowRight, Bot, User, FileText, CheckSquare, Users, ScanText, Combine, Milestone, SlidersHorizontal, Info
} from 'lucide-react';

const CaseTitle = ({ children }: { children: React.ReactNode }) => (
    <h2 className="text-2xl font-bold text-gray-800 border-l-4 border-blue-600 pl-4 mb-6">
        {children}
    </h2>
);

const ConfigItem = ({ icon, label, value, explanation }: { icon: React.ReactNode, label: string, value: React.ReactNode, explanation: string }) => (
    <div className="flex items-start py-4 border-b border-gray-200 last:border-b-0">
        <div className="w-1/4 flex items-center text-sm font-medium text-gray-700 pr-4">
            {icon}
            <span className="ml-2">{label}</span>
        </div>
        <div className="w-1/3 text-sm font-semibold text-blue-800 bg-blue-50 px-3 py-1 rounded-md">
            {value}
        </div>
        <div className="w-5/12 text-xs text-gray-600 pl-6 italic flex items-start">
             <Info size={14} className="mr-2 mt-0.5 flex-shrink-0 text-gray-400" />
            <span>{explanation}</span>
        </div>
    </div>
);

const KeywordsDisplay = ({ keywords }: { keywords: string[] }) => (
    <div className="flex flex-wrap gap-2">
        {keywords.map(kw => (
            <span key={kw} className="inline-block bg-blue-100 text-blue-700 text-xs font-medium px-2 py-1 rounded-full">{kw}</span>
        ))}
    </div>
);

const DialogueBox = ({ speaker, text, highlight = false, speakerType }: { speaker: string, text: string, highlight?: boolean, speakerType: 'bot' | 'user' }) => (
    <div className={`flex items-start space-x-3 ${highlight ? 'p-3 bg-yellow-50 rounded-lg ring-2 ring-yellow-300' : ''}`}>
        {speakerType === 'bot' ? 
            <Bot className="w-8 h-8 text-blue-600 flex-shrink-0" /> :
            <User className="w-8 h-8 text-green-600 flex-shrink-0" />
        }
        <div>
            <p className="font-semibold text-gray-700">{speaker}</p>
            <p className="text-gray-600">{text}</p>
        </div>
    </div>
);

export const CustomerDissatisfactionExample: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200 mb-6 -mx-6 -mt-6">
        <div className="max-w-full mx-auto px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-900">综合示例：客户不满未道歉</h1>
          <p className="text-gray-600 mt-1">
            一个经典的质检场景，用于检测客服在面对客户负面情绪时，是否尽到了安抚义务。
          </p>
        </div>
      </div>

      <div className="max-w-full mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左侧主要内容区 */}
        <div className="lg:col-span-2 space-y-8">
          
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <CaseTitle>业务场景与规则拆解</CaseTitle>
            <p className="mb-6 text-gray-700">
                在服务中，当客户明确表达了不满或投诉意图时，客服人员应首先表示歉意以安抚客户情绪。此规则旨在捕获那些客户表达不满后，客服在随后的对话中**未**使用标准道歉话术的情况。
            </p>
            <div className="space-y-4">
                <div className="flex items-start p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <ListChecks className="w-6 h-6 text-blue-600 mr-4" />
                    <div>
                        <h3 className="font-semibold text-blue-800">[条件 A] 客户表达不满</h3>
                        <p className="text-sm text-blue-700">使用【关键词检查】算子，检测客户是否提及"投诉"、"不满意"、"太差了"等词语。</p>
                    </div>
                </div>
                <div className="flex items-start p-4 bg-green-50 border border-green-200 rounded-lg">
                    <ListChecks className="w-6 h-6 text-green-600 mr-4" />
                    <div>
                        <h3 className="font-semibold text-green-800">[条件 B] 客服进行道歉</h3>
                        <p className="text-sm text-green-700">使用【关键词检查】算子，检测客服是否提及"抱歉"、"对不起"等词语。</p>
                    </div>
                </div>
                <div className="flex items-center justify-center p-4">
                    <p className="text-lg font-bold text-gray-700 font-mono">
                        最终逻辑: <span className="text-red-600">A && !B</span>
                    </p>
                </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <CaseTitle>规则配置详情</CaseTitle>
            <div className="space-y-8">
                {/* Condition A Config */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">[条件 A] 客户表达不满</h3>
                    <div className="divide-y divide-gray-200">
                         <ConfigItem 
                            icon={<Users size={16} className="text-gray-500" />}
                            label="检测角色"
                            value={<span className="flex items-center"><User size={16} className="mr-2 text-green-600"/> 客户</span>}
                            explanation="我们的目标是捕捉客户的情绪，因此必须将检测对象限定为'客户'。"
                        />
                         <ConfigItem 
                            icon={<Milestone size={16} className="text-gray-500" />}
                            label="前置条件"
                            value="无"
                            explanation="这是整个逻辑链的起点，是触发事件，因此它本身没有前置条件。"
                        />
                         <ConfigItem 
                            icon={<ScanText size={16} className="text-gray-500" />}
                            label="检测范围"
                            value="全文"
                            explanation="客户可能在对话的任何阶段表达不满，所以需要覆盖整个对话过程。"
                        />
                         <ConfigItem 
                            icon={<FileText size={16} className="text-gray-500" />}
                            label="关键词"
                            value={<KeywordsDisplay keywords={["投诉", "不满意", "太差了", "搞什么", "怎么回事"]} />}
                            explanation="这些词汇是客户表达负面情绪的直接信号，是判断不满的关键依据。"
                        />
                         <ConfigItem 
                            icon={<Combine size={16} className="text-gray-500" />}
                            label="分析方式"
                            value="单句分析"
                            explanation="不满情绪通常在单句话内就能完整表达，无需合并多个句子进行分析。"
                        />
                         <ConfigItem 
                            icon={<CheckSquare size={16} className="text-gray-500" />}
                            label="检测类型"
                            value="包含任意一个关键词"
                            explanation="只要客户说出任何一个不满关键词，就应该被视为一个有效的信号，因此使用'任意'逻辑。"
                        />
                         <ConfigItem 
                            icon={<SlidersHorizontal size={16} className="text-gray-500" />}
                            label="扩展功能"
                            value="无"
                            explanation="对于基础的情绪触发检测，不需要启用'单句话内生效'或'限定命中次数'等高级功能。"
                        />
                    </div>
                </div>
                {/* Condition B Config */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-5">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">[条件 B] 客服进行道歉</h3>
                     <div className="divide-y divide-gray-200">
                        <ConfigItem 
                            icon={<Users size={16} className="text-gray-500" />}
                            label="检测角色"
                            value={<span className="flex items-center"><Bot size={16} className="mr-2 text-blue-600"/> 客服</span>}
                            explanation="我们需要判断客服是否履行了安抚义务，因此检测对象必须是'客服'。"
                        />
                        <ConfigItem 
                            icon={<Milestone size={16} className="text-gray-500" />}
                            label="前置条件"
                            value="无"
                            explanation="道歉检测本身是独立的事件。它与'客户不满'的逻辑关系是在最终规则组合(A && !B)中定义的，而非在此条件内部。"
                        />
                        <ConfigItem 
                            icon={<ScanText size={16} className="text-gray-500" />}
                            label="检测范围"
                            value="全文"
                            explanation="客服可能在客户抱怨后的任何时间点道歉，检测全文能确保不遗漏。"
                        />
                        <ConfigItem 
                            icon={<FileText size={16} className="text-gray-500" />}
                            label="关键词"
                            value={<KeywordsDisplay keywords={["抱歉", "对不起", "不好意思", "非常遗憾", "十分抱歉"]} />}
                            explanation="这些是标准的道歉用语，是判断客服是否道歉的直接证据。"
                        />
                        <ConfigItem 
                            icon={<Combine size={16} className="text-gray-500" />}
                            label="分析方式"
                            value="单句分析"
                            explanation="与抱怨类似，道歉行为也通常在单句话内完成，适合使用单句分析。"
                        />
                        <ConfigItem 
                            icon={<CheckSquare size={16} className="text-gray-500" />}
                            label="检测类型"
                            value="包含任意一个关键词"
                            explanation="只要客服说出任何一个道歉词，即可认为他尽到了安抚义务，使用'任意'逻辑最合理。"
                        />
                        <ConfigItem 
                            icon={<SlidersHorizontal size={16} className="text-gray-500" />}
                            label="扩展功能"
                            value="无"
                            explanation="基础的道歉行为检测，同样不需要复杂的扩展功能。"
                        />
                    </div>
                </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <CaseTitle>实战演练</CaseTitle>
            
            {/* 正例：命中规则 */}
            <div className="mb-8">
                <div className="flex items-center mb-4">
                    <AlertCircle className="w-6 h-6 text-red-500 mr-3" />
                    <h3 className="text-xl font-semibold text-gray-800">规则命中示例 (客服未道歉)</h3>
                </div>
                <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
                    <DialogueBox speaker="客户" text="你们这个产品太差了，我要投诉！" highlight={true} speakerType="user" />
                    <DialogueBox speaker="客服" text="先生您具体是遇到了什么问题呢？" highlight={true} speakerType="bot" />
                </div>
                <p className="mt-3 text-sm text-gray-600">
                    <span className="font-bold text-red-600">分析</span>：客户命中了【条件A】，并且客服在后续对话中未能命中【条件B】，因此规则 `A && !B` 成立，判定为**命中**。
                </p>
            </div>

            {/* 反例：未命中规则 */}
            <div>
                 <div className="flex items-center mb-4">
                    <CheckCircle className="w-6 h-6 text-green-500 mr-3" />
                    <h3 className="text-xl font-semibold text-gray-800">规则未命中示例 (客服已道歉)</h3>
                </div>
                <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
                    <DialogueBox speaker="客户" text="你们这个产品太差了，我要投诉！" highlight={true} speakerType="user" />
                    <DialogueBox speaker="客服" text="先生非常抱歉给您带来了不好的体验，您具体是遇到了什么问题呢？" highlight={true} speakerType="bot" />
                </div>
                <p className="mt-3 text-sm text-gray-600">
                     <span className="font-bold text-green-600">分析</span>：客户命中了【条件A】，但客服也命中了【条件B】，因此 `!B` 为假，规则 `A && !B` 不成立，判定为**未命中**。
                </p>
            </div>
          </div>

        </div>
        {/* 右侧学习要点 */}
        <div className="lg:col-span-1">
          <div className="sticky top-6 bg-white p-6 rounded-lg shadow-sm border">
             <div className="flex items-center mb-4">
                <Lightbulb className="w-6 h-6 text-yellow-500 mr-3" />
                <h2 className="text-xl font-semibold text-gray-800">本例学习要点</h2>
            </div>
            <ul className="space-y-4 text-gray-700">
                <li className="flex items-start">
                    <ArrowRight className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                    <span>
                        <span className="font-semibold">逻辑非 `!` 的运用</span>:
                        `!B` 表示 "条件B不成立"。这是检测"应做某事但未做"类场景的核心。
                    </span>
                </li>
                 <li className="flex items-start">
                    <ArrowRight className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                    <span>
                        <span className="font-semibold">跨角色条件组合</span>:
                        规则可以由不同角色的条件组合而成，如本例中条件A检测客户，条件B检测客服。
                    </span>
                </li>
                 <li className="flex items-start">
                    <ArrowRight className="w-5 h-5 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                    <span>
                        <span className="font-semibold">上下文关联</span>:
                        通过 `A && !B` 的逻辑，我们将两个独立的事件（客户抱怨、客服道歉）在上下文中关联起来，实现了更智能的判断。
                    </span>
                </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerDissatisfactionExample; 