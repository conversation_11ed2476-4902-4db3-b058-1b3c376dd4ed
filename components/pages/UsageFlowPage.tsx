import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { PageHeader } from '../PageHeader';
import mermaid from 'mermaid';

// Mermaid Diagram Component
const MermaidDiagram: React.FC<{ chart: string }> = ({ chart }) => {
    useEffect(() => {
        mermaid.initialize({ startOnLoad: true, theme: 'neutral' });
        mermaid.run();
    }, []);

    return (
        <div className="mermaid">
            {chart}
        </div>
    );
};

export const UsageFlowPage: React.FC = () => {
    const mermaidChart = `
graph TD
    A("创建规则") --> B("创建方案");
    
    B --> C{质检方式};
    C --> D["数据集质检 (测试/验证)"];
    C --> E["呼叫中心任务质检 (生产环境)"];

    subgraph "方式一：数据集质检"
        D --> D1("手动上传录音文件<br/>创建数据集");
        D1 --> D2("使用数据集，选择质检方案<br/>发起质检任务");
    end

    subgraph "方式二：呼叫中心任务质检"
        E --> F{任务类型};
        F --> G["离线语音质检"];
        F --> H["离线文本质检"];
        F --> I["实时文本质检"];

        G --> G1("创建离线语音质检任务<br/>绑定质检方案");
        G1 --> G2("使用SDK上传<br/>语音文件地址");
        
        H --> H1("创建离线文本质检任务<br/>绑定质检方案");
        H1 --> H2("使用SDK上传<br/>完整对话内容");

        I --> I1("创建实时文本质检任务<br/>绑定质检方案");
        I1 --> I2("使用SDK实时上传<br/>当前轮对话内容");
        I2 --> I3("对话结束后<br/>使用SDK修改通话状态");
    end

    subgraph "通用后续流程"
        D2 --> J("质检完成后, 自动/手动分配任务<br/>质检员进行人工复核");
        G2 --> J;
        H2 --> J;
        I3 --> J;
        J --> K("客服可查询结果并发起申诉");
        K --> L("管理员查看统计数据");
    end
    
    classDef main fill:#e3f2fd,stroke:#90caf9,stroke-width:2px,color:#0d47a1;
    class A,B,J,K,L main;

    classDef choice fill:#e8f5e9,stroke:#a5d6a7,stroke-width:2px,color:#1b5e20;
    class C,E,F choice;

    classDef action fill:#fffde7,stroke:#ffd54f,stroke-width:1px,color:#212121;
    class D,D1,D2,G,H,I,G1,G2,H1,H2,I1,I2,I3 action;
`;

    return (
        <div className="min-h-screen bg-gray-50">
            <PageHeader
                title="系统使用流程"
                description="从规则配置到结果分析，可视化全链路质检流程。"
                badge="使用流程"
                tag="流程图"
            />
            <main className="p-6 md:p-10 flex justify-center items-start">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="p-8 bg-white rounded-xl shadow-lg border border-gray-200 w-full max-w-7xl overflow-auto"
                >
                    <div className="text-center mb-6">
                        <h2 className="text-2xl font-bold text-gray-800">质检系统完整工作流</h2>
                        <p className="text-gray-500 mt-2">下图展示了从规则创建到数据分析的完整业务闭环。</p>
                    </div>
                    <MermaidDiagram chart={mermaidChart} />
                </motion.div>
            </main>
        </div>
    );
}; 