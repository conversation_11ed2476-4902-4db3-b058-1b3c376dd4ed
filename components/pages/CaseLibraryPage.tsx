import React, { useState, useMemo, ForwardRefExoticComponent, RefAttributes } from 'react';
import { Link } from 'react-router-dom';
import { PageHeader } from '../PageHeader';
import { ConfirmationModal } from '../common/ConfirmationModal';
import { CaseFormDrawer, CaseFormData } from '../common/CaseFormDrawer';
import { motion } from 'framer-motion';
import { Search, Tag, ShieldCheck, Heart, Zap, BrainCircuit, BookOpen, ChevronDown, Plus, Edit, Trash2, ListChecks, Target, LucideProps } from 'lucide-react';

type CaseType = 'excellent' | 'violation' | 'pending';

interface Case {
  id: number;
  type: CaseType;
  title: string;
  description: string;
  tags: string[];
  icon: ForwardRefExoticComponent<Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>>;
  iconColor: string;
  config: { label: string; value: string; }[];
  dialogue: { speaker: '客服' | '客户'; text: string; highlight?: boolean; }[];
  analysis: string;
}

const initialCases: Case[] = [
    {
      id: 1, type: 'excellent', title: '主动服务意识超群', description: '客户问题解决后，坐席主动询问有无其他帮助，并根据客户历史订单推荐了相关优惠活动，极大提升了客户满意度。',
      tags: ['服务质量', '客户满意度'], icon: Zap, iconColor: 'text-yellow-500',
      config: [ { label: '核心规则', value: '未检测到负面关键词 + 对话结束语检测' }, { label: '学习要点', value: '在服务结束后，使用开放性问题"还有什么可以帮您"而非封闭性问题"还有其他问题吗"。' } ],
      dialogue: [ { speaker: '客服', text: '好的，您的问题已经解决了。请问还有什么可以帮您吗？' }, { speaker: '客户', text: '没有了，谢谢。' }, { speaker: '客服', text: '不客气，看到您之前购买过我们的A型号产品，最近我们有升级版A+的优惠活动，附赠延保服务，请问您感兴趣吗？', highlight: true }, ],
      analysis: '该坐席表现出色的主动服务意识，不仅解决了当前问题，还通过个性化推荐尝试深挖客户价值，是提升客户忠诚度的优秀范例。'
    },
    {
      id: 2, type: 'excellent', title: '高效精准的问题定位', description: '面对客户模糊的技术问题描述，坐席通过几个关键问题快速定位到故障点，并给出了清晰的解决方案。',
      tags: ['专业能力', '运营效率'], icon: BrainCircuit, iconColor: 'text-purple-500',
      config: [ { label: '核心规则', value: '知识库匹配 + 对话轮次检测' }, { label: '学习要点', value: '面对模糊问题，采用"缩小范围"的提问策略，如"请问您是在操作哪个菜单时遇到的问题？"、"报错提示是什么？"。' } ],
      dialogue: [ { speaker: '客户', text: '你们的软件怎么又出问题了，打不开啊！' }, { speaker: '客服', text: '您好，您先别着急，为了快速帮您定位问题，请问您是点击桌面图标后没有反应，还是登录时提示错误呢？', highlight: true }, { speaker: '客户', text: '是登录的时候提示密码错误，但我没改过密码。' }, { speaker: '客服', text: '明白了，这个情况通常是…' }, ],
      analysis: '面对客户的抱怨，坐席没有纠结于情绪，而是立刻进入专业的问题诊断流程，通过二分法提问快速缩小了问题范围，体现了极高的专业素养。'
    },
    {
      id: 3, type: 'violation', title: '客户不满情绪未安抚', description: '识别客户在对话中表达不满或愤怒情绪，并检测坐席是否在后续交流中使用了标准的安抚话术。',
      tags: ['服务质量', '情绪检测'], icon: Heart, iconColor: 'text-red-500',
      config: [ { label: '核心规则', value: '关键词检测 + 正则表达式' }, { label: '检测目标', value: '客户说出"不满意"、"生气"、"投诉"等词语后，检测客服是否在3句话内回应"抱歉"、"对不起"、"您先别生气"等。' } ],
      dialogue: [ { speaker: '客户', text: '你们这个产品怎么回事啊，用了几天就坏了，我很生气！', highlight: true }, { speaker: '客服', text: '您好，请问您具体遇到什么问题了呢？' }, { speaker: '客户', text: '我不是说了吗，就是坏了！我要投诉！', highlight: true }, { speaker: '客服', text: '好的女士，您先提供一下您的订单号可以吗？' }, { speaker: '客户', text: '真是麻烦！' }, ],
      analysis: '客服在客户明确表达不满和投诉意图后，未能第一时间进行有效安抚，而是直接进入流程询问，可能导致客户情绪进一步激化。这是典型的服务意识缺失。'
    },
    {
      id: 4, type: 'violation', title: '金融产品销售合规性检查', description: '确保坐席在介绍金融产品时，完整、准确地披露了所有必要的风险提示和免责声明。',
      tags: ['销售合规', '金融风控'], icon: ShieldCheck, iconColor: 'text-green-500',
      config: [ { label: '核心规则', value: '文本相似度匹配 + 关键词检测' }, { label: '检测目标', value: '坐席必须完整说出标准风险提示话术，且不得缺少"投资有风险"、"请谨慎选择"等关键词。' } ],
      dialogue: [ { speaker: '客服', text: '这款理财产品年化收益率高达5%，非常不错的。' }, { speaker: '客户', text: '听起来是不错，有什么风险吗？' }, { speaker: '客服', text: '所有理财都有一定风险，但我们这款是很稳健的。您要现在就办理吗？', highlight: true }, ],
      analysis: '坐席在客户问及风险时，使用了模糊的、诱导性的回答（"很稳健的"），并试图跳过标准的风险披露流程，直接引导客户购买，属于严重销售违规。'
    },
    {
      id: 5, type: 'pending', title: '多次询问相同问题', description: '客户在一次通话中反复询问关于产品保修政策的同一个问题，可能表明坐席的解释不够清晰或标准话术存在缺陷。',
      tags: ['话术分析', '待优化'], icon: BookOpen, iconColor: 'text-gray-500',
      config: [ { label: '核心规则', value: '上下文重复检查 + 语义相似度分析' }, { label: '分析方向', value: '检查坐席的解释是否存在专业术语过多、逻辑不清的问题。评估标准话术是否需要优化，以更通俗易懂的方式呈现。' } ],
      dialogue: [ { speaker: '客户', text: '这个保修具体都包括什么？' }, { speaker: '客服', text: '我们提供的是两年期的非人为损坏的硬件保修。' }, { speaker: '客户', text: '那要是我屏幕碎了，算人为的吗？' }, { speaker: '客服', text: '是的，物理损坏属于人为损坏。' }, { speaker: '客户', text: '那我还是没太懂，到底什么算非人为的？', highlight: true }, ],
      analysis: '坐席的回答虽然准确，但过于专业和笼统。面对客户追问，未能使用具体举例的方式（如"比如无法开机、电池无法充电等属于非人为损坏"）来阐明，导致沟通效率低下。此案例可用于优化保修政策的标准解答话术。'
    },
];

const allTags = Array.from(new Set(initialCases.flatMap(c => c.tags)));

const libraryTypes = {
    excellent: { label: '优秀服务案例库', description: '学习优秀实践，提升服务品质。' },
    violation: { label: '典型违规案例库', description: '知悉违规红线，规避服务风险。' },
    pending: { label: '待分析问题池', description: '复盘疑难问题，持续优化改进。' },
}

const CaseCard = ({ caseData, onEdit, onDelete }: { caseData: Case, onEdit: () => void, onDelete: () => void }) => {
    const { id, title, description, tags, icon: Icon, iconColor } = caseData;
    const { icon, ...serializableCaseData } = caseData;

    return (
        <motion.div
            layout
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-300 border border-gray-100 flex flex-col group"
        >
            <div className="p-6 flex-grow">
                <div className="flex items-start justify-between">
                    <h3 className="text-lg font-bold text-gray-800 mb-2 pr-4">{title}</h3>
                    <div className="flex-shrink-0">
                        <Icon className={`w-8 h-8 ${iconColor}`} />
                    </div>
                </div>
                <p className="text-sm text-gray-600 mb-4 h-20">{description}</p>
                <div className="flex flex-wrap gap-2">
                    {tags.map(tag => (
                        <div key={tag} className="flex items-center text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">
                           <Tag size={12} className="mr-1.5" />
                           {tag}
                        </div>
                    ))}
                </div>
            </div>
            <div className="bg-gray-50 px-6 py-3 rounded-b-xl mt-auto flex justify-between items-center">
                <Link to={`/cases/${id}`} state={{ caseData: serializableCaseData }} className="flex items-center font-semibold text-blue-600 hover:text-blue-700 transition-colors">
                    <BookOpen size={16} className="mr-2" />
                    学习此案例
                </Link>
                <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button onClick={onEdit} className="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-gray-200 rounded-full"><Edit size={16}/></button>
                    <button onClick={onDelete} className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-gray-200 rounded-full"><Trash2 size={16}/></button>
                </div>
            </div>
        </motion.div>
    );
};

export const CaseLibraryPage = () => {
    const [cases, setCases] = useState(initialCases);
    const [activeLibrary, setActiveLibrary] = useState<CaseType>('excellent');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedTags, setSelectedTags] = useState<string[]>([]);
    const [isDeleteModalOpen, setDeleteModalOpen] = useState(false);
    const [caseToDelete, setCaseToDelete] = useState<Case | null>(null);
    const [isDrawerOpen, setIsDrawerOpen] = useState(false);
    const [editingCase, setEditingCase] = useState<Case | null>(null);

    const filteredCases = useMemo(() => {
        return cases
            .filter(c => c.type === activeLibrary)
            .filter(c => c.title.toLowerCase().includes(searchTerm.toLowerCase()))
            .filter(c => selectedTags.length === 0 || selectedTags.every(tag => c.tags.includes(tag)));
    }, [cases, activeLibrary, searchTerm, selectedTags]);

    const toggleTag = (tag: string) => {
        setSelectedTags(prev => 
            prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]
        );
    };

    const handleDeleteClick = (caseItem: Case) => {
        setCaseToDelete(caseItem);
        setDeleteModalOpen(true);
    };

    const confirmDelete = () => {
        if (caseToDelete) {
            setCases(prevCases => prevCases.filter(c => c.id !== caseToDelete.id));
        }
        setDeleteModalOpen(false);
        setCaseToDelete(null);
        setIsDrawerOpen(true);
    };

    const handleAddNewCase = () => {
        setEditingCase(null);
        setIsDrawerOpen(true);
    };

    const handleEditCase = (caseItem: Case) => {
        setEditingCase(caseItem);
        setIsDrawerOpen(true);
    };

    const caseTypeDetails = {
        excellent: { icon: Zap, iconColor: 'text-yellow-500' },
        violation: { icon: Heart, iconColor: 'text-red-500' },
        pending: { icon: BookOpen, iconColor: 'text-gray-500' },
    };

    const handleSaveCase = (dataFromForm: CaseFormData) => {
        const details = caseTypeDetails[dataFromForm.type];
        if (dataFromForm.id) { // Edit
            setCases(cases.map(c => 
                c.id === dataFromForm.id 
                ? { ...c, ...dataFromForm, ...details } 
                : c
            ));
        } else { // Create
            const newCase = {
                ...dataFromForm,
                id: Date.now(),
                ...details,
            };
            setCases([newCase, ...cases]);
        }
        setIsDrawerOpen(false);
    };

    return (
        <div className="bg-gray-50 min-h-full">
            <PageHeader
                title={libraryTypes[activeLibrary].label}
                description={libraryTypes[activeLibrary].description}
                actions={
                    <button 
                        onClick={handleAddNewCase}
                        className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                    >
                        <Plus size={18} className="mr-2" />
                        新建案例
                    </button>
                }
            />
            <main className="p-6 md:p-10 max-w-7xl mx-auto">
                <div className="mb-8">
                    <div className="flex justify-between items-center mb-4">
                        <div className="flex-grow max-w-md">
                            <div className="relative">
                                <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="搜索案例名称..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-12 pr-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                                />
                            </div>
                        </div>
                        <div className="flex items-center gap-4">
                            {Object.keys(libraryTypes).map(type => (
                                <button
                                    key={type}
                                    onClick={() => setActiveLibrary(type as CaseType)}
                                    className={`px-5 py-2 text-sm font-semibold rounded-md transition-colors ${
                                        activeLibrary === type ? 'bg-white text-blue-600 shadow' : 'text-gray-500 hover:text-gray-800'
                                    }`}
                                >
                                    {libraryTypes[type as CaseType].label}
                                </button>
                            ))}
                        </div>
                    </div>
                    <div className="flex flex-wrap items-center gap-2">
                        <span className="text-sm font-medium text-gray-700 mr-2">标签筛选:</span>
                        {allTags.map(tag => (
                             <button
                                key={tag}
                                onClick={() => toggleTag(tag)}
                                className={`px-4 py-1.5 text-sm font-semibold rounded-full border transition-colors duration-200 ${
                                    selectedTags.includes(tag)
                                        ? 'bg-blue-600 text-white border-blue-600'
                                        : 'bg-white text-gray-600 hover:bg-gray-100 border-gray-300'
                                }`}
                            >
                                {tag}
                            </button>
                        ))}
                    </div>
                </div>

                <motion.div layout className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {filteredCases.map(c => (
                        <CaseCard 
                            key={c.id} 
                            caseData={c}
                            onEdit={() => handleEditCase(c)}
                            onDelete={() => handleDeleteClick(c)}
                        />
                    ))}
                </motion.div>
                {filteredCases.length === 0 && (
                     <div className="text-center py-16">
                        <p className="text-gray-500">此分类下未找到匹配的案例。</p>
                     </div>
                )}
            </main>
            <ConfirmationModal
                isOpen={isDeleteModalOpen}
                onClose={() => setDeleteModalOpen(false)}
                onConfirm={confirmDelete}
                title="确认删除案例"
                actionText="确认删除"
                actionColor="red"
            >
                <p className="text-sm text-gray-500">
                    您确定要删除案例 "{caseToDelete?.title}" 吗？此操作无法撤销。
                </p>
            </ConfirmationModal>
            <CaseFormDrawer
                isOpen={isDrawerOpen}
                onClose={() => setIsDrawerOpen(false)}
                onSave={handleSaveCase}
                caseData={editingCase}
            />
        </div>
    );
};

export default CaseLibraryPage; 