import React, { useState, useRef, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { PageHeader } from '../PageHeader';
import {
    User, HardDrive, Phone, Calendar, Clock, AlertTriangle, Play, Pause, <PERSON>wind, <PERSON>For<PERSON>, UserCircle, Bot,
    Smile, Frown, MicOff, MessageSquare, Target, Sparkles, ChevronLeft
} from 'lucide-react';
import { motion } from 'framer-motion';
import { Tooltip } from '../common/Tooltip';

// --- Types ---
interface Alert {
    type: 'sentiment' | 'keyword';
    message: string;
    timestamp: number;
    severity: 'high' | 'medium' | 'low';
}

interface TranscriptItem {
    speaker: 'agent' | 'customer';
    text: string;
    timestamp: number;
    sentiment?: 'neutral' | 'negative';
    keyword?: string;
    silence_start?: boolean;
    silence_end?: boolean;
}

type EventType = 'sentiment_negative' | 'keyword' | 'silence';

interface SessionEvent {
    type: EventType;
    timestamp: number;
    duration?: number;
}

interface SessionData {
    id: string;
    agent: { id: string; name: string };
    customer: { id: string; phone: string };
    startTime: string;
    duration: number;
    machineScore?: number;
    alerts: Alert[];
    transcript: TranscriptItem[];
}

// --- Mock Data ---

const mockSessionData: SessionData = {
    id: 'S_A001_01',
    agent: { id: 'A001', name: '王伟' },
    customer: { id: 'C_12345', phone: '138****1234' },
    startTime: '2023-10-27 14:30:05',
    duration: 215, // seconds
    machineScore: 72,
    alerts: [
        { type: 'sentiment', message: '客户情绪激动，评分为-0.8', timestamp: 125, severity: 'high' },
        { type: 'keyword', message: '客户提及关键词 "投诉"', timestamp: 142, severity: 'medium' },
    ],
    transcript: [
        { speaker: 'agent', text: '您好，这里是xx银行贷后服务中心，请问是王先生吗？', timestamp: 5 },
        { speaker: 'customer', text: '是我，你们怎么又打电话来了？我都说了好几遍了，下个月才还得起！', timestamp: 15 },
        { speaker: 'agent', text: '王先生您别激动，我们只是按照规定进行提醒，没有催促您的意思。', timestamp: 28 },
        { speaker: 'customer', text: '别跟我说这些！每次都一样的话术！你们这样天天骚扰，我受不了了！', timestamp: 45, sentiment: 'neutral' },
        { speaker: 'agent', text: '真的很抱歉给您带来了不好的感受。我们也是希望能够帮助您...', timestamp: 62 },
        { speaker: 'customer', text: '帮助？你们就是催债！烦死了！再打电话过来，我就要去银监会投诉你们！', timestamp: 125, sentiment: 'negative' },
        { speaker: 'agent', text: '您先消消气，我们绝对没有骚扰您的意思。关于您的困难，我们可以一起看看有没有别的解决办法。', timestamp: 138 },
        { speaker: 'customer', text: '别说了，我不想听！总之，下个月之前别再打了！', timestamp: 142, keyword: '投诉' },
        { speaker: 'agent', text: '好的好的，王先生，我们尊重您的意见。那我们下个月再联系您。', timestamp: 160 },
        { speaker: 'customer', text: '嗯。', timestamp: 175, silence_start: true },
        { speaker: 'agent', text: '那祝您生活愉快，再见。', timestamp: 200, silence_end: true },
    ],
};

const EventTag: React.FC<{item: TranscriptItem}> = ({ item }) => {
    if (item.sentiment === 'negative') {
        return <div className="mt-2 flex items-center text-xs text-red-600 bg-red-100 px-2 py-1 rounded-full w-fit"><Frown className="w-4 h-4 mr-1.5" />负面情绪</div>;
    }
    if (item.keyword) {
        return <div className="mt-2 flex items-center text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full w-fit"><Target className="w-4 h-4 mr-1.5" />关键词: {item.keyword}</div>;
    }
    if (item.silence_start) {
        return <div className="mt-2 flex items-center text-xs text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full w-fit"><MicOff className="w-4 h-4 mr-1.5" />长时静默</div>;
    }
    return null;
}

// --- Sub-components ---

interface AudioPlayerProps {
    duration: number;
    currentTime: number;
    setCurrentTime: React.Dispatch<React.SetStateAction<number>>;
    isPlaying: boolean;
    setIsPlaying: React.Dispatch<React.SetStateAction<boolean>>;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ duration, currentTime, setCurrentTime, isPlaying, setIsPlaying }) => {
    const progressRef = useRef<HTMLDivElement>(null);

    const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (!progressRef.current) return;
        const rect = progressRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const newTime = (x / rect.width) * duration;
        setCurrentTime(newTime);
    };

    return (
        <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-center gap-6">
                <button className="text-gray-600 hover:text-black"><Rewind /></button>
                <button onClick={() => setIsPlaying(!isPlaying)} className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center shadow-lg">
                    {isPlaying ? <Pause size={32} /> : <Play size={32} className="ml-1" />}
                </button>
                <button className="text-gray-600 hover:text-black"><FastForward /></button>
            </div>
            <div className="flex items-center gap-4 mt-4 text-sm">
                <span className="font-mono text-gray-600">{new Date(currentTime * 1000).toISOString().substr(14, 5)}</span>
                <div ref={progressRef} onClick={handleProgressClick} className="w-full h-2 bg-gray-200 rounded-full cursor-pointer">
                    <motion.div
                        className="h-2 bg-blue-500 rounded-full"
                        style={{ width: `${(currentTime / duration) * 100}%` }}
                    />
                </div>
                <span className="font-mono text-gray-600">{new Date(duration * 1000).toISOString().substr(14, 5)}</span>
            </div>
        </div>
    );
};

interface TranscriptPanelProps {
    transcript: TranscriptItem[];
    currentTime: number;
    setCurrentTime: React.Dispatch<React.SetStateAction<number>>;
    isPlaying: boolean;
}

const TranscriptPanel: React.FC<TranscriptPanelProps> = ({ transcript, currentTime, setCurrentTime, isPlaying }) => {
    const activeRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (activeRef.current) {
            activeRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, [currentTime]);

    return (
        <div className="h-[60vh] overflow-y-auto pr-4 space-y-4">
            {transcript.map((item, i) => {
                const isAgent = item.speaker === 'agent';
                const isActive = currentTime >= item.timestamp && (transcript[i + 1] ? currentTime < transcript[i + 1].timestamp : true);

                return (
                    <div
                        key={i}
                        ref={isActive ? activeRef : null}
                        onClick={() => setCurrentTime(item.timestamp)}
                        className={`flex gap-3 cursor-pointer p-3 rounded-lg transition-all duration-300 ${isActive ? 'bg-blue-100' : 'hover:bg-gray-50'}`}
                    >
                        {isAgent ? <Bot className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" /> : <UserCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />}
                        <div className="w-full">
                            <div className="flex justify-between items-center">
                                <span className={`font-semibold ${isAgent ? 'text-blue-700' : 'text-green-700'}`}>{isAgent ? '坐席' : '客户'}</span>
                                <span className="text-xs text-gray-500 font-mono">{new Date(item.timestamp * 1000).toISOString().substr(14, 5)}</span>
                            </div>
                            <p className="mt-1 text-gray-800">{item.text}</p>
                            <EventTag item={item} />
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

interface ReviewPanelProps {
    machineScore?: number;
}

const ReviewPanel: React.FC<ReviewPanelProps> = ({ machineScore }) => {
  const [finalScore, setFinalScore] = useState('');
  const [reviewNotes, setReviewNotes] = useState('');

  const handleSubmit = () => {
    if (!finalScore) {
        alert('请输入最终得分');
        return;
    }
    alert(`提交成功!\n最终得分: ${finalScore}\n复核备注: ${reviewNotes}`);
    // Here you would typically call an API to submit the review
  }

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <h2 className="text-xl font-bold text-gray-800 mb-4">复核操作</h2>
      <div className="space-y-4">
        {machineScore && (
            <div className="mb-2">
                <label className="text-sm font-medium text-gray-500">机审得分</label>
                <div className="mt-1 p-3 bg-gray-100 rounded-lg text-lg font-bold text-gray-800">
                    {machineScore}
                </div>
            </div>
        )}
        <div>
          <label className="text-sm font-medium text-gray-700" htmlFor="final-score">最终得分</label>
          <input
            id="final-score"
            type="number"
            value={finalScore}
            onChange={(e) => setFinalScore(e.target.value)}
            placeholder="输入最终得分 (0-100)"
            className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        <div>
          <label className="text-sm font-medium text-gray-700" htmlFor="review-notes">复核备注</label>
          <textarea
            id="review-notes"
            value={reviewNotes}
            onChange={(e) => setReviewNotes(e.target.value)}
            rows={4}
            placeholder="输入详细的复核意见或备注..."
            className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        <div className="flex justify-end gap-3 pt-2">
            <button className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                暂存
            </button>
            <button onClick={handleSubmit} className="px-4 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
                提交复核结果
            </button>
        </div>
      </div>
    </div>
  );
}

// --- Main Component ---
const SessionDetailPage: React.FC = () => {
    const { sessionId } = useParams<{ sessionId: string }>();
    const [session, setSession] = useState<SessionData>(mockSessionData);
    const [currentTime, setCurrentTime] = useState(0);
    const [isPlaying, setIsPlaying] = useState(false);

    useEffect(() => {
        let interval: number;
        if (isPlaying) {
            interval = window.setInterval(() => {
                setCurrentTime(prevTime => {
                    if (prevTime >= session.duration) {
                        setIsPlaying(false);
                        return session.duration;
                    }
                    return prevTime + 1;
                });
            }, 1000);
        }
        return () => window.clearInterval(interval);
    }, [isPlaying, session.duration]);

    return (
        <div className="min-h-screen bg-gray-50/50">
            <PageHeader
                title="会话详情"
                description={`查看坐席 ${session.agent.name} 与客户的完整交互过程。`}
                badge="质检工作台"
                breadcrumbs={[
                    { name: '坐席实时监控', path: '/qa-workbench/agent-monitoring' },
                    { name: '会话详情', path: `/qa-management/sessions/${sessionId}` },
                ]}
            />
            <main className="p-6 md:p-10">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left/Main Column */}
                    <div className="lg:col-span-2 bg-white p-6 rounded-xl shadow-sm border border-gray-100 flex flex-col">
                        <h2 className="text-xl font-bold text-gray-800 mb-2">实时语音转写</h2>
                        <p className="text-sm text-gray-500 mb-4">点击任意对话可定位播放</p>
                        
                        <div className="flex-grow overflow-hidden">
                            <TranscriptPanel
                                transcript={session.transcript}
                                currentTime={currentTime}
                                setCurrentTime={setCurrentTime}
                                isPlaying={isPlaying}
                            />
                        </div>
                    </div>

                    {/* Right Column */}
                    <div className="space-y-8">
                        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                            <h2 className="text-xl font-bold text-gray-800 mb-4">录音播放</h2>
                            <AudioPlayer duration={session.duration} currentTime={currentTime} setCurrentTime={setCurrentTime} isPlaying={isPlaying} setIsPlaying={setIsPlaying} />
                        </div>
                        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                            <h2 className="text-xl font-bold text-gray-800 mb-4">会话信息</h2>
                            <ul className="space-y-3 text-sm">
                                <li className="flex items-center"><User className="w-4 h-4 mr-3 text-gray-500" />坐席: <span className="font-medium ml-2">{session.agent.name}</span></li>
                                <li className="flex items-center"><Phone className="w-4 h-4 mr-3 text-gray-500" />客户号码: <span className="font-medium ml-2">{session.customer.phone}</span></li>
                                <li className="flex items-center"><Calendar className="w-4 h-4 mr-3 text-gray-500" />开始时间: <span className="font-medium ml-2">{session.startTime}</span></li>
                                <li className="flex items-center"><Clock className="w-4 h-4 mr-3 text-gray-500" />通话时长: <span className="font-medium ml-2">{session.duration}s</span></li>
                            </ul>
                        </div>
                        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                            <h2 className="text-xl font-bold text-gray-800 mb-4">详细预警分析</h2>
                            {session.alerts.length > 0 ? (
                                <ul className="space-y-3">
                                    {session.alerts.map((alert, i) => (
                                        <li key={i} className="flex items-start text-sm p-2 rounded-lg cursor-pointer hover:bg-gray-100" onClick={() => setCurrentTime(alert.timestamp)}>
                                            <AlertTriangle className="w-5 h-5 mr-3 text-red-500 flex-shrink-0 mt-0.5" />
                                            <div>
                                                <span className="font-semibold text-gray-800">{alert.type === 'sentiment' ? '情绪预警' : '关键词预警'}</span>
                                                <p className="text-gray-600">{alert.message}</p>
                                                <p className="text-xs text-gray-400">发生于 {alert.timestamp}s</p>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            ) : (
                                <p className="text-sm text-gray-500">本次会话无激活预警。</p>
                            )}
                        </div>
                        <ReviewPanel machineScore={session.machineScore} />
                    </div>
                </div>
            </main>
        </div>
    );
};

export default SessionDetailPage; 