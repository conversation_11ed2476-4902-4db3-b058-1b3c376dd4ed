import React, { useState } from 'react';
import { UnifiedPageHeader } from './final-design/components/UnifiedPageHeader';
import { Bell, Check, Filter, AlertTriangle, ListChecks } from 'lucide-react';

// --- Types and Mock Data ---

type NotificationType = 'realtime_alert' | 'review_assignment';
type NotificationStatus = 'read' | 'unread';

interface Notification {
    id: string;
    type: NotificationType;
    status: NotificationStatus;
    title: string;
    content: string;
    timestamp: Date;
    link?: string;
}

const initialNotifications: Notification[] = [
    {
        id: 'N-001',
        type: 'realtime_alert',
        status: 'unread',
        title: '实时监控通知: 客户情绪激动',
        content: '实时监控方案 [客户负面情绪实时监控] 已触发，会话ID: SESS_B9Y4L3N0P2。已生成高优预警。',
        timestamp: new Date(Date.now() - 2 * 60 * 1000),
        link: '/qa-workbench/real-time-alerts'
    },
    {
        id: 'N-002',
        type: 'review_assignment',
        status: 'unread',
        title: '复核策略通知: 低分录音已分配',
        content: '根据策略 [核心业务流程质检]，一条得分45分的录音已自动分配给质检员 [李莉] 复核。',
        timestamp: new Date(Date.now() - 10 * 60 * 1000),
        link: '/qa-management/task-management'
    },
    {
        id: 'N-003',
        type: 'realtime_alert',
        status: 'read',
        title: '实时监控通知: 合规风险',
        content: '实时监控方案 [金融销售合规实时监控] 已触发，会话ID: SESS_C0Z5M4O1Q3。',
        timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000),
        link: '/qa-workbench/real-time-alerts'
    },
    {
        id: 'N-004',
        type: 'review_assignment',
        status: 'read',
        title: '复核策略通知: 优秀案例已推送',
        content: '根据策略 [优秀服务案例发现]，一条得分98分的录音已自动推送至案例库。',
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
    },
];

const typeConfig: Record<NotificationType, { text: string, icon: React.ElementType, color: string }> = {
    realtime_alert: { text: '实时监控', icon: AlertTriangle, color: 'text-orange-500' },
    review_assignment: { text: '复核分配', icon: ListChecks, color: 'text-blue-500' },
};

// --- Main Component ---

export const NotificationCenterPage: React.FC = () => {
    const [notifications, setNotifications] = useState<Notification[]>(initialNotifications);
    const [filterType, setFilterType] = useState<NotificationType | 'all'>('all');
    const [filterStatus, setFilterStatus] = useState<NotificationStatus | 'all'>('all');

    const handleMarkAsRead = (id: string) => {
        setNotifications(notifications.map(n => n.id === id ? { ...n, status: 'read' } : n));
    };
    
    const handleMarkAllAsRead = () => {
        setNotifications(notifications.map(n => ({...n, status: 'read'})));
    };

    const filteredNotifications = notifications.filter(n => 
        (filterType === 'all' || n.type === filterType) &&
        (filterStatus === 'all' || n.status === filterStatus)
    );
    
    const TimeAgo = ({ date }: { date: Date }) => {
        const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
        if (seconds < 60) return <span>{seconds}秒前</span>;
        const minutes = Math.floor(seconds / 60);
        if (minutes < 60) return <span>{minutes}分钟前</span>;
        const hours = Math.floor(minutes / 60);
        return <span>{hours}小时前</span>;
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <UnifiedPageHeader
                title="通知中心"
                subtitle="聚合所有知会类消息，助您随时掌握系统动态。"
                icon={Bell}
                badge={{ text: "工作台", color: "blue" }}
                actions={[
                    {
                        label: "全部标记为已读",
                        onClick: handleMarkAllAsRead,
                        icon: Check,
                        variant: "outline"
                    }
                ]}
            />
            <main className="p-6 md:p-10 max-w-5xl mx-auto">
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                    {/* Filters */}
                    <div className="flex justify-between items-center mb-6 pb-4 border-b">
                        <h2 className="text-xl font-bold text-gray-800">通知列表</h2>
                        <div className="flex items-center gap-4">
                            {/* Filter by Type */}
                             <div className="flex items-center gap-2">
                                <Filter className="w-4 h-4 text-gray-500" />
                                {(['all', 'realtime_alert', 'review_assignment'] as const).map(type => (
                                    <button
                                        key={type}
                                        onClick={() => setFilterType(type)}
                                        className={`px-3 py-1 text-sm font-semibold rounded-full transition-colors ${
                                            filterType === type ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-200'
                                        }`}
                                    >
                                        {type === 'all' ? '全部类型' : typeConfig[type].text}
                                    </button>
                                ))}
                            </div>
                             {/* Filter by Status */}
                             <div className="flex items-center gap-2">
                                {(['all', 'unread', 'read'] as const).map(status => (
                                    <button
                                        key={status}
                                        onClick={() => setFilterStatus(status)}
                                        className={`px-3 py-1 text-sm font-semibold rounded-full transition-colors ${
                                            filterStatus === status ? 'bg-green-600 text-white' : 'text-gray-600 hover:bg-gray-200'
                                        }`}
                                    >
                                        {status === 'all' ? '全部状态' : status === 'unread' ? '未读' : '已读'}
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                    
                    {/* Notification List */}
                    <div className="space-y-4">
                        {filteredNotifications.map(notification => (
                            <div key={notification.id} className={`p-4 rounded-lg flex items-start gap-4 transition-colors ${notification.status === 'unread' ? 'bg-blue-50/50 hover:bg-blue-100/60' : 'bg-gray-50 hover:bg-gray-100'}`}>
                                <div className={`mt-1 p-2 rounded-full ${notification.status === 'unread' ? 'bg-blue-100' : 'bg-gray-200'}`}>
                                    {React.createElement(typeConfig[notification.type].icon, { className: `w-5 h-5 ${typeConfig[notification.type].color}`})}
                                </div>
                                <div className="flex-grow">
                                    <div className="flex justify-between items-center">
                                        <h3 className="font-bold text-gray-800">{notification.title}</h3>
                                        <span className="text-xs text-gray-500"><TimeAgo date={notification.timestamp} /></span>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-1">{notification.content}</p>
                                </div>
                                 {notification.status === 'unread' && (
                                    <button onClick={() => handleMarkAsRead(notification.id)} className="text-sm font-semibold text-blue-600 px-3 py-1 rounded-md hover:bg-blue-100 self-center">
                                        标记已读
                                    </button>
                                )}
                            </div>
                        ))}
                        {filteredNotifications.length === 0 && (
                            <div className="text-center py-12 text-gray-500">
                                <Bell className="w-10 h-10 mx-auto mb-3 text-gray-400" />
                                <p>暂无符合条件的通知</p>
                            </div>
                        )}
                    </div>
                </div>
            </main>
        </div>
    );
};