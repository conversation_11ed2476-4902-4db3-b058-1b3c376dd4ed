import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import RealTimeChart from '../charts/RealTimeChart';
import IndustryTrendChart from '../charts/IndustryTrendChart';
import QualityDashboard from '../charts/QualityDashboard';
import RuleAnalysisChart from '../charts/RuleAnalysisChart';
import {
  Users,
  Settings,
  BarChart3,
  Shield,
  CheckCircle,
  TrendingUp,
  Zap,
  Brain,
  ArrowRight,
  Sparkles,
  Target,
  Award,
  Clock,
  Eye,
  AlertTriangle,
  Lightbulb,
  Star,
  Quote,
  Building2,
  Rocket,
  Globe,
  MessageSquare,
  ThumbsUp,
  Activity,
  Gauge
} from 'lucide-react';

// 核心功能模块数据
const coreModules = [
  {
    name: "质检工作台",
    description: "打破传统质检的信息壁垒，为不同角色提供专属的协同工作平台。坐席可透明查看质检结果并发起申诉，复核员高效处理复核任务，管理者实时掌控团队状态，实现全员参与的质检流程。",
    icon: Users,
    bgColor: "bg-gradient-to-br from-blue-500 to-blue-600",
    hoverColor: "hover:from-blue-600 hover:to-blue-700",
    textColor: "text-white",
    borderColor: "border-blue-200",
    shadowColor: "shadow-blue-500/25",
    features: ["我的复核任务", "申诉处理流程", "质检成绩透明化", "实时通知中心"],
    link: "/final-design/my-review-tasks",
    stats: "100% 流程透明化"
  },
  {
    name: "质检管理",
    description: "构建从标准定义到任务执行的全流程闭环管理体系。通过词库、规则、方案的层级化配置，结合智能化的计划任务管理，实现质检标准的统一化和执行的自动化。",
    icon: Settings,
    bgColor: "bg-gradient-to-br from-emerald-500 to-emerald-600",
    hoverColor: "hover:from-emerald-600 hover:to-emerald-700",
    textColor: "text-white",
    borderColor: "border-emerald-200",
    shadowColor: "shadow-emerald-500/25",
    features: ["词库标准化管理", "规则智能配置", "方案灵活组合", "计划自动执行"],
    link: "/final-design/rule-library",
    stats: "1000+ 规则库"
  },
  {
    name: "统计分析",
    description: "将海量质检数据转化为深度商业洞察，提供从宏观到微观的多维度分析视图。帮助管理者精准定位问题根源，量化评估改进效果，实现从经验管理到数据驱动决策的转变。",
    icon: BarChart3,
    bgColor: "bg-gradient-to-br from-purple-500 to-purple-600",
    hoverColor: "hover:from-purple-600 hover:to-purple-700",
    textColor: "text-white",
    borderColor: "border-purple-200",
    shadowColor: "shadow-purple-500/25",
    features: ["运营全景总览", "质量深度分析", "复核效能分析", "申诉洞察报告"],
    link: "/final-design/quality-operation-overview",
    stats: "多维度数据洞察"
  },
  {
    name: "系统管理",
    description: "构建AI驱动的智能质检基础设施，支持多种数据源接入、语音识别引擎和大语言模型的统一管理。为质检系统提供强大的技术底座和智能化能力支撑。",
    icon: Shield,
    bgColor: "bg-gradient-to-br from-orange-500 to-orange-600",
    hoverColor: "hover:from-orange-600 hover:to-orange-700",
    textColor: "text-white",
    borderColor: "border-orange-200",
    shadowColor: "shadow-orange-500/25",
    features: ["多源数据接入", "AI引擎管理", "语音识别配置", "预警策略设置"],
    link: "/final-design/data-source-management",
    stats: "AI智能驱动"
  }
];

// 用户角色视角
const userRoles = [
  {
    icon: Users,
    title: '坐席员视角',
    description: '从被动接受审判到主动参与质检。透明查看质检详情，理解每一分扣分原因，通过正式申诉渠道表达异议，将质检从"监视工具"转变为"成长助手"。',
    link: '/final-design/agent-homepage',
    bgGradient: 'from-blue-50 to-indigo-50',
    iconColor: 'text-blue-600',
    borderColor: 'border-blue-200',
    hoverBorder: 'hover:border-blue-300'
  },
  {
    icon: Target,
    title: '班组长视角',
    description: '从凭感觉管理到数据驱动决策。精准识别团队短板和个人优劣势，基于客观数据进行针对性辅导，让团队管理从"经验主义"升级为"科学管理"。',
    link: '/final-design/team-leader-homepage',
    bgGradient: 'from-emerald-50 to-green-50',
    iconColor: 'text-emerald-600',
    borderColor: 'border-emerald-200',
    hoverBorder: 'hover:border-emerald-300'
  },
  {
    icon: CheckCircle,
    title: '复核员视角',
    description: '从简单审核员到AI校准专家。在统一高效的界面中精准复核，每一次纠错都成为优化AI模型的宝贵数据，实现人机协同的质检新模式。',
    link: '/final-design/reviewer-homepage',
    bgGradient: 'from-purple-50 to-violet-50',
    iconColor: 'text-purple-600',
    borderColor: 'border-purple-200',
    hoverBorder: 'hover:border-purple-300'
  },
  {
    icon: Award,
    title: '质检主管视角',
    description: '从经验决策到数据驱动战略。拥有全景式数据驾驶舱，设计质检体系规则，分析深度业务洞察，实现从"管理者"到"战略设计师"的角色升级。',
    link: '/final-design/supervisor-homepage',
    bgGradient: 'from-orange-50 to-amber-50',
    iconColor: 'text-orange-600',
    borderColor: 'border-orange-200',
    hoverBorder: 'hover:border-orange-300'
  }
];

// 系统核心价值数据
const coreValues = [
  {
    title: "100%全量AI质检",
    description: "彻底告别传统1%-5%的抽检模式，通过AI引擎实现100%全量覆盖。统一客观的质检标准消除人工主观偏差，将质检周期从数天压缩至分钟级，确保每个客户交互都被纳入监控范围。",
    icon: Brain,
    link: "/final-design/quality-operation-overview",
    gradient: "from-cyan-500 to-blue-600",
    stats: "100% 全量覆盖"
  },
  {
    title: "全流程闭环管理",
    description: "构建AI初检→人工复核→坐席申诉→最终裁定的完整闭环流程。每个环节相互连接、相互促进，实现从计划定义到执行优化的PDCA管理循环，确保质检体系的持续改进。",
    icon: Sparkles,
    link: "/final-design/my-review-tasks",
    gradient: "from-violet-500 to-purple-600",
    stats: "闭环式管理"
  },
  {
    title: "数据驱动决策",
    description: "将海量非结构化通话数据转化为结构化商业洞察，为各层级决策提供客观依据。从坐席个人改进到主管战略规划，实现管理模式从经验直觉向科学数据的根本转变。",
    icon: TrendingUp,
    link: "/final-design/quality-operation-overview",
    gradient: "from-emerald-500 to-teal-600",
    stats: "科学化决策"
  },
  {
    title: "实时风险干预",
    description: "变传统的事后补救为事中主动干预。实时监控通话风险信号，秒级预警高危事件，赋能管理者进行即时介入，将潜在的客户流失和合规风险扼杀在摇篮中。",
    icon: Zap,
    link: "/final-design/real-time-alert-center",
    gradient: "from-orange-500 to-red-600",
    stats: "秒级风险预警"
  }
];

// 传统vs智能对比数据
const comparisonData = [
  {
    aspect: "质检覆盖率",
    traditional: { value: "1-5%", desc: "人工抽检，大量盲区", icon: Eye, color: "text-red-500" },
    intelligent: { value: "100%", desc: "AI全量质检，无遗漏", icon: CheckCircle, color: "text-green-500" }
  },
  {
    aspect: "反馈时效",
    traditional: { value: "3-7天", desc: "人工处理，周期长", icon: Clock, color: "text-red-500" },
    intelligent: { value: "分钟级", desc: "实时分析，即时反馈", icon: Zap, color: "text-green-500" }
  },
  {
    aspect: "质检标准",
    traditional: { value: "主观判断", desc: "人员差异，标准不一", icon: AlertTriangle, color: "text-red-500" },
    intelligent: { value: "客观统一", desc: "AI标准，公平公正", icon: Shield, color: "text-green-500" }
  },
  {
    aspect: "成本投入",
    traditional: { value: "高人力成本", desc: "大量质检员，成本高", icon: TrendingUp, color: "text-red-500" },
    intelligent: { value: "智能化运营", desc: "AI驱动，成本可控", icon: Brain, color: "text-green-500" }
  }
];

// 客户成功案例数据
const successCases = [
  {
    company: "某大型银行客服中心",
    industry: "金融服务",
    icon: Building2,
    challenge: "日均通话量10万+，传统质检覆盖率不足2%，客户投诉处理不及时",
    solution: "部署智能质检系统，实现100%全量质检和实时预警",
    results: [
      { metric: "质检覆盖率", improvement: "从2%提升至100%" },
      { metric: "客户满意度", improvement: "提升35%" },
      { metric: "质检效率", improvement: "提升500%" }
    ],
    bgGradient: "from-blue-50 to-indigo-100",
    borderColor: "border-blue-200"
  },
  {
    company: "某知名电商客服团队",
    industry: "电子商务",
    icon: Globe,
    challenge: "促销期间通话激增，质检人员不足，服务质量难以保障",
    solution: "引入AI质检+人工复核模式，建立实时预警机制",
    results: [
      { metric: "处理时效", improvement: "从7天缩短至1小时" },
      { metric: "服务质量", improvement: "合格率提升40%" },
      { metric: "运营成本", improvement: "降低60%" }
    ],
    bgGradient: "from-emerald-50 to-green-100",
    borderColor: "border-emerald-200"
  }
];

// 实时数据展示
const realTimeData = [
  { label: "今日质检量", value: "12,847", trend: "+15%", icon: Activity, color: "text-blue-600" },
  { label: "平均质检分", value: "87.3", trend: "+2.1", icon: Gauge, color: "text-green-600" },
  { label: "实时预警", value: "3", trend: "-67%", icon: AlertTriangle, color: "text-orange-600" },
  { label: "申诉处理", value: "98.5%", trend: "+5%", icon: ThumbsUp, color: "text-purple-600" }
];

// 客户证言数据
const testimonials = [
  {
    name: "张经理",
    role: "质检主管",
    company: "某保险公司",
    avatar: "👨‍💼",
    content: "系统上线3个月，我们的质检效率提升了500%，更重要的是，数据驱动的决策让我们的服务质量有了质的飞跃。",
    rating: 5
  },
  {
    name: "李组长",
    role: "班组长",
    company: "某通信运营商",
    avatar: "👩‍💼",
    content: "以前凭感觉管理团队，现在有了精准的数据支撑。每个坐席的优劣势一目了然，辅导更有针对性。",
    rating: 5
  },
  {
    name: "王坐席",
    role: "客服代表",
    company: "某互联网公司",
    avatar: "👨‍💻",
    content: "质检结果透明公正，申诉流程规范高效。现在我把质检当作成长的助手，而不是监督的工具。",
    rating: 5
  }
];

// 行业洞察数据
const industryInsights = [
  {
    title: "客服行业数字化转型加速",
    description: "85%的企业计划在未来2年内引入AI质检技术",
    icon: Rocket,
    stat: "85%",
    trend: "上升",
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  },
  {
    title: "质检效率需求迫切",
    description: "传统质检模式已无法满足现代客服中心的规模需求",
    icon: TrendingUp,
    stat: "300%",
    trend: "增长",
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    title: "客户体验要求提升",
    description: "客户对服务质量的期望值持续提高，质检标准需要更加严格",
    icon: ThumbsUp,
    stat: "96%",
    trend: "期望值",
    color: "text-purple-600",
    bgColor: "bg-purple-50"
  }
];

/**
 * 智能客服质检系统首页
 * 展示系统核心价值、功能模块和用户角色视角
 */
const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100">
        {/* 增强的动态背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-96 h-96 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse"></div>
          <div className="absolute top-20 right-10 w-80 h-80 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-gradient-to-r from-indigo-400 to-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse" style={{animationDelay: '4s'}}></div>

          {/* 添加网格背景 */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:60px_60px]"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 py-24 sm:py-32 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center"
          >
            {/* 添加标签 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-blue-200/50 shadow-lg mb-8"
            >
              <Sparkles className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">AI驱动的智能质检平台</span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.2 }}
              className="text-5xl md:text-7xl font-extrabold tracking-tight mb-8"
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
                智能客服质检系统
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mt-8 text-xl md:text-2xl max-w-4xl mx-auto text-gray-600 leading-relaxed font-light"
            >
              解决传统质检<span className="font-semibold text-red-700 bg-red-50 px-2 py-1 rounded-md">覆盖率低、标准不一、反馈滞后</span>的核心痛点
              <br className="hidden sm:block" />
              构建<span className="font-semibold text-blue-700 bg-blue-50 px-2 py-1 rounded-md">AI驱动、全员协同、数据决策</span>的智能质检新范式
            </motion.p>
            
            {/* 统计数据展示 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            >
              {[
                { label: "质检覆盖率", value: "100%", icon: CheckCircle },
                { label: "反馈时效提升", value: "1000x", icon: TrendingUp },
                { label: "管理效率提升", value: "300%+", icon: Award }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full mb-3">
                    <stat.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</div>
                  <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="mt-16 flex flex-col sm:flex-row justify-center items-center gap-6"
            >
              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/quality-operation-overview"
                  className="group relative inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden"
                >
                  <span className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  <BarChart3 className="w-5 h-5 relative z-10" />
                  <span className="relative z-10">查看运营总览</span>
                  <ArrowRight className="w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/supervisor-homepage"
                  className="group inline-flex items-center gap-3 px-8 py-4 bg-white/90 backdrop-blur-sm text-gray-700 font-semibold rounded-2xl border-2 border-gray-200 shadow-lg hover:shadow-xl hover:border-blue-300 hover:text-blue-700 transition-all duration-300"
                >
                  <Users className="w-5 h-5 group-hover:text-blue-600 transition-colors duration-300" />
                  <span>体验系统功能</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 group-hover:text-blue-600 transition-all duration-300" />
                </Link>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Core Modules Section */}
      <div className="py-24 sm:py-32 relative">
        {/* 增强的背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-b from-white via-slate-50/80 to-white"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-1/4 w-64 h-64 bg-blue-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40"></div>
          <div className="absolute bottom-20 right-1/4 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200/50 mb-6"
            >
              <Settings className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">核心功能模块</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800">
                全流程质检解决方案
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              从标准定义到执行分析的全流程闭环管理，从坐席个人到主管决策的多角色协同，
              <br className="hidden sm:block" />
              构建AI驱动的智能质检管理生态系统
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {coreModules.map((module, index) => (
              <motion.div
                key={module.name}
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.6,
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  y: -12,
                  scale: 1.03,
                  transition: { duration: 0.3 }
                }}
                className={`group relative overflow-hidden rounded-3xl border-2 ${module.borderColor} p-8 transition-all duration-500 hover:shadow-2xl ${module.shadowColor} bg-white/90 backdrop-blur-sm`}
              >
                {/* 增强的背景效果 */}
                <div className={`absolute -top-12 -right-12 w-40 h-40 ${module.bgColor} rounded-full opacity-10 transition-all duration-700 group-hover:scale-[8] group-hover:opacity-20 blur-2xl`}></div>
                <div className={`absolute -bottom-6 -left-6 w-24 h-24 ${module.bgColor} rounded-full opacity-5 transition-all duration-700 group-hover:scale-[4] group-hover:opacity-15 blur-xl`}></div>

                {/* 光晕效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

                <div className="relative z-10">
                  <div className="flex items-start justify-between mb-6">
                    <motion.div
                      className={`w-16 h-16 ${module.bgColor} ${module.hoverColor} ${module.textColor} rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300`}
                      whileHover={{
                        rotate: [0, -15, 15, 0],
                        scale: 1.15
                      }}
                      transition={{ duration: 0.6 }}
                    >
                      <module.icon className="w-8 h-8" />
                    </motion.div>

                    <div className="text-right">
                      <div className="text-xs font-medium text-gray-500 mb-1">统计数据</div>
                      <div className="text-sm font-bold text-gray-700">{module.stats}</div>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors duration-300">
                    {module.name}
                  </h3>

                  <p className="text-gray-600 mb-6 leading-relaxed text-base">
                    {module.description}
                  </p>

                  <div className="flex flex-wrap gap-2 mb-8">
                    {module.features.map((feature, featureIndex) => (
                      <motion.span
                        key={feature}
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ delay: featureIndex * 0.1 }}
                        className="text-xs font-medium bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 px-3 py-2 rounded-full hover:from-blue-100 hover:to-purple-100 hover:text-blue-700 transition-all duration-300 cursor-default border border-slate-200/50"
                      >
                        {feature}
                      </motion.span>
                    ))}
                  </div>

                  <Link
                    to={module.link}
                    className="group/link inline-flex items-center gap-3 font-semibold text-base text-blue-600 hover:text-purple-600 transition-colors duration-300 bg-blue-50 hover:bg-purple-50 px-4 py-2 rounded-xl"
                  >
                    <span>查看详情</span>
                    <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* User Roles Section */}
      <div className="py-24 sm:py-32 relative overflow-hidden">
        {/* 增强的动态背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-80 h-80 bg-gradient-to-r from-blue-200 to-cyan-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute -bottom-8 left-20 w-64 h-64 bg-gradient-to-r from-indigo-200 to-blue-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse" style={{animationDelay: '4s'}}></div>

          {/* 添加装饰性网格 */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(99,102,241,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(99,102,241,0.03)_1px,transparent_1px)] bg-[size:40px_40px]"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-50 to-pink-50 rounded-full border border-purple-200/50 mb-6"
            >
              <Users className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-700">多角色协同</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
                全员参与质检流程
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              打破传统质检的角色壁垒和信息不对称，让每个角色都从"被管理者"转变为"价值创造者"，
              <br className="hidden sm:block" />
              构建透明、公正、高效的质检协同生态
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {userRoles.map((role, index) => (
              <motion.div
                key={role.title}
                initial={{ opacity: 0, y: 50, rotateY: -15 }}
                whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.7,
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 80
                }}
                whileHover={{
                  y: -15,
                  scale: 1.05,
                  transition: { duration: 0.3 }
                }}
                className={`group bg-gradient-to-br ${role.bgGradient} backdrop-blur-sm rounded-3xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border-2 ${role.borderColor} ${role.hoverBorder} relative overflow-hidden`}
              >
                {/* 增强的卡片光效 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                <div className={`absolute -top-4 -right-4 w-32 h-32 bg-gradient-to-br ${role.bgGradient} rounded-full opacity-20 transition-all duration-500 group-hover:scale-125 group-hover:opacity-30 blur-2xl`}></div>

                <Link to={role.link} className="block relative z-10">
                  <div className="text-center">
                    <motion.div
                      className={`w-16 h-16 bg-white ${role.iconColor} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg border-2 ${role.borderColor}`}
                      whileHover={{
                        rotate: [0, -15, 15, 0],
                        scale: 1.15
                      }}
                      transition={{ duration: 0.6 }}
                    >
                      <role.icon className="w-8 h-8" />
                    </motion.div>

                    <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-blue-700 transition-colors duration-300">
                      {role.title}
                    </h3>

                    <p className="text-gray-600 text-sm leading-relaxed min-h-[72px] mb-6">
                      {role.description}
                    </p>

                    <motion.div
                      className={`inline-flex items-center gap-2 font-semibold text-sm ${role.iconColor} bg-white/80 px-4 py-2 rounded-xl border ${role.borderColor} group-hover:bg-white transition-all duration-300`}
                      whileHover={{ scale: 1.05 }}
                    >
                      <span>进入工作台</span>
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </motion.div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Core Values Section */}
      <div className="py-24 sm:py-32 relative overflow-hidden">
        {/* 增强的科技感背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900"></div>
        <div className="absolute inset-0">
          {/* 动态网格背景 */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:60px_60px] animate-pulse"></div>

          {/* 增强的光点效果 */}
          <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-blue-400 rounded-full animate-ping opacity-60"></div>
          <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-purple-400 rounded-full animate-ping opacity-60" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-3/4 w-2.5 h-2.5 bg-cyan-400 rounded-full animate-ping opacity-60" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/3 right-1/3 w-1.5 h-1.5 bg-pink-400 rounded-full animate-ping opacity-60" style={{animationDelay: '3s'}}></div>

          {/* 添加光晕效果 */}
          <div className="absolute top-20 left-20 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-6"
            >
              <Sparkles className="w-4 h-4 text-blue-400" />
              <span className="text-sm font-medium text-blue-300">系统核心价值</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400">
                AI驱动的智能质检体系
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              重塑客服质检工作范式，从传统的"事后补救"转向"事中干预"，
              <br className="hidden sm:block" />
              从"经验管理"升级为"数据决策"，构建服务质量管理的核心竞争力
            </p>
          </motion.div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {coreValues.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 60, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.8,
                  delay: index * 0.2,
                  type: "spring",
                  stiffness: 60
                }}
                whileHover={{
                  y: -15,
                  scale: 1.03,
                  transition: { duration: 0.4 }
                }}
                className="group relative overflow-hidden rounded-3xl border border-white/20 p-8 transition-all duration-500 hover:shadow-2xl bg-white/10 backdrop-blur-lg"
              >
                {/* 增强的动态背景效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                <div className={`absolute -top-12 -right-12 w-48 h-48 bg-gradient-to-r ${value.gradient} rounded-full opacity-10 transition-all duration-700 group-hover:scale-125 group-hover:opacity-20 blur-3xl`}></div>
                <div className={`absolute -bottom-8 -left-8 w-32 h-32 bg-gradient-to-r ${value.gradient} rounded-full opacity-5 transition-all duration-700 group-hover:scale-150 group-hover:opacity-15 blur-2xl`}></div>

                {/* 边框光效 */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${value.gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-sm`}></div>

                <div className="relative z-10">
                  <div className="flex items-start justify-between mb-6">
                    <motion.div
                      className={`w-16 h-16 bg-gradient-to-br ${value.gradient} text-white rounded-2xl flex items-center justify-center shadow-2xl`}
                      whileHover={{
                        rotate: [0, -20, 20, 0],
                        scale: 1.2
                      }}
                      transition={{ duration: 0.8 }}
                    >
                      <value.icon className="w-8 h-8" />
                    </motion.div>

                    <div className="text-right">
                      <div className="text-xs font-medium text-gray-400 mb-1">性能指标</div>
                      <div className="text-sm font-bold text-gray-200">{value.stats}</div>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-white group-hover:text-blue-300 transition-colors duration-300 mb-4">
                    {value.title}
                  </h3>

                  <p className="text-gray-300 mb-8 leading-relaxed text-base">
                    {value.description}
                  </p>

                  <Link
                    to={value.link}
                    className="group/link inline-flex items-center gap-3 font-semibold text-base text-blue-400 hover:text-purple-400 transition-colors duration-300 bg-white/10 hover:bg-white/20 px-4 py-2 rounded-xl backdrop-blur-sm"
                  >
                    <span>了解更多</span>
                    <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform duration-300" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Traditional vs Intelligent Comparison Section */}
      <div className="py-24 sm:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-blue-50"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-64 h-64 bg-red-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30"></div>
          <div className="absolute bottom-20 right-20 w-64 h-64 bg-green-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-50 to-green-50 rounded-full border border-gray-200/50 mb-6"
            >
              <Lightbulb className="w-4 h-4 text-orange-600" />
              <span className="text-sm font-medium text-gray-700">传统 vs 智能质检</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-600 via-gray-800 to-green-600">
                告别传统质检痛点，拥抱智能质检未来
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              对比传统质检模式的局限性，展现AI驱动智能质检的革命性优势
            </p>
          </motion.div>

          <div className="grid gap-8 max-w-6xl mx-auto">
            {comparisonData.map((item, index) => (
              <motion.div
                key={item.aspect}
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-3xl p-8 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300"
              >
                <div className="grid md:grid-cols-3 gap-8 items-center">
                  {/* 对比维度 */}
                  <div className="text-center md:text-left">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{item.aspect}</h3>
                    <div className="w-16 h-1 bg-gradient-to-r from-red-500 to-green-500 mx-auto md:mx-0 rounded-full"></div>
                  </div>

                  {/* 传统模式 */}
                  <div className="text-center p-6 bg-red-50 rounded-2xl border-2 border-red-100">
                    <div className="flex items-center justify-center mb-4">
                      <item.traditional.icon className={`w-8 h-8 ${item.traditional.color}`} />
                    </div>
                    <div className="text-lg font-bold text-red-700 mb-2">{item.traditional.value}</div>
                    <div className="text-sm text-red-600">{item.traditional.desc}</div>
                    <div className="mt-3 text-xs text-red-500 font-medium">传统模式</div>
                  </div>

                  {/* 智能模式 */}
                  <div className="text-center p-6 bg-green-50 rounded-2xl border-2 border-green-100">
                    <div className="flex items-center justify-center mb-4">
                      <item.intelligent.icon className={`w-8 h-8 ${item.intelligent.color}`} />
                    </div>
                    <div className="text-lg font-bold text-green-700 mb-2">{item.intelligent.value}</div>
                    <div className="text-sm text-green-600">{item.intelligent.desc}</div>
                    <div className="mt-3 text-xs text-green-500 font-medium">智能模式</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Customer Success Cases Section */}
      <div className="py-24 sm:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900"></div>
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
          <div className="absolute top-20 left-20 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-6"
            >
              <Star className="w-4 h-4 text-yellow-400" />
              <span className="text-sm font-medium text-blue-300">客户成功案例</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400">
                真实案例见证智能质检价值
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              来自不同行业客户的真实反馈，展现智能质检系统的实际效果和业务价值
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-8">
            {successCases.map((caseItem, index) => (
              <motion.div
                key={caseItem.company}
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.8,
                  delay: index * 0.2,
                  type: "spring",
                  stiffness: 80
                }}
                whileHover={{
                  y: -10,
                  scale: 1.02,
                  transition: { duration: 0.3 }
                }}
                className={`bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 hover:border-white/30 transition-all duration-500`}
              >
                <div className="flex items-start gap-4 mb-6">
                  <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                    <caseItem.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-1">{caseItem.company}</h3>
                    <div className="text-sm text-blue-300 font-medium">{caseItem.industry}</div>
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-orange-300 mb-2">面临挑战</h4>
                  <p className="text-gray-300 text-sm leading-relaxed">{caseItem.challenge}</p>
                </div>

                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-green-300 mb-2">解决方案</h4>
                  <p className="text-gray-300 text-sm leading-relaxed">{caseItem.solution}</p>
                </div>

                <div>
                  <h4 className="text-sm font-semibold text-blue-300 mb-3">实施效果</h4>
                  <div className="space-y-2">
                    {caseItem.results.map((result, resultIndex) => (
                      <div key={resultIndex} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <span className="text-gray-300 text-sm">{result.metric}</span>
                        <span className="text-green-400 font-semibold text-sm">{result.improvement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Real-time Data Dashboard Section */}
      <div className="py-24 sm:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-gray-50"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-1/4 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40"></div>
          <div className="absolute bottom-20 right-1/4 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-40"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200/50 mb-6"
            >
              <Activity className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">实时运营数据</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600">
                数据驱动的质检运营中心
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              实时监控质检运营状态，用数据说话，让管理决策更科学、更精准
            </p>
          </motion.div>

          {/* 实时数据卡片 */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {realTimeData.map((data, index) => (
              <motion.div
                key={data.label}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.6,
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  y: -8,
                  scale: 1.05,
                  transition: { duration: 0.3 }
                }}
                className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <data.icon className={`w-8 h-8 ${data.color}`} />
                  <div className={`text-sm font-semibold px-2 py-1 rounded-full ${
                    data.trend.startsWith('+') ? 'bg-green-100 text-green-700' :
                    data.trend.startsWith('-') ? 'bg-red-100 text-red-700' :
                    'bg-blue-100 text-blue-700'
                  }`}>
                    {data.trend}
                  </div>
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{data.value}</div>
                <div className="text-sm text-gray-600 font-medium">{data.label}</div>
              </motion.div>
            ))}
          </div>

          {/* 数据可视化图表区域 */}
          <div className="grid lg:grid-cols-2 gap-8">
            {/* 质检趋势分析 */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="bg-white rounded-3xl p-8 shadow-xl border border-gray-200"
            >
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold text-gray-900">质检趋势分析</h3>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-600">质检量</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-gray-600">平均分</span>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl p-4 border border-gray-200">
                <RealTimeChart />
              </div>
            </motion.div>

            {/* 质检规则分析 */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="bg-white rounded-3xl p-8 shadow-xl border border-gray-200"
            >
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">失分规则分析</h3>
                <p className="text-gray-600 text-sm">各类质检规则的失分分布情况</p>
              </div>

              <div className="bg-gradient-to-br from-gray-50 to-purple-50 rounded-2xl p-4 border border-gray-200">
                <RuleAnalysisChart />
              </div>
            </motion.div>
          </div>

          {/* 质检仪表盘 */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="bg-white rounded-3xl p-8 shadow-xl border border-gray-200 mt-8"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">核心指标仪表盘</h3>
              <p className="text-gray-600">实时监控质检合格率和AI准确率</p>
            </div>

            <div className="bg-gradient-to-br from-gray-50 to-green-50 rounded-2xl p-4 border border-gray-200">
              <QualityDashboard />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Customer Testimonials Section */}
      <div className="py-24 sm:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-80 h-80 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30"></div>
          <div className="absolute bottom-20 right-10 w-72 h-72 bg-gradient-to-r from-blue-200 to-cyan-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30"></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-50 to-pink-50 rounded-full border border-purple-200/50 mb-6"
            >
              <Quote className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-700">客户证言</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600">
                听听用户怎么说
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              来自不同角色用户的真实反馈，见证智能质检系统带来的实际价值
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                initial={{ opacity: 0, y: 50, rotateY: -10 }}
                whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.8,
                  delay: index * 0.2,
                  type: "spring",
                  stiffness: 80
                }}
                whileHover={{
                  y: -10,
                  scale: 1.03,
                  transition: { duration: 0.3 }
                }}
                className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-white/50 hover:shadow-xl transition-all duration-500"
              >
                {/* 评分星级 */}
                <div className="flex items-center gap-1 mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>

                {/* 证言内容 */}
                <blockquote className="text-gray-700 leading-relaxed mb-6 text-base">
                  "{testimonial.content}"
                </blockquote>

                {/* 用户信息 */}
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-2xl">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-purple-600 font-medium">{testimonial.role}</div>
                    <div className="text-xs text-gray-500">{testimonial.company}</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Industry Insights Section */}
      <div className="py-24 sm:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900"></div>
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.03)_1px,transparent_1px)] bg-[size:40px_40px]"></div>
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-ping opacity-60"></div>
          <div className="absolute top-3/4 right-1/4 w-1.5 h-1.5 bg-purple-400 rounded-full animate-ping opacity-60" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-3/4 w-2.5 h-2.5 bg-pink-400 rounded-full animate-ping opacity-60" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-6"
            >
              <TrendingUp className="w-4 h-4 text-blue-400" />
              <span className="text-sm font-medium text-blue-300">行业洞察</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
                把握行业趋势，引领质检未来
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              深度解读客服质检行业发展趋势，洞察市场需求变化，抢占智能化转型先机
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8 mb-16">
            {industryInsights.map((insight, index) => (
              <motion.div
                key={insight.title}
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{
                  duration: 0.8,
                  delay: index * 0.2,
                  type: "spring",
                  stiffness: 80
                }}
                whileHover={{
                  y: -15,
                  scale: 1.05,
                  transition: { duration: 0.3 }
                }}
                className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 hover:border-white/30 transition-all duration-500"
              >
                <div className="flex items-center justify-between mb-6">
                  <div className={`w-16 h-16 ${insight.bgColor} rounded-2xl flex items-center justify-center`}>
                    <insight.icon className={`w-8 h-8 ${insight.color}`} />
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-white mb-1">{insight.stat}</div>
                    <div className="text-sm text-gray-400">{insight.trend}</div>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-white mb-4">{insight.title}</h3>
                <p className="text-gray-300 leading-relaxed">{insight.description}</p>
              </motion.div>
            ))}
          </div>

          {/* 行业趋势图表 */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-white mb-4">AI质检技术采用趋势</h3>
              <p className="text-gray-300">预计未来3年内，AI质检将成为客服行业标配</p>
            </div>

            <div className="bg-white/5 rounded-2xl p-4 border border-white/20">
              <IndustryTrendChart />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Call to Action Section */}
      <div className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50"></div>
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-72 h-72 bg-gradient-to-r from-blue-200 to-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-64 h-64 bg-gradient-to-r from-purple-200 to-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="max-w-6xl mx-auto px-6 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200/50 mb-6"
            >
              <Rocket className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">开启智能质检之旅</span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
                准备好革新您的质检体系了吗？
              </span>
            </h2>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-4xl mx-auto">
              加入已经选择智能质检的企业行列，体验从传统到智能的质检革命
            </p>

            {/* 核心优势快速展示 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
              {[
                { icon: CheckCircle, title: "100%全量覆盖", desc: "告别抽检盲区" },
                { icon: Zap, title: "分钟级反馈", desc: "实时质检结果" },
                { icon: Brain, title: "AI智能分析", desc: "客观公正标准" }
              ].map((item, index) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  className="text-center p-4"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <item.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="font-semibold text-gray-900 mb-1">{item.title}</div>
                  <div className="text-sm text-gray-600">{item.desc}</div>
                </motion.div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row justify-center items-center gap-6">
              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/quality-operation-overview"
                  className="group inline-flex items-center gap-3 px-10 py-5 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 text-lg"
                >
                  <BarChart3 className="w-6 h-6" />
                  <span>立即体验系统</span>
                  <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  to="/final-design/supervisor-homepage"
                  className="group inline-flex items-center gap-3 px-10 py-5 bg-white text-gray-700 font-semibold rounded-2xl border-2 border-gray-200 shadow-lg hover:shadow-xl hover:border-blue-300 hover:text-blue-700 transition-all duration-300 text-lg"
                >
                  <MessageSquare className="w-6 h-6 group-hover:text-blue-600 transition-colors duration-300" />
                  <span>预约演示</span>
                  <ArrowRight className="w-6 h-6 group-hover:translate-x-1 group-hover:text-blue-600 transition-all duration-300" />
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

    </div>
  );
};

export default HomePage;