import React, { useState, useEffect } from 'react';
import { Button, Tag, Space, Modal, Drawer, Form, Input, Select, Switch, message, Popconfirm, Tooltip, Card } from 'antd';
import { Plus, Search, Edit, Trash2, Power, PowerOff, Settings, Mic, Cloud, HardDrive } from 'lucide-react';
import { motion } from 'framer-motion';
import UnifiedPageHeader from './components/UnifiedPageHeader';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';

interface SpeechEngine {
  id: string;
  name: string;
  type: 'cloud' | 'offline';
  provider: string;
  status: 'active' | 'inactive';
  apiEndpoint?: string;
  apiKey?: string;
  modelPath?: string;
  description?: string;
  lastUsed: string;
  createdAt: string;
  updatedAt: string;
}

const SpeechEngineManagementPage: React.FC = () => {
  const [engines, setEngines] = useState<SpeechEngine[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [editingEngine, setEditingEngine] = useState<SpeechEngine | null>(null);
  const [form] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 模拟数据
  const mockEngines: SpeechEngine[] = [
    {
      id: '1',
      name: '阿里云语音识别',
      type: 'cloud',
      provider: '阿里云',
      status: 'active',
      apiEndpoint: 'https://nls-gateway.cn-shanghai.aliyuncs.com',
      apiKey: 'LTAI5t***********',
      description: '阿里云智能语音识别服务，支持实时语音识别',
      lastUsed: '2024-01-15 14:30:25',
      createdAt: '2024-01-01 10:00:00',
      updatedAt: '2024-01-15 14:30:25'
    },
    {
      id: '2',
      name: '腾讯云语音识别',
      type: 'cloud',
      provider: '腾讯云',
      status: 'inactive',
      apiEndpoint: 'https://asr.tencentcloudapi.com',
      apiKey: 'AKIDz***********',
      description: '腾讯云语音识别服务',
      lastUsed: '2024-01-10 09:15:30',
      createdAt: '2024-01-05 15:20:00',
      updatedAt: '2024-01-10 09:15:30'
    },
    {
      id: '3',
      name: 'Whisper离线引擎',
      type: 'offline',
      provider: 'OpenAI',
      status: 'active',
      modelPath: '/opt/models/whisper-large-v3',
      description: 'OpenAI Whisper离线语音识别模型',
      lastUsed: '2024-01-14 16:45:12',
      createdAt: '2024-01-03 11:30:00',
      updatedAt: '2024-01-14 16:45:12'
    },
    {
      id: '4',
      name: 'PaddleSpeech离线引擎',
      type: 'offline',
      provider: '百度飞桨',
      status: 'active',
      modelPath: '/opt/models/paddlespeech-conformer',
      description: '百度飞桨PaddleSpeech离线语音识别引擎',
      lastUsed: '2024-01-12 13:20:45',
      createdAt: '2024-01-02 14:15:00',
      updatedAt: '2024-01-12 13:20:45'
    }
  ];

  useEffect(() => {
    loadEngines();
  }, []);

  const loadEngines = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      setEngines(mockEngines);
    } catch (error) {
      message.error('加载语音识别引擎失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      active: { color: 'green', text: '运行中', icon: Power },
      inactive: { color: 'orange', text: '未启用', icon: PowerOff }
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    const IconComponent = config.icon;
    
    return (
      <div className="flex items-center space-x-1 whitespace-nowrap">
        <Tag color={config.color} className="flex items-center space-x-1">
          <IconComponent className="w-3 h-3" />
          <span>{config.text}</span>
        </Tag>
      </div>
    );
  };

  const getTypeTag = (type: string) => {
    const typeConfig = {
      cloud: { color: 'blue', text: '云端引擎', icon: Cloud },
      offline: { color: 'purple', text: '离线引擎', icon: HardDrive }
    };
    const config = typeConfig[type as keyof typeof typeConfig];
    const IconComponent = config.icon;
    
    return (
      <Tag color={config.color} className="flex items-center space-x-1">
        <IconComponent className="w-3 h-3" />
        <span>{config.text}</span>
      </Tag>
    );
  };

  const handleCreate = () => {
    setEditingEngine(null);
    form.resetFields();
    setIsDrawerVisible(true);
  };

  const handleEdit = (engine: SpeechEngine) => {
    setEditingEngine(engine);
    form.setFieldsValue({
      ...engine,
      status: engine.status === 'active'
    });
    setIsDrawerVisible(true);
  };

  const handleDelete = async (id: string) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      setEngines(engines.filter(engine => engine.id !== id));
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleToggleStatus = async (id: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      setEngines(engines.map(engine => 
        engine.id === id 
          ? { ...engine, status: newStatus as 'active' | 'inactive' }
          : engine
      ));
      message.success(`${newStatus === 'active' ? '启用' : '禁用'}成功`);
    } catch (error) {
      message.error('操作失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const engineData = {
        ...values,
        status: values.status ? 'active' : 'inactive',
        id: editingEngine?.id || Date.now().toString(),
        lastUsed: editingEngine?.lastUsed || new Date().toLocaleString(),
        createdAt: editingEngine?.createdAt || new Date().toLocaleString(),
        updatedAt: new Date().toLocaleString()
      };

      if (editingEngine) {
        setEngines(engines.map(engine => 
          engine.id === editingEngine.id ? engineData : engine
        ));
        message.success('更新成功');
      } else {
        setEngines([...engines, engineData]);
        message.success('创建成功');
      }
      
      setIsDrawerVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 筛选字段配置
  const filterFields: FilterField[] = [
    {
      key: 'search',
      label: '搜索',
      type: 'text',
      placeholder: '搜索引擎名称或服务商',
      width: 'w-64'
    },
    {
      key: 'type',
      label: '引擎类型',
      type: 'select',
      placeholder: '全部类型',
      options: [
        { value: 'cloud', label: '云端引擎' },
        { value: 'offline', label: '离线引擎' }
      ],
      width: 'w-32'
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      placeholder: '全部状态',
      options: [
        { value: 'active', label: '运行中' },
        { value: 'inactive', label: '未启用' }
      ],
      width: 'w-32'
    }
  ];

  // 过滤数据
  const filteredEngines = engines.filter(engine => {
    const searchText = filters.search || '';
    const filterType = filters.type || '';
    const filterStatus = filters.status || '';
    
    const matchesSearch = !searchText || 
      engine.name.toLowerCase().includes(searchText.toLowerCase()) ||
      engine.provider.toLowerCase().includes(searchText.toLowerCase());
    const matchesType = !filterType || engine.type === filterType;
    const matchesStatus = !filterStatus || engine.status === filterStatus;
    return matchesSearch && matchesType && matchesStatus;
  });

  // 分页数据
  const paginatedEngines = filteredEngines.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const handleSearch = () => {
    setCurrentPage(1);
  };

  const handleReset = () => {
    setFilters({});
    setCurrentPage(1);
  };

  // 渲染引擎表格行
  const renderEngineRow = (engine: SpeechEngine, index: number) => {
    return (
      <tr key={engine.id} className="hover:bg-gray-50">
        {/* 序号 */}
        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
          {(currentPage - 1) * pageSize + index + 1}
        </td>
        
        {/* 引擎名称 */}
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center space-x-3">
            <Mic className="w-4 h-4 text-blue-500" />
            <div>
              <div className="text-sm font-medium text-gray-900">{engine.name}</div>
              <div className="text-sm text-gray-500">{engine.provider}</div>
            </div>
          </div>
        </td>
        
        {/* 引擎类型 */}
        <td className="px-6 py-4 whitespace-nowrap">
          {getTypeTag(engine.type)}
        </td>
        
        {/* 服务商 */}
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {engine.provider}
        </td>
        
        {/* 状态 */}
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center justify-start">
            {getStatusTag(engine.status)}
          </div>
        </td>
        
        {/* 最后使用 */}
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {engine.lastUsed || '-'}
        </td>
        
        {/* 操作 */}
        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center justify-end space-x-2">
            <Tooltip title="编辑">
              <button
                onClick={() => handleEdit(engine)}
                className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors"
              >
                <Edit className="w-4 h-4" />
              </button>
            </Tooltip>
            <Tooltip title={engine.status === 'active' ? '禁用' : '启用'}>
              <button
                onClick={() => handleToggleStatus(engine.id, engine.status)}
                className={`p-1.5 rounded-lg transition-colors mx-1 ${
                  engine.status === 'active' 
                    ? 'text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50' 
                    : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                }`}
              >
                {engine.status === 'active' ? 
                  <PowerOff className="w-4 h-4" /> : 
                  <Power className="w-4 h-4" />
                }
              </button>
            </Tooltip>
            <Tooltip title="删除">
              <Popconfirm
                title="确认删除"
                description="确定要删除这个语音识别引擎吗？"
                onConfirm={() => handleDelete(engine.id)}
                okText="确定"
                cancelText="取消"
              >
                <button
                  className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </Popconfirm>
            </Tooltip>
          </div>
        </td>
      </tr>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50/50">
      <UnifiedPageHeader
        title="语音识别引擎管理"
        subtitle="管理云端和离线语音识别引擎配置"
        icon={Mic}
        actions={[
          {
            label: "新建引擎",
            icon: Plus,
            variant: "primary",
            onClick: handleCreate
          }
        ]}
      />

      <main className="p-6 md:p-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        >
        {/* 筛选区域 */}
        <UnifiedSearchFilter
          fields={filterFields}
          filters={filters}
          onFiltersChange={setFilters}
          onSearch={handleSearch}
          onReset={handleReset}
          isExpanded={isFilterExpanded}
          onToggleExpanded={() => setIsFilterExpanded(!isFilterExpanded)}
        />

        {/* 引擎列表 */}
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-600">
            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
              <tr>
                <th className="px-4 py-3">序号</th>
                <th className="px-6 py-3">引擎名称</th>
                <th className="px-6 py-3">引擎类型</th>
                <th className="px-6 py-3">服务商</th>
                <th className="px-6 py-3">状态</th>
                <th className="px-6 py-3">最后使用</th>
                <th className="px-6 py-3 text-right">操作</th>
              </tr>
            </thead>
            <tbody>
              {paginatedEngines.length > 0 ? (
                paginatedEngines.map((engine, index) => renderEngineRow(engine, index))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center text-gray-500">
                    <div className="flex flex-col items-center space-y-2">
                      <Mic className="w-12 h-12 text-gray-300" />
                      <span>暂无引擎数据</span>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        
        {/* 分页 */}
        {filteredEngines.length > 0 && (
          <UnifiedPagination
            current={currentPage}
            total={filteredEngines.length}
            pageSize={pageSize}
            onChange={setCurrentPage}
          />
        )}
        </motion.div>
      </main>

      {/* 新建/编辑抽屉 */}
      <Drawer
        title={editingEngine ? '编辑语音识别引擎' : '新建语音识别引擎'}
        width={600}
        open={isDrawerVisible}
        onClose={() => {
          setIsDrawerVisible(false);
          form.resetFields();
        }}
        footer={
          <div className="flex justify-end space-x-2">
            <Button onClick={() => {
              setIsDrawerVisible(false);
              form.resetFields();
            }}>
              取消
            </Button>
            <Button type="primary" onClick={() => form.submit()}>
              {editingEngine ? '更新' : '创建'}
            </Button>
          </div>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ status: true, type: 'cloud' }}
        >
          <Form.Item
            name="name"
            label="引擎名称"
            rules={[{ required: true, message: '请输入引擎名称' }]}
          >
            <Input placeholder="请输入引擎名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="引擎类型"
            rules={[{ required: true, message: '请选择引擎类型' }]}
          >
            <Select
              placeholder="请选择引擎类型"
              options={[
                { label: '云端引擎', value: 'cloud' },
                { label: '离线引擎', value: 'offline' }
              ]}
            />
          </Form.Item>

          <Form.Item
            name="provider"
            label="服务商"
            rules={[{ required: true, message: '请输入服务商' }]}
          >
            <Input placeholder="请输入服务商名称" />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const engineType = getFieldValue('type');
              
              if (engineType === 'cloud') {
                return (
                  <>
                    <Form.Item
                      name="apiEndpoint"
                      label="API端点"
                      rules={[{ required: true, message: '请输入API端点' }]}
                    >
                      <Input placeholder="请输入API端点地址" />
                    </Form.Item>
                    
                    <Form.Item
                      name="apiKey"
                      label="API密钥"
                      rules={[{ required: true, message: '请输入API密钥' }]}
                    >
                      <Input.Password placeholder="请输入API密钥" />
                    </Form.Item>
                  </>
                );
              } else if (engineType === 'offline') {
                return (
                  <Form.Item
                    name="modelPath"
                    label="模型路径"
                    rules={[{ required: true, message: '请输入模型路径' }]}
                  >
                    <Input placeholder="请输入离线模型文件路径" />
                  </Form.Item>
                );
              }
              
              return null;
            }}
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea
              placeholder="请输入引擎描述"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="启用状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};

export default SpeechEngineManagementPage;