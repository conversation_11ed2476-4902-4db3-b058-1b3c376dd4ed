import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Trash2, ChevronRight, ChevronDown, Database, Siren, AlertTriangle } from 'lucide-react';
import { Select } from 'antd';

// 词库接口
interface WordLibrary {
  id: string;
  name: string;
  description: string;
  wordCount: number;
  status: 'active' | 'inactive';
}

/**
 * Webhook渠道接口 (用于选择)
 */
interface WebhookChannel {
  id: string;
  name: string;
  enabled: boolean;
}

interface KeywordCheckConfig {
    detectionRole: 'agent' | 'customer' | 'all';
    detectionScope: 'full' | 'range' | 'before_hit' | 'after_hit' | 'around_hit';
    rangeStart?: number;
    rangeEnd?: number;
    keywords: string[];
    analysisMethod: 'single' | 'multiple';
    detectionType: 'any' | 'all' | 'count' | 'none';
    keywordCount: number;
    singleSentence: boolean;
    limitHitCount: boolean;
    hitCount: number;
    attachedLibraries?: string[]; // 挂载的词库ID列表
}

interface Condition {
    id: string;
    type: string;
    config?: KeywordCheckConfig;
    isCollapsed?: boolean;
    prerequisite?: string;
    // Future properties for specific condition types can be added here
    // e.g., keywords: string[], pattern: string
}

interface CreateRuleFormProps {
    onClose: () => void;
    onSubmit: (newRule: {
        name: string;
        description: string;
        importance: string;
        category: string;
        detectionLogic: 'ALL' | 'ANY' | 'NONE' | 'CUSTOM';
        conditions: Condition[];
        ruleType: string;
        enableAlert: boolean;
        alertLevel: string;
        notificationMethods: string[];
        notificationTargets: string[];
        selectedWebhookIds: string[];
    }) => void;
    allRuleTypes: string[];
}

const drawerVariants = {
    hidden: { x: '100%' },
    visible: { x: 0 },
};

const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
}

const OPERATOR_CATEGORIES = {
    '文字检查': ['关键词检查', '正则表达式检查'],
    '语音检查': ['通话静音检查', '语速检查', '抢话/打断', '录音时长检测', '非正常挂机', '非正常接听', '语句数检测', '角色判断', '能量检测'],
    '模型检查': ['大模型检查'],
};

// 初始词库数据
const initialWordLibraries: WordLibrary[] = [
  {
    id: '1',
    name: '服务态度正面词库',
    description: '包含优质服务、专业、耐心等正面服务态度词汇',
    wordCount: 25,
    status: 'active'
  },
  {
    id: '2',
    name: '服务态度负面词库',
    description: '包含不耐烦、敷衍、冷漠等负面服务态度词汇',
    wordCount: 18,
    status: 'active'
  },
  {
    id: '3',
    name: '禁用词汇库',
    description: '包含不当言论、敏感词汇等禁用词汇',
    wordCount: 32,
    status: 'active'
  },
  {
    id: '4',
    name: '专业术语库',
    description: '包含行业专业术语和标准用语',
    wordCount: 45,
    status: 'active'
  },
  {
    id: '5',
    name: '情绪词汇库',
    description: '包含各种情绪表达词汇',
    wordCount: 28,
    status: 'inactive'
  }
];

// 模拟从 "通知渠道管理" 页面获取的已配置的Webhook渠道
const mockWebhookChannels: WebhookChannel[] = [
  { id: '1', name: '钉钉-质检主管群机器人', enabled: true },
  { id: '2', name: '企业微信-服务告警', enabled: false },
  { id: '3', name: '内部工单系统触发器', enabled: true },
];

/**
 * 创建新规则的抽屉式表单组件
 * @param {object} props - 组件属性
 * @param {Function} props.onClose - 关闭抽屉的回调函数
 * @param {Function} props.onSubmit - 提交表单的回调函数
 * @param {string[]} props.allRuleTypes - 所有可选的规则类型
 */
export const CreateRuleForm: React.FC<CreateRuleFormProps> = ({ onClose, onSubmit }) => {
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [importance, setImportance] = useState('轻度违规');
    const [category, setCategory] = useState('');
    const [detectionLogic, setDetectionLogic] = useState<'ALL' | 'ANY' | 'NONE' | 'CUSTOM'>('ALL');
    const [conditions, setConditions] = useState<Condition[]>([]);
    const [error, setError] = useState('');
    const [showOperatorMenu, setShowOperatorMenu] = useState(false);
    const [activeCategory, setActiveCategory] = useState<string | null>(null);
    const [shouldMenuPopUp, setShouldMenuPopUp] = useState(false);
    const [showRuleTypeManagementModal, setShowRuleTypeManagementModal] = useState(false);
    const [newRuleTypeName, setNewRuleTypeName] = useState('');
    const [editingRuleTypeIndex, setEditingRuleTypeIndex] = useState<number | null>(null);
    const [customRuleTypes, setCustomRuleTypes] = useState<string[]>([]);
    const [wordLibraries] = useState<WordLibrary[]>(initialWordLibraries);
    const [enableAlert, setEnableAlert] = useState(false);
    const [alertLevel, setAlertLevel] = useState('高');
    const [notificationMethods, setNotificationMethods] = useState<string[]>(['realtime_center']);
    const [notificationTargets, setNotificationTargets] = useState<string[]>(['team_leader']);
    const [selectedWebhookIds, setSelectedWebhookIds] = useState<string[]>([]);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const menuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        // 当菜单关闭时，重置激活的分类
        if (!showOperatorMenu) {
            setActiveCategory(null);
        }
    }, [showOperatorMenu]);

    const handleRemoveCondition = (id: string) => {
        setConditions(prev => prev.filter(c => c.id !== id));
    };

    const handleConditionChange = (conditionId: string, field: string, value: any) => {
        setConditions(prevConditions => 
            prevConditions.map(condition => 
                condition.id === conditionId 
                    ? { ...condition, [field]: value }
                    : condition
            )
        );
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!name.trim()) {
            setError('规则名称不能为空');
            return;
        }
        if (conditions.length === 0) {
            setError('请至少添加一个检测条件');
            return;
        }
        onSubmit({ 
            name, 
            description, 
            importance, 
            category, 
            detectionLogic, 
            conditions, 
            ruleType: category,
            enableAlert,
            alertLevel,
            notificationMethods,
            notificationTargets,
            selectedWebhookIds
        });
        setError('');
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (showOperatorMenu && 
                menuRef.current && 
                buttonRef.current && 
                !menuRef.current.contains(event.target as Node) && 
                !buttonRef.current.contains(event.target as Node)) {
                setShowOperatorMenu(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showOperatorMenu]);

    const handleAddConditionClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        
        if (buttonRef.current) {
            const buttonRect = buttonRef.current.getBoundingClientRect();
            const windowHeight = window.innerHeight;
            const spaceBelow = windowHeight - buttonRect.bottom;
            const spaceAbove = buttonRect.top;
            const menuHeight = 300; // 估计的菜单高度

            setShouldMenuPopUp(spaceBelow < menuHeight && spaceAbove > spaceBelow);
        }
        
        setShowOperatorMenu(!showOperatorMenu);
    };

    const handleOperatorSelect = (operatorType: string) => {
        const newCondition: Condition = {
            id: conditions.length === 0 ? 'cond-a' : `cond-${Date.now()}-${Math.random()}`,
            type: operatorType,
            config: operatorType === '关键词检查' ? {
                detectionRole: 'all',
                detectionScope: 'full',
                rangeStart: undefined,
                rangeEnd: undefined,
                keywords: [] as string[],
                analysisMethod: 'single',
                detectionType: 'any',
                keywordCount: 1,
                singleSentence: false,
                limitHitCount: false,
                hitCount: 1,
                attachedLibraries: []
            } : undefined
        };
        setConditions(prevConditions => [...prevConditions, newCondition]);
        setShowOperatorMenu(false);
    };

    const handleToggleCollapse = (conditionId: string) => {
        setConditions(prev => prev.map(condition => 
            condition.id === conditionId 
                ? { ...condition, isCollapsed: !condition.isCollapsed }
                : condition
        ));
    };

    /**
     * 处理添加新规则类型
     */
    const handleAddRuleType = () => {
        if (!newRuleTypeName.trim()) {
            return;
        }
        
        const trimmedName = newRuleTypeName.trim();
        
        // 检查是否已存在
        const existingTypes = ['合规风险', '服务规范', '服务质量', '服务效率', '服务红线', '信息安全', '客户体验', '信息提取', '销售合规'];
        if (existingTypes.includes(trimmedName) || customRuleTypes.includes(trimmedName)) {
            alert('该规则类型已存在');
            return;
        }
        
        // 添加到自定义规则类型列表
        setCustomRuleTypes(prev => [...prev, trimmedName]);
        setNewRuleTypeName('');
    };

    /**
     * 处理编辑规则类型
     */
    const handleEditRuleType = (index: number) => {
        setEditingRuleTypeIndex(index);
        setNewRuleTypeName(customRuleTypes[index]);
    };

    /**
     * 处理保存编辑的规则类型
     */
    const handleSaveEditRuleType = () => {
        if (!newRuleTypeName.trim() || editingRuleTypeIndex === null) {
            return;
        }
        
        const trimmedName = newRuleTypeName.trim();
        const existingTypes = ['合规风险', '服务规范', '服务质量', '服务效率', '服务红线', '信息安全', '客户体验', '信息提取', '销售合规'];
        const otherCustomTypes = customRuleTypes.filter((_, index) => index !== editingRuleTypeIndex);
        
        if (existingTypes.includes(trimmedName) || otherCustomTypes.includes(trimmedName)) {
            alert('该规则类型已存在');
            return;
        }
        
        const updatedTypes = [...customRuleTypes];
        updatedTypes[editingRuleTypeIndex] = trimmedName;
        setCustomRuleTypes(updatedTypes);
        
        // 如果当前选中的类型被编辑了，更新选中值
        if (category === customRuleTypes[editingRuleTypeIndex]) {
            setCategory(trimmedName);
        }
        
        setEditingRuleTypeIndex(null);
        setNewRuleTypeName('');
    };

    /**
     * 处理删除规则类型
     */
    const handleDeleteRuleType = (index: number) => {
        const typeToDelete = customRuleTypes[index];
        if (confirm(`确定要删除规则类型"${typeToDelete}"吗？`)) {
            const updatedTypes = customRuleTypes.filter((_, i) => i !== index);
            setCustomRuleTypes(updatedTypes);
            
            // 如果当前选中的类型被删除了，清空选择
            if (category === typeToDelete) {
                setCategory('');
            }
        }
    };

    /**
     * 处理取消编辑
     */
    const handleCancelEdit = () => {
        setEditingRuleTypeIndex(null);
        setNewRuleTypeName('');
    };

    /**
     * 处理规则类型管理模态框的关闭
     */
    const handleCloseRuleTypeManagement = () => {
        setShowRuleTypeManagementModal(false);
        setEditingRuleTypeIndex(null);
        setNewRuleTypeName('');
    };

    const handleNotificationMethodChange = (method: string) => {
        const isCurrentlyIncluded = notificationMethods.includes(method);
        
        if (method === 'webhook' && isCurrentlyIncluded) {
            // This means we are about to REMOVE 'webhook'
            setSelectedWebhookIds([]); // Clear selected webhooks when disabling
        }

        setNotificationMethods(prev =>
            prev.includes(method)
                ? prev.filter(m => m !== method)
                : [...prev, method]
        );
    };

    const handleNotificationTargetChange = (target: string) => {
        setNotificationTargets(prev =>
            prev.includes(target)
                ? prev.filter(t => t !== target)
                : [...prev, target]
        );
    };

    return (
        <AnimatePresence>
            <motion.div
                variants={backdropVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-40"
                onClick={onClose}
            >
                <motion.div
                    variants={drawerVariants}
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    className="bg-white h-full w-full max-w-4xl shadow-2xl flex flex-col"
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex-shrink-0 flex items-center justify-between p-6 border-b border-gray-200">
                        <h2 className="text-xl font-semibold text-gray-800">创建新规则</h2>
                        <button
                            onClick={onClose}
                            className="p-2 text-gray-400 rounded-full hover:bg-gray-100 hover:text-gray-600 transition-colors"
                        >
                            <X className="w-6 h-6" />
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="flex-grow flex flex-col overflow-hidden">
                        <div className="flex-grow p-6 space-y-6 overflow-y-auto">
                            {/* Basic Info Section */}
                            <div className="space-y-4 p-4 border rounded-lg bg-white">
                                <h3 className="text-lg font-medium text-gray-800">基本信息</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="col-span-2">
                                    <label htmlFor="rule-name" className="block text-sm font-medium text-gray-700 mb-1">
                                        规则名称 <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        id="rule-name"
                                        type="text"
                                        value={name}
                                        onChange={(e) => setName(e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                        placeholder="例如：标准开场白与情绪检测"
                                    />
                                </div>
                                    <div>
                                        <label htmlFor="rule-category" className="block text-sm font-medium text-gray-700 mb-1">
                                            规则类型
                                        </label>
                                        <select
                                            id="rule-category"
                                            value={category}
                                            onChange={(e) => {
                                                if (e.target.value === '__manage__') {
                                                    setShowRuleTypeManagementModal(true);
                                                } else {
                                                    setCategory(e.target.value);
                                                }
                                            }}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white"
                                        >
                                            <option value="">请选择规则类型</option>
                                            <option value="合规风险">合规风险</option>
                                            <option value="服务规范">服务规范</option>
                                            <option value="服务质量">服务质量</option>
                                            <option value="服务效率">服务效率</option>
                                            <option value="服务红线">服务红线</option>
                                            <option value="信息安全">信息安全</option>
                                            <option value="客户体验">客户体验</option>
                                            <option value="信息提取">信息提取</option>
                                            <option value="销售合规">销售合规</option>
                                            {customRuleTypes.map((ruleType) => (
                                                <option key={ruleType} value={ruleType}>{ruleType}</option>
                                            ))}
                                            <option value="__manage__" className="text-blue-600 font-medium">⚙️ 规则类型管理</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label htmlFor="rule-importance" className="block text-sm font-medium text-gray-700 mb-1">
                                            重要程度
                                        </label>
                                        <select
                                            id="rule-importance"
                                            value={importance}
                                            onChange={(e) => setImportance(e.target.value)}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white"
                                        >
                                            <option>轻度违规</option>
                                            <option>中度违规</option>
                                            <option>严重违规</option>
                                        </select>
                                    </div>
                                    <div className="col-span-2">
                                    <label htmlFor="rule-description" className="block text-sm font-medium text-gray-700 mb-1">
                                        规则描述
                                    </label>
                                    <textarea
                                        id="rule-description"
                                        value={description}
                                        onChange={(e) => setDescription(e.target.value)}
                                        rows={3}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                        placeholder="输入关于此规则的详细说明..."
                                    />
                                    </div>
                                </div>
                            </div>

                            {/* Alert Config Section */}
                            <div className="space-y-4 p-4 border rounded-lg bg-white">
                                <div className="flex items-center justify-between">
                                    <h3 className="text-lg font-medium text-gray-800 flex items-center gap-2">
                                        {/* <Siren className="w-5 h-5 text-orange-500" /> */}
                                        <span>预警配置</span>
                                    </h3>
                                    <label htmlFor="enable-alert" className="flex items-center cursor-pointer">
                                        <span className="mr-3 text-sm font-medium text-gray-700">启用实时预警</span>
                                        <div className="relative">
                                            <input 
                                                id="enable-alert" 
                                                type="checkbox" 
                                                className="sr-only" 
                                                checked={enableAlert}
                                                onChange={(e) => setEnableAlert(e.target.checked)}
                                            />
                                            <div className={`block w-14 h-8 rounded-full transition-colors ${enableAlert ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
                                            <div className={`dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition-transform ${enableAlert ? 'translate-x-6' : ''}`}></div>
                                        </div>
                                    </label>
                                </div>
                                <AnimatePresence>
                                    {enableAlert && (
                                        <motion.div
                                            initial={{ opacity: 0, height: 0 }}
                                            animate={{ opacity: 1, height: 'auto' }}
                                            exit={{ opacity: 0, height: 0 }}
                                            transition={{ duration: 0.3, ease: "easeInOut" }}
                                            className="overflow-hidden"
                                        >
                                            <div className="pt-4 mt-4 border-t border-gray-200 space-y-4">
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                    <div>
                                                        <label htmlFor="alert-level" className="block text-sm font-medium text-gray-700 mb-1">预警等级</label>
                                                        <select
                                                            id="alert-level"
                                                            value={alertLevel}
                                                            onChange={(e) => setAlertLevel(e.target.value)}
                                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white"
                                                        >
                                                            <option value="严重">严重 (Critical)</option>
                                                            <option value="高">高 (High)</option>
                                                            <option value="中">中 (Medium)</option>
                                                            <option value="低">低 (Low)</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">通知方式</label>
                                                    <div className="space-y-4">
                                                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                                                            <label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                                                                <input type="checkbox" checked={notificationMethods.includes('realtime_center')} onChange={() => handleNotificationMethodChange('realtime_center')} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                                                                <span>在预警中心显示</span>
                                                            </label>
                                                            <label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                                                                <input type="checkbox" checked={notificationMethods.includes('popup')} onChange={() => handleNotificationMethodChange('popup')} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                                                                <span>系统弹窗通知</span>
                                                            </label>
                                                            <label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                                                                <input type="checkbox" checked={notificationMethods.includes('email')} onChange={() => handleNotificationMethodChange('email')} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                                                                <span>发送邮件</span>
                                                            </label>
                                                            <label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                                                                <input type="checkbox" checked={notificationMethods.includes('webhook')} onChange={() => handleNotificationMethodChange('webhook')} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                                                                <span>Webhook</span>
                                                            </label>
                                                        </div>
                                                        <AnimatePresence>
                                                            {notificationMethods.includes('webhook') && (
                                                                <motion.div
                                                                    initial={{ opacity: 0, height: 0, marginTop: 0 }}
                                                                    animate={{ opacity: 1, height: 'auto', marginTop: '1rem' }}
                                                                    exit={{ opacity: 0, height: 0, marginTop: 0 }}
                                                                    transition={{ duration: 0.2 }}
                                                                    className="overflow-hidden"
                                                                >
                                                                    <Select
                                                                        mode="multiple"
                                                                        allowClear
                                                                        style={{ width: '100%' }}
                                                                        placeholder="选择要通知的Webhook渠道..."
                                                                        value={selectedWebhookIds}
                                                                        onChange={setSelectedWebhookIds}
                                                                        options={mockWebhookChannels.map(channel => ({
                                                                            label: `${channel.name} ${!channel.enabled ? '(已禁用)' : ''}`,
                                                                            value: channel.id,
                                                                            disabled: !channel.enabled
                                                                        }))}
                                                                    />
                                                                </motion.div>
                                                            )}
                                                        </AnimatePresence>
                                                    </div>
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">通知对象</label>
                                                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                                                        <label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                                                            <input type="checkbox" checked={notificationTargets.includes('team_leader')} onChange={() => handleNotificationTargetChange('team_leader')} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                                                            <span>该坐席的班组长</span>
                                                        </label>
                                                        <label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                                                            <input type="checkbox" checked={notificationTargets.includes('qa_supervisor')} onChange={() => handleNotificationTargetChange('qa_supervisor')} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                                                            <span>所有质检主管</span>
                                                        </label>
                                                        {/* <label className="flex items-center space-x-2 text-sm text-gray-700 cursor-pointer">
                                                            <input type="checkbox" checked={notificationTargets.includes('specific_person')} onChange={() => handleNotificationTargetChange('specific_person')} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                                                            <span>指定人员</span>
                                                        </label> */}
                                                    </div>
                                                </div>
                                            </div>
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </div>

                            {/* Condition Config Section */}
                            <div className="space-y-4 p-4 border rounded-lg bg-white">
                                <h3 className="text-lg font-medium text-gray-800">条件配置</h3>
                                
                                <div className="grid grid-cols-1 md:grid-cols-5 items-center gap-4">
                                    <label className="text-sm font-medium text-gray-700 md:col-span-1">检测内容</label>
                                    <div className="md:col-span-4">
                                        <select
                                            value={detectionLogic}
                                            onChange={(e) => setDetectionLogic(e.target.value as 'ALL' | 'ANY' | 'NONE' | 'CUSTOM')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white"
                                        >
                                            <option value="ALL">以下条件全部满足</option>
                                            <option value="ANY" disabled={conditions.length <= 1}>以下条件任一满足</option>
                                            <option value="NONE" disabled={conditions.length <= 1}>以下条件任一不满足</option>
                                            <option value="CUSTOM">自定义条件之间的逻辑关系</option>
                                        </select>
                                        {detectionLogic === 'CUSTOM' && (
                                            <input
                                                type="text"
                                                placeholder="请输入以下项的逻辑关系，如 (a&&b)||(a&&c)"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm mt-2"
                                            />
                                        )}
                                    </div>
                                </div>
                                
                                <div className="grid grid-cols-1 md:grid-cols-5 items-start gap-4 pt-2">
                                    <label className="text-sm font-medium text-gray-700 md:col-span-1 mt-2">
                                        检测条件
                                        {/* <a href="#" className="block text-xs text-blue-600 hover:underline">查看教程</a> */}
                                    </label>
                                    <div className="md:col-span-4 space-y-3">
                                        {conditions.map((condition, index) => (
                                            <div key={condition.id} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                                                <div 
                                                    className="flex items-center justify-between p-3 bg-gray-50 cursor-pointer select-none border-b border-gray-200"
                                                    onClick={() => handleToggleCollapse(condition.id)}
                                                >
                                                    <div className="flex items-center gap-2">
                                                        {condition.isCollapsed ? (
                                                            <ChevronRight className="w-4 h-4 text-gray-500" />
                                                        ) : (
                                                            <ChevronDown className="w-4 h-4 text-gray-500" />
                                                        )}
                                                        <span className="text-sm font-medium text-gray-800">{String.fromCharCode(97 + index)}. {condition.type}</span>
                                                        <span className="text-xs text-gray-500">({condition.type})</span>
                                                    </div>
                                                    <button 
                                                        type="button" 
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleRemoveCondition(condition.id);
                                                        }} 
                                                        className="p-1 text-gray-400 hover:text-red-600 rounded-full hover:bg-red-50 transition-colors"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </button>
                                                </div>
                                                    
                                                {!condition.isCollapsed && condition.type === '关键词检查' && condition.config && (
                                                    <div className="p-4 space-y-4">
                                                        {/* Row for Detection Role */}
                                                        <div className="flex items-center">
                                                            <div className="w-24 text-right mr-4 shrink-0">
                                                                <label className="text-sm text-gray-600">检测角色</label>
                                                            </div>
                                                            <div className="flex-grow">
                                                                <select
                                                                    value={condition.config.detectionRole}
                                                                    onChange={(e) => handleConditionChange(condition.id, 'config', { ...condition.config, detectionRole: e.target.value as 'agent' | 'customer' | 'all' })}
                                                                    className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                                                >
                                                                    <option value="all">所有角色</option>
                                                                    <option value="customer">客户</option>
                                                                    <option value="agent">客服</option>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        {/* Row for Prerequisite */}
                                                        {condition.id !== 'cond-a' && (
                                                            <div className="flex items-center">
                                                                <div className="w-24 text-right mr-4 shrink-0">
                                                                    <label className="text-sm text-gray-600">前置条件</label>
                                                                </div>
                                                                <div className="flex-grow">
                                                                    <select
                                                                        className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                                                        value={condition.prerequisite || ''}
                                                                        onChange={(e) => {
                                                                            const prerequisiteValue = e.target.value;
                                                                            handleConditionChange(condition.id, 'prerequisite', prerequisiteValue);
                                                                            if (prerequisiteValue && condition.config) {
                                                                                handleConditionChange(condition.id, 'config', {
                                                                                    ...condition.config,
                                                                                    detectionScope: 'before_hit'
                                                                                });
                                                                            }
                                                                        }}
                                                                    >
                                                                        <option value="">无</option>
                                                                        {conditions.slice(0, index).map((prevCondition, prevIndex) => (
                                                                            <option key={prevCondition.id} value={String.fromCharCode(97 + prevIndex)}>
                                                                                条件{String.fromCharCode(97 + prevIndex)}
                                                                            </option>
                                                                        ))}
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {/* Row for Hit Count */}
                                                        {condition.prerequisite && (
                                                          <div className="flex items-center">
                                                              <div className="w-24 text-right mr-4 shrink-0">
                                                                  <div className="flex items-center justify-end gap-1">
                                                                      <label className="text-sm text-gray-600">条件</label>
                                                                      <span className="cursor-help text-gray-400 text-xs">ⓘ</span>
                                                                  </div>
                                                              </div>
                                                              <div className="flex items-center gap-2">
                                                                  <span className="text-sm text-gray-600">第</span>
                                                                  <input
                                                                      type="number"
                                                                      className="w-24 px-2 py-1 text-sm border border-gray-300 rounded-md"
                                                                      placeholder=""
                                                                  />
                                                                  <span className="text-sm text-gray-600">次命中</span>
                                                              </div>
                                                          </div>
                                                        )}

                                                        {/* Row for Detection Scope */}
                                                        <div className="flex items-start">
                                                          <div className="w-24 text-right mr-4 shrink-0 pt-1">
                                                              <div className="flex items-center justify-end gap-1">
                                                                  <label className="text-sm text-gray-600">检测范围</label>
                                                                  <span className="cursor-help text-gray-400 text-xs">ⓘ</span>
                                                              </div>
                                                          </div>
                                                            <div className="flex-grow space-y-2">
                                                                <select
                                                                    value={condition.config.detectionScope}
                                                                    onChange={(e) => handleConditionChange(condition.id, 'config', { ...condition.config, detectionScope: e.target.value as 'full' | 'range' | 'before_hit' | 'after_hit' | 'around_hit' })}
                                                                    className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                                                >
                                                                    {condition.prerequisite ? (
                                                                        <>
                                                                            <option value="before_hit">前置条件命中位置之前</option>
                                                                            <option value="after_hit">前置条件命中位置之后</option>
                                                                            <option value="around_hit">前置条件命中位置前后</option>
                                                                        </>
                                                                    ) : (
                                                                        <>
                                                                            <option value="full">全文</option>
                                                                            <option value="range">指定范围</option>
                                                                        </>
                                                                    )}
                                                                </select>
                                                                {condition.config.detectionScope === 'range' && (
                                                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                                                        <span>第</span>
                                                                        <input
                                                                            type="number"
                                                                            className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md"
                                                                            value={condition.config.rangeStart || ''}
                                                                            onChange={(e) => handleConditionChange(condition.id, 'config', { ...condition.config, rangeStart: e.target.value ? parseInt(e.target.value) : undefined })}
                                                                            min={1}
                                                                        />
                                                                        <span>~</span>
                                                                        <input
                                                                            type="number"
                                                                            className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md"
                                                                            value={condition.config.rangeEnd || ''}
                                                                            onChange={(e) => handleConditionChange(condition.id, 'config', { ...condition.config, rangeEnd: e.target.value ? parseInt(e.target.value) : undefined })}
                                                                            min={condition.config.rangeStart || 1}
                                                                        />
                                                                        <span>句</span>
                                                                    </div>
                                                                )}
                                                                {(condition.config.detectionScope === 'before_hit' || condition.config.detectionScope === 'after_hit' || condition.config.detectionScope === 'around_hit') && (
                                                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                                                        <span>{condition.config.detectionScope === 'around_hit' ? '前后' : '范围'}</span>
                                                                        <span>第</span>
                                                                        <input
                                                                            type="number"
                                                                            className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md"
                                                                            value={condition.config.rangeStart || ''}
                                                                            onChange={(e) => handleConditionChange(condition.id, 'config', { ...condition.config, rangeStart: e.target.value ? parseInt(e.target.value) : undefined })}
                                                                            min={1}
                                                                        />
                                                                        <span>~</span>
                                                                        <input
                                                                            type="number"
                                                                            className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md"
                                                                            value={condition.config.rangeEnd || ''}
                                                                            onChange={(e) => handleConditionChange(condition.id, 'config', { ...condition.config, rangeEnd: e.target.value ? parseInt(e.target.value) : undefined })}
                                                                            min={condition.config.rangeStart || 1}
                                                                        />
                                                                        <span>句</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>

                                                        {/* Row for Keywords */}
                                                        <div className="flex items-start">
                                                            <div className="w-24 text-right mr-4 shrink-0 pt-1">
                                                                <div className="flex items-center justify-end gap-1">
                                                                    <label className="text-sm text-gray-600">关键词</label>
                                                                    <span className="cursor-help text-gray-400 text-xs">ⓘ</span>
                                                                </div>
                                                            </div>
                                                            <div className="flex-grow">
                                                                <div className="flex flex-wrap gap-2 p-2 min-h-[38px] border border-gray-300 rounded-md bg-white focus-within:ring-1 focus-within:ring-blue-500 focus-within:border-blue-500">
                                                                    {condition.config.keywords.map((keyword, idx) => (
                                                                        <span key={idx} className="inline-flex items-center px-2 py-1 rounded bg-blue-50 text-blue-700 text-sm">
                                                                            {keyword}
                                                                            <button
                                                                                type="button"
                                                                                onClick={() => {
                                                                                    const newKeywords = condition.config!.keywords.filter((_, i) => i !== idx);
                                                                                    handleConditionChange(condition.id, 'config', { ...condition.config, keywords: newKeywords });
                                                                                }}
                                                                                className="ml-1.5 text-blue-600 hover:text-blue-800"
                                                                            >×</button>
                                                                        </span>
                                                                    ))}
                                                                    <input
                                                                        type="text"
                                                                        placeholder="输入按enter键添加,多个可用逗号隔开"
                                                                        className="flex-grow min-w-[220px] border-0 focus:ring-0 text-sm p-1"
                                                                        onKeyDown={(e) => {
                                                                            if (e.key === 'Enter' && (e.target as HTMLInputElement).value.trim()) {
                                                                                e.preventDefault();
                                                                                const newKeywords = (e.target as HTMLInputElement).value.trim().split(/,|，/).filter(k => k.trim());
                                                                                const updatedKeywords = [...condition.config!.keywords, ...newKeywords];
                                                                                handleConditionChange(condition.id, 'config', { ...condition.config, keywords: updatedKeywords });
                                                                                (e.target as HTMLInputElement).value = '';
                                                                            }
                                                                        }}
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Row for Word Libraries */}
                                                        <div className="flex items-start">
                                                            <div className="w-24 text-right mr-4 shrink-0 pt-1">
                                                                <div className="flex items-center justify-end gap-1">
                                                                    <label className="text-sm text-gray-600">挂载词库</label>
                                                                    <span className="cursor-help text-gray-400 text-xs">ⓘ</span>
                                                                </div>
                                                            </div>
                                                            <div className="flex-grow">
                                                                <div className="space-y-2">
                                                                    {/* 已选择的词库 */}
                                                                    {condition.config.attachedLibraries && condition.config.attachedLibraries.length > 0 && (
                                                                        <div className="flex flex-wrap gap-2">
                                                                            {condition.config.attachedLibraries.map((libraryId) => {
                                                                                const library = wordLibraries.find(lib => lib.id === libraryId);
                                                                                return library ? (
                                                                                    <span key={libraryId} className="inline-flex items-center px-3 py-1 rounded-full bg-green-50 text-green-700 text-sm border border-green-200">
                                                                                        <Database className="w-3 h-3 mr-1" />
                                                                                        {library.name}
                                                                                        <button
                                                                                            type="button"
                                                                                            onClick={() => {
                                                                                                const newLibraries = condition.config!.attachedLibraries!.filter(id => id !== libraryId);
                                                                                                handleConditionChange(condition.id, 'config', { ...condition.config, attachedLibraries: newLibraries });
                                                                                            }}
                                                                                            className="ml-2 text-green-600 hover:text-green-800"
                                                                                        >×</button>
                                                                                    </span>
                                                                                ) : null;
                                                                            })}
                                                                        </div>
                                                                    )}
                                                                    
                                                                    {/* 词库选择下拉菜单 */}
                                                                    <select
                                                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                                                                        value=""
                                                                        onChange={(e) => {
                                                                            if (e.target.value) {
                                                                                const currentLibraries = condition.config!.attachedLibraries || [];
                                                                                if (!currentLibraries.includes(e.target.value)) {
                                                                                    const newLibraries = [...currentLibraries, e.target.value];
                                                                                    handleConditionChange(condition.id, 'config', { ...condition.config, attachedLibraries: newLibraries });
                                                                                }
                                                                                e.target.value = ''; // 重置选择
                                                                            }
                                                                        }}
                                                                    >
                                                                        <option value="">选择要挂载的词库...</option>
                                                                        {wordLibraries
                                                                            .filter(library => library.status === 'active')
                                                                            .filter(library => !condition.config?.attachedLibraries?.includes(library.id))
                                                                            .map(library => (
                                                                                <option key={library.id} value={library.id}>
                                                                                    {library.name} ({library.wordCount}个词条)
                                                                                </option>
                                                                            ))
                                                                        }
                                                                    </select>
                                                                    
                                                                    {condition.config.attachedLibraries && condition.config.attachedLibraries.length === 0 && (
                                                                        <p className="text-xs text-gray-500 mt-1">
                                                                            选择词库后，系统将自动将词库中的词条加入到关键词检测中
                                                                        </p>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Row for Analysis Method */}
                                                        <div className="flex items-center">
                                                            <div className="w-24 text-right mr-4 shrink-0">
                                                                <div className="flex items-center justify-end gap-1">
                                                                    <label className="text-sm text-gray-600">分析方式</label>
                                                                    <span className="cursor-help text-gray-400 text-xs">ⓘ</span>
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center gap-6">
                                                                <label className="inline-flex items-center">
                                                                    <input
                                                                        type="radio"
                                                                        name={`analysis-method-${condition.id}`}
                                                                        checked={condition.config.analysisMethod === 'single'}
                                                                        onChange={() => handleConditionChange(condition.id, 'config', { ...condition.config, analysisMethod: 'single' })}
                                                                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                                    />
                                                                    <span className="ml-2 text-sm text-gray-700">单句分析</span>
                                                                </label>
                                                                <label className="inline-flex items-center">
                                                                    <input
                                                                        type="radio"
                                                                        name={`analysis-method-${condition.id}`}
                                                                        checked={condition.config.analysisMethod === 'multiple'}
                                                                        onChange={() => handleConditionChange(condition.id, 'config', { ...condition.config, analysisMethod: 'multiple' })}
                                                                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                                    />
                                                                    <span className="ml-2 text-sm text-gray-700">多句分析</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        
                                                        {/* Row for Detection Type */}
                                                        <div className="flex items-start">
                                                            <div className="w-24 text-right mr-4 shrink-0 pt-1">
                                                                <div className="flex items-center justify-end gap-1">
                                                                    <label className="text-sm text-gray-600">检测类型</label>
                                                                    <span className="cursor-help text-gray-400 text-xs">ⓘ</span>
                                                                </div>
                                                            </div>
                                                            <div className="grid grid-cols-2 gap-x-6 gap-y-2">
                                                                <label className="inline-flex items-center">
                                                                    <input
                                                                        type="radio"
                                                                        name={`detection-type-${condition.id}`}
                                                                        checked={condition.config?.detectionType === 'any'}
                                                                        onChange={() => handleConditionChange(condition.id, 'config', { ...condition.config, detectionType: 'any' })}
                                                                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                                    />
                                                                    <span className="ml-2 text-sm text-gray-700">包含任意一个关键词</span>
                                                                </label>
                                                                <label className="inline-flex items-center">
                                                                    <input
                                                                        type="radio"
                                                                        name={`detection-type-${condition.id}`}
                                                                        checked={condition.config?.detectionType === 'all'}
                                                                        onChange={() => handleConditionChange(condition.id, 'config', { ...condition.config, detectionType: 'all' })}
                                                                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                                    />
                                                                    <span className="ml-2 text-sm text-gray-700">包含全部关键词</span>
                                                                </label>
                                                                <label className={`inline-flex items-center ${(condition.config?.keywords?.length || 0) < 2 ? 'opacity-50 cursor-not-allowed' : ''}`}>
                                                                    <input
                                                                        type="radio"
                                                                        name={`detection-type-${condition.id}`}
                                                                        checked={condition.config?.detectionType === 'count'}
                                                                        onChange={() => {
                                                                            if (condition.type === '关键词检查' && condition.config && condition.config.keywords.length >= 2) {
                                                                                handleConditionChange(condition.id, 'config', { ...condition.config, detectionType: 'count' });
                                                                            }
                                                                        }}
                                                                        disabled={(condition.config?.keywords?.length || 0) < 2}
                                                                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500 disabled:opacity-50"
                                                                    />
                                                                    <span className="ml-2 text-sm text-gray-700">包含任意N个关键词</span>
                                                                </label>
                                                                <label className="inline-flex items-center">
                                                                    <input
                                                                        type="radio"
                                                                        name={`detection-type-${condition.id}`}
                                                                        checked={condition.config?.detectionType === 'none'}
                                                                        onChange={() => handleConditionChange(condition.id, 'config', { ...condition.config, detectionType: 'none' })}
                                                                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                                    />
                                                                    <span className="ml-2 text-sm text-gray-700">全部不包含</span>
                                                                </label>
                                                            </div>
                                                        </div>

                                                        {/* Row for Extended Features */}
                                                        <div className="flex items-center">
                                                            <div className="w-24 text-right mr-4 shrink-0">
                                                                <div className="flex items-center justify-end gap-1">
                                                                    <label className="text-sm text-gray-600">扩展功能</label>
                                                                    <span className="cursor-help text-gray-400 text-xs">ⓘ</span>
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center gap-4">
                                                                <label className={`inline-flex items-center ${(condition.config?.keywords?.length || 0) < 2 ? 'opacity-50 cursor-not-allowed' : ''}`}>
                                                                    <input
                                                                        type="checkbox"
                                                                        checked={condition.config?.singleSentence}
                                                                        onChange={(e) => {
                                                                            if (condition.type === '关键词检查' && condition.config && condition.config.keywords.length >= 2) {
                                                                                handleConditionChange(condition.id, 'config', { ...condition.config, singleSentence: e.target.checked });
                                                                            }
                                                                        }}
                                                                        disabled={(condition.config?.keywords?.length || 0) < 2}
                                                                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50"
                                                                    />
                                                                    <span className="ml-2 text-sm text-gray-700">单句话内生效</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                        
                                        {/* Add Condition Button */}
                                        <div className="relative">
                                            <button
                                                ref={buttonRef}
                                                type="button"
                                                onClick={handleAddConditionClick}
                                                className="w-full flex items-center justify-center gap-2 p-3 bg-blue-50 text-blue-700 border border-blue-200 rounded-lg hover:bg-blue-100 hover:border-blue-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 ease-in-out"
                                            >
                                                <Plus className="w-5 h-5" />
                                                <span className="font-medium">添加检测条件</span>
                                            </button>

                                            {/* Operator Menu */}
                                            {showOperatorMenu && (
                                                <div
                                                    ref={menuRef}
                                                    className={`absolute ${shouldMenuPopUp ? 'bottom-full mb-2' : 'top-full mt-2'} left-0 w-auto bg-white rounded-lg shadow-xl border border-gray-200 z-50 flex overflow-hidden`}
                                                >
                                                    {/* Left Panel: Categories */}
                                                    <div className="w-40 border-r border-gray-100 bg-gray-50/50 py-2">
                                                        {Object.keys(OPERATOR_CATEGORIES).map((category) => (
                                                            <div
                                                                key={category}
                                                                onClick={() => setActiveCategory(category)}
                                                                className={`px-4 py-2 text-sm cursor-pointer transition-colors duration-150 flex justify-between items-center ${
                                                                    activeCategory === category
                                                                        ? 'bg-white text-blue-600 font-semibold'
                                                                        : 'text-gray-600 hover:bg-white'
                                                                }`}
                                                            >
                                                                <span>{category}</span>
                                                                <ChevronRight className="w-4 h-4 text-gray-400" />
                                                            </div>
                                                        ))}
                                                    </div>

                                                    {/* Right Panel: Operators */}
                                                    {activeCategory && (
                                                        <div className="w-56 py-2">
                                                            {OPERATOR_CATEGORIES[activeCategory as keyof typeof OPERATOR_CATEGORIES].map((operator) => (
                                                                <button
                                                                    key={operator}
                                                                    type="button"
                                                                    onClick={() => handleOperatorSelect(operator)}
                                                                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-150"
                                                                >
                                                                    {operator}
                                                                </button>
                                                            ))}
                                                        </div>
                                                    )}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Add footer with buttons */}
                        <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-gray-50">
                            <div className="flex justify-end gap-3">
                            <button
                                type="button"
                                onClick={onClose}
                                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                    保存
                            </button>
                            </div>
                        </div>
                    </form>
                </motion.div>
            </motion.div>
            
            {/* 规则类型管理模态框 */}
            {showRuleTypeManagementModal && (
                <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-50">
                    <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[80vh] flex flex-col">
                        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                            <h3 className="text-lg font-semibold text-gray-800">规则类型管理</h3>
                            <button
                                onClick={handleCloseRuleTypeManagement}
                                className="p-2 text-gray-400 rounded-full hover:bg-gray-100 hover:text-gray-600 transition-colors"
                            >
                                <X className="w-5 h-5" />
                            </button>
                        </div>
                        
                        <div className="flex-1 overflow-y-auto">
                            {/* 添加新规则类型区域 */}
                            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                                <div className="flex items-center gap-3">
                                    <input
                                        type="text"
                                        value={newRuleTypeName}
                                        onChange={(e) => setNewRuleTypeName(e.target.value)}
                                        onKeyPress={(e) => {
                                            if (e.key === 'Enter') {
                                                if (editingRuleTypeIndex !== null) {
                                                    handleSaveEditRuleType();
                                                } else {
                                                    handleAddRuleType();
                                                }
                                            }
                                        }}
                                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                        placeholder="请输入规则类型名称"
                                    />
                                    {editingRuleTypeIndex !== null ? (
                                        <>
                                            <button
                                                type="button"
                                                onClick={handleSaveEditRuleType}
                                                disabled={!newRuleTypeName.trim()}
                                                className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                保存
                                            </button>
                                            <button
                                                type="button"
                                                onClick={handleCancelEdit}
                                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                            >
                                                取消
                                            </button>
                                        </>
                                    ) : (
                                        <button
                                            type="button"
                                            onClick={handleAddRuleType}
                                            disabled={!newRuleTypeName.trim()}
                                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                                        >
                                            <Plus className="w-4 h-4" />
                                            添加
                                        </button>
                                    )}
                                </div>
                            </div>
                            
                            {/* 规则类型列表 */}
                            <div className="px-6 py-4">
                                <div className="space-y-3">
                                    {/* 系统预设规则类型 */}
                                    <div>
                                        <h4 className="text-sm font-medium text-gray-600 mb-2">系统预设类型</h4>
                                        <div className="space-y-2">
                                            {['合规风险', '服务规范', '服务质量', '服务效率', '服务红线', '信息安全', '客户体验', '信息提取', '销售合规'].map((type) => (
                                                <div key={type} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                    <span className="text-sm text-gray-700">{type}</span>
                                                    <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">系统预设</span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                    
                                    {/* 自定义规则类型 */}
                                    {customRuleTypes.length > 0 && (
                                        <div>
                                            <h4 className="text-sm font-medium text-gray-600 mb-2">自定义类型</h4>
                                            <div className="space-y-2">
                                                {customRuleTypes.map((type, index) => (
                                                    <div key={index} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                                        <span className="text-sm text-gray-700">{type}</span>
                                                        <div className="flex items-center gap-2">
                                                            <button
                                                                type="button"
                                                                onClick={() => handleEditRuleType(index)}
                                                                className="p-1 text-gray-400 hover:text-blue-600 rounded transition-colors"
                                                                title="编辑"
                                                            >
                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                                </svg>
                                                            </button>
                                                            <button
                                                                type="button"
                                                                onClick={() => handleDeleteRuleType(index)}
                                                                className="p-1 text-gray-400 hover:text-red-600 rounded transition-colors"
                                                                title="删除"
                                                            >
                                                                <Trash2 className="w-4 h-4" />
                                                            </button>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                    
                                    {customRuleTypes.length === 0 && (
                                        <div className="text-center py-8 text-gray-500">
                                            <p className="text-sm">暂无自定义规则类型</p>
                                            <p className="text-xs mt-1">在上方输入框中添加新的规则类型</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        
                        <div className="px-6 py-4 border-t border-gray-200 flex justify-end">
                            <button
                                type="button"
                                onClick={handleCloseRuleTypeManagement}
                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default CreateRuleForm;