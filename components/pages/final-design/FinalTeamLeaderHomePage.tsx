import React, { useState } from 'react';
import { Card, Button, Badge, Typography, Space, Avatar } from 'antd';
import { Button as CustomButton } from '../../ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  Award, 
  AlertTriangle, 
  Bell, 
  Calendar,
  Users,
  Target,
  CheckCircle,
  XCircle,
  BarChart3,
  Clock,
  MessageSquare,
  Crown,
  Star,
  UserCheck,
  Phone,
  FileText,
  HelpCircle
} from 'lucide-react';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import { MetricCard } from './common/MetricCard';
import { MetricDescription } from './common/MetricHelpButton';
import { GlobalStyles } from './common/GlobalStyles';

const { Text } = Typography;

// 班组长视角指标说明数据
const teamMetricDescriptions: Record<string, MetricDescription> = {
  teamAverageScore: {
    title: "团队平均质检得分",
    description: "在特定时间段内，团队内所有成员的质检得分的平均值。",
    calculation: "团队平均质检得分 = 周期内团队所有成员的质检得分总和 / 周期内团队已完成的质检总次数"
  },
  teamQualificationRate: {
    title: "团队质检合格率",
    description: "在特定时间段内，团队质检结果为\"合格\"的次数占团队总质检次数的百分比。",
    calculation: "团队质检合格率 = ( 周期内团队质检合格的总次数 / 周期内团队已完成的质检总次数 ) × 100%"
  },
  teamAppealRate: {
    title: "团队申诉率",
    description: "在特定时间段内，团队成员发起的申诉次数占团队总质检次数的百分比。",
    calculation: "团队申诉率 = ( 周期内团队发起的总申诉次数 / 周期内团队已完成的质检总次数 ) × 100%"
  },
  teamAppealSuccessRate: {
    title: "团队申诉成功率",
    description: "在特定时间段内，团队申诉成功的次数占团队总申诉次数的百分比。",
    calculation: "团队申诉成功率 = ( 周期内团队申诉成功的总次数 / 周期内团队发起的总申诉次数 ) × 100%"
  }
};



/**
 * 团队绩效数据接口
 */
interface TeamPerformanceData {
  teamAverageScore: number;
  teamQualificationRate: number;
  teamAppealRate: number;
  teamAppealSuccessRate: number;
  teamMemberCount: number;
  teamRank: number;
  totalTeams: number;
}

/**
 * 团队成员成绩接口
 */
interface TeamMemberScore {
  id: string;
  name: string;
  averageScore: number;
  qualificationRate: number;
  appealCount: number;
  rank: number;
  trend: 'up' | 'down' | 'stable';
}

/**
 * 通知公告接口
 */
interface Notification {
  id: number;
  type: 'system' | 'task' | 'announcement' | 'result' | 'appeal';
  title: string;
  content: string;
  time: string;
  isRead: boolean;
  priority: 'high' | 'medium' | 'low';
}

// 模拟团队绩效数据
const teamPerformanceData: TeamPerformanceData = {
  teamAverageScore: 87.2,
  teamQualificationRate: 89.5,
  teamAppealRate: 8.3,
  teamAppealSuccessRate: 65.2,
  teamMemberCount: 12,
  teamRank: 2,
  totalTeams: 8
};

// 团队整体服务质量趋势数据
const teamQualityTrendData = [
  { date: '01-15', score: 85.8 },
  { date: '01-16', score: 86.4 },
  { date: '01-17', score: 87.1 },
  { date: '01-18', score: 86.9 },
  { date: '01-19', score: 87.2 },
  { date: '01-20', score: 88.1 },
  { date: '01-21', score: 87.5 }
];

// 团队严重错误项统计
const teamSeriousErrors = [
  { name: '未核实客户身份', count: 5, severity: 'high' },
  { name: '违规承诺退款', count: 3, severity: 'high' },
  { name: '未经授权查询客户信息', count: 2, severity: 'high' },
  { name: '泄露客户隐私信息', count: 1, severity: 'high' },
  { name: '违规操作系统权限', count: 1, severity: 'high' }
];

// 团队高频失分项
const teamFrequentDeductions = [
  { name: '服务态度不佳', count: 18, points: -36 },
  { name: '未主动询问需求', count: 15, points: -30 },
  { name: '话术不规范', count: 12, points: -24 },
  { name: '响应时间过长', count: 10, points: -20 },
  { name: '未做总结确认', count: 8, points: -16 }
];

// 通知公告数据
const notifications: Notification[] = [
  {
    id: 1,
    type: 'result',
    title: '组员质检结果通知',
    content: '张三的质检记录 QM2507210001 已出分，最终得分 92.5 分，表现优秀',
    time: '1小时前',
    isRead: false,
    priority: 'high'
  },
  {
    id: 2,
    type: 'result',
    title: '组员质检结果通知',
    content: '李四的质检记录 QM2507210002 已出分，最终得分 78.0 分，需要关注',
    time: '3小时前',
    isRead: false,
    priority: 'medium'
  },
  {
    id: 3,
    type: 'system',
    title: '系统公告',
    content: '关于五一假期排班及调休的通知已发布，请组织团队查看并安排',
    time: '1天前',
    isRead: true,
    priority: 'medium'
  },
  {
    id: 4,
    type: 'system',
    title: '质检标准更新通知',
    content: '客服规范V2.1版本已发布，新增3项质检规则，请组织团队学习',
    time: '2天前',
    isRead: true,
    priority: 'low'
  }
];

/**
 * 团队成员成绩排名数据
 */
const teamMemberScores: TeamMemberScore[] = [
  {
    id: '001',
    name: '张三',
    averageScore: 92.5,
    qualificationRate: 95.2,
    appealCount: 1,
    rank: 1,
    trend: 'up'
  },
  {
    id: '002',
    name: '李四',
    averageScore: 89.8,
    qualificationRate: 91.7,
    appealCount: 2,
    rank: 2,
    trend: 'stable'
  },
  {
    id: '003',
    name: '王五',
    averageScore: 87.3,
    qualificationRate: 88.9,
    appealCount: 3,
    rank: 3,
    trend: 'up'
  },
  {
    id: '004',
    name: '赵六',
    averageScore: 85.1,
    qualificationRate: 86.4,
    appealCount: 1,
    rank: 4,
    trend: 'down'
  },
  {
    id: '005',
    name: '钱七',
    averageScore: 83.7,
    qualificationRate: 84.2,
    appealCount: 4,
    rank: 5,
    trend: 'down'
  }
];

/**
 * 团队成员近期质检记录接口
 */
interface TeamMemberRecentScore {
  id: string;
  agentName: string;
  agentAvatar: string;
  result: 'qualified' | 'unqualified';
  score: number;
  callTime: string;
  duration: string;
  appealStatus: 'none' | 'processing' | 'success' | 'failed';
}

/**
 * 团队成员近期质检记录数据
 */
const teamMemberRecentScores: TeamMemberRecentScore[] = [
  {
    id: 'QM2507010095',
    agentName: '张三',
    agentAvatar: 'https://i.pravatar.cc/150?u=a042581f4e29026704d',
    result: 'unqualified',
    score: 78,
    callTime: '10:30',
    duration: '5分21秒',
    appealStatus: 'success',
  },
  {
    id: 'QM2507010094',
    agentName: '李四',
    agentAvatar: 'https://i.pravatar.cc/150?u=a042581f4e29026705d',
    result: 'qualified',
    score: 92,
    callTime: '09:45',
    duration: '8分10秒',
    appealStatus: 'none',
  },
  {
    id: 'QM2507010093',
    agentName: '王五',
    agentAvatar: 'https://i.pravatar.cc/150?u=a042581f4e29026706d',
    result: 'unqualified',
    score: 82,
    callTime: '16:20',
    duration: '3分45秒',
    appealStatus: 'failed',
  },
  {
    id: 'QM2507010092',
    agentName: '赵六',
    agentAvatar: 'https://i.pravatar.cc/150?u=a042581f4e29026707d',
    result: 'qualified',
    score: 95,
    callTime: '14:05',
    duration: '12分30秒',
    appealStatus: 'none',
  },
  {
    id: 'QM2507010091',
    agentName: '钱七',
    agentAvatar: 'https://i.pravatar.cc/150?u=a042581f4e29026708d',
    result: 'unqualified',
    score: 65,
    callTime: '11:50',
    duration: '7分5秒',
    appealStatus: 'processing',
  },
];

/**
 * 班组长视角主页组件
 */
const FinalTeamLeaderHomePage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  /**
   * 获取分数颜色样式
   */
  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * 获取质检结果徽章
   */
  const getResultBadge = (result: string) => {
    return result === 'qualified' ? (
      <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">合格</span>
    ) : (
      <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">不合格</span>
    );
  };

  /**
   * 获取申诉状态徽章
   */
  const getAppealStatusBadge = (status: string) => {
    switch (status) {
      case 'processing':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">申诉中</span>;
      case 'success':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">申诉成功</span>;
      case 'failed':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">申诉失败</span>;
      default:
        return null;
    }
  };

  /**
   * 获取趋势图标
   */
  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <span className="w-4 h-4 text-gray-400">—</span>;
    }
  };

  /**
   * 获取排名徽章
   */
  const getRankBadge = (rank: number) => {
    if (rank === 1) {
      return (
        <div className="flex items-center space-x-1">
          <Crown className="w-4 h-4 text-yellow-500" />
          <span className="text-yellow-600 font-bold">#{rank}</span>
        </div>
      );
    } else if (rank <= 3) {
      return (
        <div className="flex items-center space-x-1">
          <Star className="w-4 h-4 text-orange-500" />
          <span className="text-orange-600 font-bold">#{rank}</span>
        </div>
      );
    }
    return <span className="text-gray-600">#{rank}</span>;
  };

  /**
   * 获取通知优先级样式
   */
  const getNotificationPriorityStyle = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high':
        return 'notification-priority-high';
      case 'medium':
        return 'notification-priority-medium';
      case 'low':
        return 'notification-priority-low';
      default:
        return 'notification-priority-medium';
    }
  };

  return (
    <div className="unified-page-container">
      <GlobalStyles />
      <UnifiedPageHeader
        title="班组长视角"
        subtitle={`欢迎回来，李班长 | 客服部-A班组 | 团队成员：${teamPerformanceData.teamMemberCount}人`}
        icon={Users}
        badge={{ text: `排名 #${teamPerformanceData.teamRank}`, color: "blue" }}
        showDesignGuide={true}
        designGuideContent={
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">页面：班组长视角 (FinalTeamLeaderHomePage.tsx) - 业务设计说明</h3>
              <p className="text-gray-700 leading-relaxed">
                该页面是<strong>班组长（Team Leader）</strong>登录后看到的专属仪表盘。其核心目标是为班组长提供管理团队所需的核心数据视图，帮助他们<strong>从团队整体和个体成员两个层面</strong>快速掌握服务质量状况，识别团队的优势与短板，并为团队管理、成员辅导和绩效沟通提供数据支持。
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">1. 页面头部 (UnifiedPageHeader)</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 标题"班组长视角"；显示当前登录班组长的信息及所管理团队信息（如"欢迎回来，李班长 | 客服部-A班组 | 团队成员：12人"）；使用`Users`图标代表团队视角；徽章显示团队关键状态（如"排名 #2"）。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 班组长和团队信息从用户/组织架构服务获取；团队排名数据从团队绩效统计中获取。</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">2. 团队核心绩效指标 (KPIs)</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 团队维度的KPIs，包括团队平均质检得分、团队质检合格率、团队申诉率、团队申诉成功率。每个卡片包含趋势指示和帮助信息。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 数据主要来源于团队质检结果和申诉数据，后端服务进行团队层面的聚合计算。
                    <ul>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>团队平均质检得分</strong>: 汇总团队所有成员在周期内的质检得分并计算平均值。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>团队质检合格率</strong>: 统计团队周期内合格通话占比。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>团队申诉率</strong>: 团队申诉次数占总质检量的比例。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>团队申诉成功率</strong>: 团队申诉成功的次数占总申诉次数的比例。</span>
                      </li>
                    </ul>
                  </span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">3. 整体服务质量趋势与团队成员成绩排名</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 左侧折线图展示团队近期平均分变化趋势，右侧列表展示团队内部成员绩效排名（Top 5），包括排名、姓名、个人表现趋势、个人平均分、合格率、申诉次数等。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 团队趋势数据来源于团队每日质检平均分聚合；成员排名数据则基于成员各自的平均分、合格率等进行统计和排序。</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">4. 团队错误分析统计</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 左侧卡片显示团队严重错误项 Top5，统计团队触犯最多次的"严重"级别规则；右侧卡片显示团队高频失分项 Top5，统计导致团队整体失分最多的规则。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 数据来源于团队质检记录中的扣分项明细和规则违规记录，根据规则的严重性和扣分值进行统计和排序。</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">5. 通知公告与团队近期质检记录</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 左侧卡片展示与班组长及其团队相关的通知（如组员质检结果预警、系统公告）；右侧卡片列出团队成员最近的质检记录，明确标出是哪位坐席的记录，包含质检ID、坐席姓名、质检结果、最终得分、申诉状态等。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 通知数据从系统通知服务获取；质检记录数据从质检结果数据库查询最新记录。点击通知或成绩记录应触发路由跳转到对应的详情页；"查看全部"按钮导航到完整的列表页面。</span>
                </li>
              </ul>
            </div>
          </div>
        }
        actions={[
          // {
          //   label: "本周数据",
          //   icon: Calendar,
          //   variant: "outline",
          //   onClick: () => setSelectedPeriod('week')
          // }
        ]}
      />

      {/* 主要内容区域 */}
      <div className="unified-content-area space-y-6">

      {/* 团队绩效数据概览 */}
      <div className="unified-metric-grid unified-metric-grid-4 unified-section-spacing">
        <MetricCard
          title="团队平均质检得分"
          value={teamPerformanceData.teamAverageScore}
          icon={Award}
          iconColor="text-blue-600"
          iconBgColor="bg-blue-100"
          valueColor="!text-blue-600"
          trend={{
            type: 'up',
            value: '+2.1',
            text: '较上周 +2.1'
          }}
          helpInfo={teamMetricDescriptions.teamAverageScore}
        />

        <MetricCard
          title="团队质检合格率"
          value={`${teamPerformanceData.teamQualificationRate}%`}
          icon={CheckCircle}
          iconColor="text-green-600"
          iconBgColor="bg-green-100"
          valueColor="!text-green-600"
          trend={{
            type: 'up',
            value: '+1.8%',
            text: '较上周 +1.8%'
          }}
          helpInfo={teamMetricDescriptions.teamQualificationRate}
        />

        <MetricCard
          title="团队申诉率"
          value={`${teamPerformanceData.teamAppealRate}%`}
          icon={MessageSquare}
          iconColor="text-orange-600"
          iconBgColor="bg-orange-100"
          valueColor="!text-orange-600"
          trend={{
            type: 'down',
            value: '-0.5%',
            text: '较上周 -0.5%'
          }}
          helpInfo={teamMetricDescriptions.teamAppealRate}
        />

        <MetricCard
          title="团队申诉成功率"
          value={`${teamPerformanceData.teamAppealSuccessRate}%`}
          icon={UserCheck}
          iconColor="text-purple-600"
          iconBgColor="bg-purple-100"
          valueColor="!text-purple-600"
          trend={{
            type: 'up',
            value: '+3.2%',
            text: '较上周 +3.2%'
          }}
          helpInfo={teamMetricDescriptions.teamAppealSuccessRate}
        />
      </div>

      {/* 整体服务质量趋势和团队成员成绩排名 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧两列：整体服务质量趋势 */}
        <div className="lg:col-span-2">
          <Card 
            className="h-full shadow-sm hover:shadow-md transition-shadow duration-200"
            title={
              <Space>
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <span className="font-semibold text-gray-800">成绩趋势分析</span>
              </Space>
            }
          >
            <ResponsiveContainer width="100%" height={320}>
              <LineChart 
                data={teamQualityTrendData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid 
                  strokeDasharray="3 3" 
                  stroke="#e5e7eb" 
                  horizontal={true}
                  vertical={false}
                />
                <XAxis 
                  dataKey="date" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6b7280' }}
                  dy={10}
                />
                <YAxis 
                  domain={[80, 95]} 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6b7280' }}
                  dx={-10}
                />
                <RechartsTooltip 
                  contentStyle={{
                    backgroundColor: '#1f2937',
                    border: 'none',
                    borderRadius: '8px',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                    color: '#ffffff'
                  }}
                  labelStyle={{ color: '#d1d5db' }}
                  formatter={(value: any) => [`${value}分`, '质检得分']}
                  labelFormatter={(label) => `日期: ${label}`}
                />
                <Line 
                  type="monotone" 
                  dataKey="score" 
                  stroke="#3b82f6" 
                  strokeWidth={3}
                  dot={{ 
                    fill: '#3b82f6', 
                    strokeWidth: 2, 
                    stroke: '#ffffff',
                    r: 4,
                    filter: 'drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3))'
                  }}
                  activeDot={{ 
                    r: 6, 
                    fill: '#3b82f6',
                    stroke: '#ffffff',
                    strokeWidth: 3,
                    filter: 'drop-shadow(0 4px 8px rgba(59, 130, 246, 0.4))'
                  }}
                  strokeLinecap="round"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </div>
        
        {/* 右侧一列：团队成员成绩排名 */}
        <div className="lg:col-span-1">
          <Card 
            className="h-full"
            title={
              <Space>
                <Users className="w-5 h-5" />
                <span>团队成员成绩排名</span>
              </Space>
            }
            
          >
            <div className="space-y-3">
              {teamMemberScores.slice(0, 5).map((member) => (
                <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    {getRankBadge(member.rank)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">{member.name}</span>
                        {getTrendIcon(member.trend)}
                      </div>
                      <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                        <span>合格率: {member.qualificationRate}%</span>
                        <span>申诉: {member.appealCount}次</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-lg font-bold ${getScoreColor(member.averageScore)}`}>
                      {member.averageScore}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>

      {/* 团队错误分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 团队严重错误项 */}
        <Card 
          title={
            <Space>
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <span>团队严重错误项 Top5</span>
            </Space>
          }
        >
          <div className="space-y-3">
            {teamSeriousErrors.slice(0, 5).map((error, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <span className="w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                    {index + 1}
                  </span>
                  <span className="text-sm font-medium">{error.name}</span>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                   error.count > 0 ? "bg-red-100 text-red-800" : "bg-green-100 text-green-800"
                 }`}>
                   {error.count} 次
                 </span>
              </div>
            ))}
          </div>
        </Card>

        {/* 团队高频失分项 */}
        <Card 
          title={
            <Space>
              <XCircle className="w-5 h-5 text-orange-500" />
              <span>团队高频失分项 Top5</span>
            </Space>
          }
        >
          <div className="space-y-3">
            {teamFrequentDeductions.slice(0, 5).map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <span className="w-6 h-6 bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                    {index + 1}
                  </span>
                  <span className="text-sm font-medium">{item.name}</span>
                </div>
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-800">
                  {item.count} 次
                </span>
              </div>
            ))}
          </div>
        </Card>
      </div>



      {/* 通知公告和团队近期成绩 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 通知公告 */}
        <Card 
          title={
            <Space>
              <Bell className="w-5 h-5" />
              <span>通知公告</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <div className="space-y-3">
            {notifications.slice(0, 4).map((notification) => (
              <div 
                key={notification.id} 
                className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${
                  getNotificationPriorityStyle(notification.priority)
                }`}
              >
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  notification.isRead ? 'bg-gray-300' : 'bg-blue-500'
                }`} />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium">{notification.title}</p>
                    {notification.priority === 'high' && (
                      <Badge count="重要" style={{ backgroundColor: '#f5222d' }} />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{notification.content}</p>
                  <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* 近期质检记录 */}
        <Card 
          title={
            <Space>
              <BarChart3 className="w-5 h-5" />
              <span>近期质检记录</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <div className="space-y-3">
            {teamMemberRecentScores.slice(0, 5).map((record) => (
              <div key={record.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{record.id}</span>
                      {getResultBadge(record.result)}
                      {getAppealStatusBadge(record.appealStatus)}
                    </div>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                    <span>{record.agentName}</span>
                    <span className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {record.callTime}
                    </span>
                        <span>{record.duration}</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-lg font-bold ${getScoreColor(record.score)}`}>
                    {record.score}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
       </div>
     </div>
   );
 };

 export default FinalTeamLeaderHomePage;