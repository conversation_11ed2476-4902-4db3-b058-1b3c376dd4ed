import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon, TrendingUp, TrendingDown, Activity } from 'lucide-react';

interface EnhancedKPICardProps {
    title: string;
    value: string | number;
    unit?: string;
    trend?: 'up' | 'down' | 'stable';
    trendValue?: string;
    icon: LucideIcon;
    gradient: string;
    description?: string;
    delay?: number;
    onClick?: () => void;
}

/**
 * Enhanced KPI Card Component
 * Provides beautiful, consistent KPI cards with animations and modern design
 */
export const EnhancedKPICard: React.FC<EnhancedKPICardProps> = ({ 
    title, 
    value, 
    unit, 
    trend, 
    trendValue, 
    icon: Icon, 
    gradient,
    description,
    delay = 0,
    onClick
}) => {
    const getTrendIcon = () => {
        switch (trend) {
            case 'up':
                return <TrendingUp className="w-4 h-4 text-emerald-500" />;
            case 'down':
                return <TrendingDown className="w-4 h-4 text-rose-500" />;
            default:
                return <Activity className="w-4 h-4 text-slate-400" />;
        }
    };

    const getTrendColor = () => {
        switch (trend) {
            case 'up':
                return 'text-emerald-600 bg-emerald-50 border-emerald-200';
            case 'down':
                return 'text-rose-600 bg-rose-50 border-rose-200';
            default:
                return 'text-slate-500 bg-slate-50 border-slate-200';
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ 
                duration: 0.5, 
                delay,
                type: "spring",
                stiffness: 100
            }}
            whileHover={{ 
                y: -8, 
                scale: 1.02,
                transition: { duration: 0.2 }
            }}
            onClick={onClick}
            className={`group relative bg-white rounded-2xl shadow-sm border border-gray-200/60 p-6 hover:shadow-xl hover:shadow-gray-200/40 transition-all duration-300 overflow-hidden ${onClick ? 'cursor-pointer' : ''}`}
        >
            {/* Background Gradient Decoration */}
            <div className={`absolute -top-8 -right-8 w-32 h-32 ${gradient} rounded-full opacity-10 transition-all duration-700 group-hover:scale-125 group-hover:opacity-20 blur-2xl`}></div>
            <div className={`absolute -bottom-4 -left-4 w-20 h-20 ${gradient} rounded-full opacity-5 transition-all duration-700 group-hover:scale-150 group-hover:opacity-15 blur-xl`}></div>
            
            {/* Subtle Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>
            
            <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                    <motion.div 
                        className={`p-3 ${gradient} rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300`}
                        whileHover={{ 
                            rotate: [0, -10, 10, 0],
                            scale: 1.15
                        }}
                        transition={{ duration: 0.5 }}
                    >
                        <Icon className="w-6 h-6 text-white" />
                    </motion.div>
                    {trend && (
                        <motion.div 
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: delay + 0.2 }}
                            className={`flex items-center gap-1.5 px-3 py-1.5 rounded-xl border ${getTrendColor()}`}
                        >
                            {getTrendIcon()}
                            <span className="text-sm font-semibold">
                                {trendValue}
                            </span>
                        </motion.div>
                    )}
                </div>
                
                <div className="space-y-3">
                    <h3 className="text-sm font-medium text-slate-600 group-hover:text-slate-700 transition-colors duration-300">
                        {title}
                    </h3>
                    <div className="flex items-baseline gap-2">
                        <motion.span 
                            className="text-3xl font-bold text-slate-900 tracking-tight"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: delay + 0.3 }}
                        >
                            {value}
                        </motion.span>
                        {unit && (
                            <span className="text-sm text-slate-500 font-medium">
                                {unit}
                            </span>
                        )}
                    </div>
                    {description && (
                        <motion.p 
                            className="text-xs text-slate-500 leading-relaxed"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: delay + 0.4 }}
                        >
                            {description}
                        </motion.p>
                    )}
                </div>
            </div>
            
            {/* Hover Effect Border */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>
        </motion.div>
    );
};

export default EnhancedKPICard;
