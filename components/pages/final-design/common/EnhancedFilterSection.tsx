import React from 'react';
import { motion } from 'framer-motion';
import { Search, ChevronDown } from 'lucide-react';

interface FilterOption {
    value: string;
    label: string;
}

interface FilterField {
    key: string;
    label: string;
    type: 'select' | 'text' | 'date';
    value: string;
    options?: FilterOption[];
    placeholder?: string;
    onChange: (value: string) => void;
}

interface EnhancedFilterSectionProps {
    fields: FilterField[];
    onSearch: () => void;
    onReset?: () => void;
    className?: string;
}

/**
 * Enhanced Filter Section Component
 * Provides beautiful, consistent filter sections across all statistical pages
 */
export const EnhancedFilterSection: React.FC<EnhancedFilterSectionProps> = ({
    fields,
    onSearch,
    onReset,
    className = ''
}) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className={`bg-white/95 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200/60 p-6 hover:shadow-lg hover:shadow-slate-200/50 transition-all duration-300 ${className}`}
        >
            <div className="flex flex-wrap items-end gap-4">
                {fields.map((field, index) => (
                    <motion.div
                        key={field.key}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1, duration: 0.3 }}
                        className="flex-1 min-w-[160px]"
                    >
                        <label className="block text-xs font-medium text-slate-600 mb-2">
                            {field.label}
                        </label>
                        {field.type === 'select' ? (
                            <div className="relative">
                                <select
                                    value={field.value}
                                    onChange={(e) => field.onChange(e.target.value)}
                                    className="w-full px-4 py-2.5 text-sm border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white text-slate-700 transition-all duration-200 hover:border-slate-400 hover:shadow-sm"
                                >
                                    {field.options?.map((option) => (
                                        <option key={option.value} value={option.value}>
                                            {option.label}
                                        </option>
                                    ))}
                                </select>
                                <ChevronDown className="absolute right-3 top-3 w-4 h-4 text-slate-400 pointer-events-none" />
                            </div>
                        ) : (
                            <input
                                type={field.type}
                                value={field.value}
                                onChange={(e) => field.onChange(e.target.value)}
                                placeholder={field.placeholder}
                                className="w-full px-4 py-2.5 text-sm border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-slate-700 transition-all duration-200 hover:border-slate-400 hover:shadow-sm"
                            />
                        )}
                    </motion.div>
                ))}
                
                <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: fields.length * 0.1 + 0.2 }}
                    className="flex items-center gap-3"
                >
                    {onReset && (
                        <motion.button 
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={onReset}
                            className="px-6 py-2.5 text-sm font-medium text-slate-600 bg-white border border-slate-300 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 shadow-sm hover:shadow-md"
                        >
                            重置
                        </motion.button>
                    )}
                    <motion.button 
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={onSearch}
                        className="flex items-center gap-2 px-6 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                        <Search className="w-4 h-4" />
                        查询
                    </motion.button>
                </motion.div>
            </div>
        </motion.div>
    );
};

export default EnhancedFilterSection;
