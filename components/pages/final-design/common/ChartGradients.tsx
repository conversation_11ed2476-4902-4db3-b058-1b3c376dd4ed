import React from 'react';

/**
 * 图表渐变定义组件
 * 提供统一的SVG渐变定义，用于所有图表
 */
export const ChartGradients: React.FC = () => {
  return (
    <defs>
      {/* 主要颜色渐变 */}
      <linearGradient id="blueGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.9} />
        <stop offset="100%" stopColor="#1d4ed8" stopOpacity={0.6} />
      </linearGradient>

      <linearGradient id="emeraldGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#10b981" stopOpacity={0.9} />
        <stop offset="100%" stopColor="#047857" stopOpacity={0.6} />
      </linearGradient>

      <linearGradient id="purpleGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#8b5cf6" stopOpacity={0.9} />
        <stop offset="100%" stopColor="#7c3aed" stopOpacity={0.6} />
      </linearGradient>

      <linearGradient id="orangeGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#f97316" stopOpacity={0.9} />
        <stop offset="100%" stopColor="#ea580c" stopOpacity={0.6} />
      </linearGradient>

      <linearGradient id="pinkGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#ec4899" stopOpacity={0.9} />
        <stop offset="100%" stopColor="#db2777" stopOpacity={0.6} />
      </linearGradient>

      <linearGradient id="tealGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#14b8a6" stopOpacity={0.9} />
        <stop offset="100%" stopColor="#0f766e" stopOpacity={0.6} />
      </linearGradient>

      <linearGradient id="indigoGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#6366f1" stopOpacity={0.9} />
        <stop offset="100%" stopColor="#4f46e5" stopOpacity={0.6} />
      </linearGradient>

      <linearGradient id="amberGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#f59e0b" stopOpacity={0.9} />
        <stop offset="100%" stopColor="#d97706" stopOpacity={0.6} />
      </linearGradient>

      {/* 面积图渐变 */}
      <linearGradient id="areaBlueGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.4} />
        <stop offset="100%" stopColor="#3b82f6" stopOpacity={0.1} />
      </linearGradient>

      <linearGradient id="areaEmeraldGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#10b981" stopOpacity={0.4} />
        <stop offset="100%" stopColor="#10b981" stopOpacity={0.1} />
      </linearGradient>

      <linearGradient id="areaPurpleGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#8b5cf6" stopOpacity={0.4} />
        <stop offset="100%" stopColor="#8b5cf6" stopOpacity={0.1} />
      </linearGradient>

      <linearGradient id="areaOrangeGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stopColor="#f97316" stopOpacity={0.4} />
        <stop offset="100%" stopColor="#f97316" stopOpacity={0.1} />
      </linearGradient>

      {/* 径向渐变（用于饼图等） */}
      <radialGradient id="radialBlueGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#60a5fa" stopOpacity={1} />
        <stop offset="100%" stopColor="#3b82f6" stopOpacity={1} />
      </radialGradient>

      <radialGradient id="radialEmeraldGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#34d399" stopOpacity={1} />
        <stop offset="100%" stopColor="#10b981" stopOpacity={1} />
      </radialGradient>

      <radialGradient id="radialPurpleGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#a78bfa" stopOpacity={1} />
        <stop offset="100%" stopColor="#8b5cf6" stopOpacity={1} />
      </radialGradient>

      <radialGradient id="radialOrangeGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#fb923c" stopOpacity={1} />
        <stop offset="100%" stopColor="#f97316" stopOpacity={1} />
      </radialGradient>

      {/* 发光效果滤镜 */}
      <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
        <feMerge> 
          <feMergeNode in="coloredBlur"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>

      {/* 阴影效果滤镜 */}
      <filter id="dropshadow" x="-50%" y="-50%" width="200%" height="200%">
        <feDropShadow dx="0" dy="2" stdDeviation="3" floodColor="#000000" floodOpacity="0.1"/>
      </filter>

      {/* 模糊效果滤镜 */}
      <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur stdDeviation="2"/>
      </filter>

      {/* 高亮效果滤镜 */}
      <filter id="highlight" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
        <feFlood floodColor="#ffffff" floodOpacity="0.3"/>
        <feComposite in="SourceGraphic" in2="coloredBlur" operator="over"/>
      </filter>
    </defs>
  );
};

/**
 * 获取渐变ID的辅助函数
 */
export const getGradientId = (color: string, type: 'linear' | 'area' | 'radial' = 'linear'): string => {
  const prefix = type === 'area' ? 'area' : type === 'radial' ? 'radial' : '';
  return `url(#${prefix}${color}Gradient)`;
};

/**
 * 颜色映射
 */
export const gradientColors = {
  blue: 'blueGradient',
  emerald: 'emeraldGradient',
  purple: 'purpleGradient',
  orange: 'orangeGradient',
  pink: 'pinkGradient',
  teal: 'tealGradient',
  indigo: 'indigoGradient',
  amber: 'amberGradient',
};

export default ChartGradients;
