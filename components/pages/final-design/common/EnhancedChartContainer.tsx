import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { ChartGradients } from './ChartGradients';

interface EnhancedChartContainerProps {
    title: string;
    subtitle?: string;
    icon: LucideIcon;
    iconColor?: string;
    iconBgColor?: string;
    children: React.ReactNode;
    height?: string;
    delay?: number;
    actions?: React.ReactNode;
    className?: string;
    showGradients?: boolean;
    loading?: boolean;
    error?: string;
}

/**
 * Enhanced Chart Container Component
 * Provides consistent styling for chart sections across all statistical pages
 */
export const EnhancedChartContainer: React.FC<EnhancedChartContainerProps> = ({
    title,
    subtitle,
    icon: Icon,
    iconColor = 'text-blue-600',
    iconBgColor = 'bg-gradient-to-br from-blue-100 to-indigo-100',
    children,
    height = 'h-80',
    delay = 0,
    actions,
    className = '',
    showGradients = true,
    loading = false,
    error
}) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
                duration: 0.6,
                delay,
                type: "spring",
                stiffness: 80
            }}
            className={`bg-white/95 backdrop-blur-sm rounded-2xl shadow-sm border border-gray-200/60 p-8 hover:shadow-lg hover:shadow-gray-200/50 transition-all duration-300 relative overflow-hidden ${className}`}
        >
            {/* 背景装饰 */}
            <div className="absolute -top-8 -right-8 w-32 h-32 bg-gradient-to-br from-blue-50 to-purple-50 rounded-full opacity-30 blur-2xl" />
            <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-gradient-to-br from-emerald-50 to-teal-50 rounded-full opacity-20 blur-xl" />

            {/* Header */}
            <div className="flex items-center justify-between mb-8 relative z-10">
                <div className="flex items-center gap-4">
                    <motion.div
                        className={`p-3 ${iconBgColor} rounded-xl shadow-lg relative`}
                        whileHover={{
                            scale: 1.1,
                            rotate: [0, -5, 5, 0]
                        }}
                        transition={{ duration: 0.3 }}
                    >
                        <Icon className={`w-6 h-6 ${iconColor}`} />
                        <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300" />
                    </motion.div>
                    <div>
                        <motion.h3
                            className="text-xl font-bold text-slate-900"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: delay + 0.1 }}
                        >
                            {title}
                        </motion.h3>
                        {subtitle && (
                            <motion.p
                                className="text-sm text-slate-600 mt-1"
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: delay + 0.2 }}
                            >
                                {subtitle}
                            </motion.p>
                        )}
                    </div>
                </div>
                {actions && (
                    <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: delay + 0.3 }}
                    >
                        {actions}
                    </motion.div>
                )}
            </div>

            {/* Chart Content */}
            <motion.div
                className={`${height} relative z-10`}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: delay + 0.4, duration: 0.5 }}
            >
                {loading ? (
                    <div className="flex items-center justify-center h-full">
                        <div className="flex items-center gap-3">
                            <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                            <span className="text-slate-600">加载中...</span>
                        </div>
                    </div>
                ) : error ? (
                    <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                            <div className="text-red-500 text-lg mb-2">⚠️</div>
                            <div className="text-slate-600">{error}</div>
                        </div>
                    </div>
                ) : (
                    <>
                        {showGradients && <ChartGradients />}
                        {children}
                    </>
                )}
            </motion.div>
        </motion.div>
    );
};

export default EnhancedChartContainer;
