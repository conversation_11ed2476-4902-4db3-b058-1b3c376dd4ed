import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
    Phone, Calendar, Play, Pause, Rewind, FastForward, UserCircle, Bot,
    Frown, MicOff, MessageSquare, Target, ChevronLeft, Check, X, CheckCircle, XCircle,
    FileText, Shield, Flag, ClipboardList, Award, Eye, Headphones, Users, Building,
    Timer, AlertCircle, Info, Brain, ExternalLink, RotateCcw, AlertTriangle, Quote
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Tooltip } from '../../common/Tooltip';

// --- Types ---
interface Alert {
    type: 'sentiment' | 'keyword';
    message: string;
    timestamp: number;
    severity: 'high' | 'medium' | 'low';
}

interface BaseTranscriptItem {
    speaker: 'agent' | 'customer' | 'system';
    text: string;
    timestamp: number;
}

interface TranscriptItem extends BaseTranscriptItem {
    id: number;
    sentiment?: 'positive' | 'neutral' | 'negative';
    interrupt?: boolean;
    silence_start?: boolean;
    silence_end?: boolean;
    keyword?: string;
}

interface ScoringRule {
    id: string;
    name: string;
    description: string;
    isHit: boolean;
    score: number;
    timestamp?: number;
}

interface ReviewDetails {
    reviewer: { id: string; name: string };
    timestamp: string;
    notes: string;
}

interface AppealDetails {
    appellant: { id: string; name: string; teamName: string; };
    timestamp: string;
    reason: string;
    status?: 'approved' | 'rejected';
    processor?: { id:string; name: string };
    processedTimestamp?: string;
    processorNotes?: string;
}

interface TaskDetails {
    taskId: string;
    taskName: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed';
    statusReason?: string;
    scopeSummary: string;
    scoringSchemeName: string;
    source: 'manual' | 'from_plan';
    creator: { id: string; name: string };
    creationDate: string;
    dueDate: string;
    baseScore?: number;
    passingScore?: number;
    assignTime?: string;
}

interface SessionData {
    id: string;
    agent: { id: string; name: string; teamName: string; };
    customer: { id: string; phone: string };
    startTime: string;
    duration: number;
    machineScore?: number;
    reviewScore?: number;
    appealScore?: number;
    finalScore: number;
    finalScoreSource: 'machine' | 'review' | 'appeal';
    alerts: Alert[];
    reviewTriggers?: string[];
    transcript: TranscriptItem[];
    scoringRules: ScoringRule[]; // Machine-scored rules
    reviewedScoringRules?: ScoringRule[]; // Manually reviewed rules
    reviewDetails?: ReviewDetails;
    appealDetails?: AppealDetails;
    taskDetails?: TaskDetails;
    appealStatus: 'eligible' | 'in_progress' | 'processed' | 'expired';
    appealDeadline?: string;
}

// --- Mock Data ---
const mockSessionData: SessionData & { transcript: TranscriptItem[] } = {
    id: 'S_A001_01',
    agent: { id: 'A001', name: '王伟', teamName: 'A组' },
    customer: { id: 'C_12345', phone: '138****1234' },
    startTime: '2023-10-27 14:30:05',
    duration: 215, // seconds
    machineScore: 72,
    reviewScore: 80, // Adjusted from 85 to 80 for consistency (100 - 5 - 10 - 5 = 80)
    appealScore: 90,
    finalScore: 90,
    finalScoreSource: 'appeal',
    alerts: [
        { type: 'sentiment', message: '客户情绪激动，评分为-0.8', timestamp: 125, severity: 'high' },
        { type: 'keyword', message: '客户提及关键词 "投诉"', timestamp: 142, severity: 'medium' },
    ],
    reviewTriggers: [
        '随机抽样复核',
        '低分触发复核',
    ],
    appealStatus: 'eligible',
    appealDeadline: '2023-10-30 18:00:00',
    scoringRules: [
        { id: 'RULE-001', name: '标准开场白检测', description: '检测坐席开场白是否符合标准句式。', isHit: true, score: -5, timestamp: 5 },
        { id: 'RULE-002', name: '合规声明-录音告知', description: '验证坐席是否在通话开始时进行了录音告知。', isHit: false, score: 0 },
        { id: 'RULE-003', name: '上下文重复询问', description: '检测坐席是否在短时间内重复询问客户相同问题。', isHit: true, score: -10, timestamp: 28 },
        { id: 'RULE-004', name: '客户情绪-激动模型', description: '使用AI模型判断客户是否存在激动情绪。', isHit: true, score: -13, timestamp: 125 },
        { id: 'RULE-005', name: '客服辱骂检测模型', description: '监控客服在对话中是否存在不文明用语。', isHit: false, score: 0 },
    ],
    reviewedScoringRules: [
        { id: 'RULE-001', name: '标准开场白检测', description: '检测坐席开场白是否符合标准句式。', isHit: true, score: -5, timestamp: 5 },
        { id: 'RULE-002', name: '合规声明-录音告知', description: '验证坐席是否在通话开始时进行了录音告知。', isHit: false, score: 0 },
        { id: 'RULE-003', name: '上下文重复询问', description: '检测坐席是否在短时间内重复询问客户相同问题。', isHit: true, score: -10, timestamp: 28 },
        { id: 'RULE-004', name: '客户情绪-激动模型', description: '使用AI模型判断客户是否存在激动情绪。', isHit: true, score: -5, timestamp: 125 },
        { id: 'RULE-005', name: '客服辱骂检测模型', description: '监控客服在对话中是否存在不文明用语。', isHit: false, score: 0 },
    ],
    reviewDetails: {
        reviewer: { id: 'MGR-002', name: '李经理' },
        timestamp: '2023-10-27 16:10:25',
        notes: '经核实，客户情绪确实激动，但坐席应对基本得体，未激化矛盾。将"客户情绪-激动模型"扣分由-13调整为-5。同时，坐席未能在第一时间有效安抚，酌情保留扣分。'
    },
    appealDetails: {
        appellant: { id: 'A001', name: '王伟', teamName: 'A组' },
        timestamp: '2023-10-28 09:30:15',
        reason: '客户本人情绪非常激动，我已尽力安抚，但客户仍多次打断。我认为情绪模型的-5分扣分过重，非我之过。',
        status: 'approved',
        processor: { id: 'DIR-001', name: '赵总监' },
        processedTimestamp: '2023-10-28 14:00:00',
        processorNotes: '申诉通过。考虑到客户的特殊情况，坐席在受压下仍保持了较高服务水平。本次扣分予以撤销，最终成绩调整为90分。已同步更新AI模型样本。'
    },
    taskDetails: {
        taskId: 'T-20231101-001',
        taskName: '11月营销活动通话质检',
        status: 'in_progress',
        scopeSummary: '坐席组: A组, B组 (等3个) | 通话时间: 2023.11.01-11.10',
        scoringSchemeName: '标准服务流程质检方案',
        source: 'manual',
        creator: { id: 'MGR-001', name: '张主管' },
        creationDate: '2023-11-01 10:30',
        dueDate: '2023-11-30 18:00:00',
        baseScore: 100,
        passingScore: 80,
        assignTime: '2023-10-27 15:00:00'
    },
    transcript: [
        { id: 1, speaker: 'agent', text: '您好，这里是xx银行贷后服务中心，请问是王先生吗？', timestamp: 5 },
        { id: 2, speaker: 'customer', text: '是我，你们怎么又打电话来了？我都说了好几遍了，下个月才还得起！', timestamp: 15 },
        { id: 3, speaker: 'agent', text: '王先生您别激动，我们只是按照规定进行提醒，没有催促您的意思。', timestamp: 28 },
        { id: 4, speaker: 'customer', text: '别跟我说这些！每次都一样的话术！你们这样天天骚扰，我受不了了！', timestamp: 45, sentiment: 'neutral' },
        { id: 5, speaker: 'agent', text: '真的很抱歉给您带来了不好的感受。我们也是希望能够帮助您...', timestamp: 62 },
        { id: 6, speaker: 'customer', text: '帮助？你们就是催债！烦死了！再打电话过来，我就要去银监会投诉你们！', timestamp: 125, sentiment: 'negative' },
        { id: 7, speaker: 'agent', text: '您先消消气，我们绝对没有骚扰您的意思。关于您的困难，我们可以一起看看有没有别的解决办法。', timestamp: 138 },
        { id: 8, speaker: 'customer', text: '别说了，我不想听！总之，下个月之前别再打了！', timestamp: 142, keyword: '投诉' },
        { id: 9, speaker: 'agent', text: '好的好的，王先生，我们尊重您的意见。那我们下个月再联系您。', timestamp: 160 },
        { id: 10, speaker: 'customer', text: '嗯。', timestamp: 175, silence_start: true },
        { id: 11, speaker: 'agent', text: '那祝您生活愉快，再见。', timestamp: 200, silence_end: true },
    ],
};

const EventTag: React.FC<{item: TranscriptItem}> = ({ item }) => {
    if (item.sentiment === 'negative') {
        return (
            <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="mt-3 flex items-center text-xs text-red-700 bg-gradient-to-r from-red-50 to-red-100 px-3 py-1.5 rounded-full w-fit border border-red-200 shadow-sm"
            >
                <Frown className="w-4 h-4 mr-1.5" />
                <span className="font-medium">负面情绪</span>
            </motion.div>
        );
    }
    if (item.keyword) {
        return (
            <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="mt-3 flex items-center text-xs text-orange-700 bg-gradient-to-r from-orange-50 to-orange-100 px-3 py-1.5 rounded-full w-fit border border-orange-200 shadow-sm"
            >
                <Target className="w-4 h-4 mr-1.5" />
                <span className="font-medium">关键词: {item.keyword}</span>
            </motion.div>
        );
    }
    if (item.silence_start) {
        return (
            <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="mt-3 flex items-center text-xs text-yellow-700 bg-gradient-to-r from-yellow-50 to-yellow-100 px-3 py-1.5 rounded-full w-fit border border-yellow-200 shadow-sm"
            >
                <MicOff className="w-4 h-4 mr-1.5" />
                <span className="font-medium">长时静默</span>
            </motion.div>
        );
    }
    return null;
}

// --- Score Panel Component ---
interface ScorePanelProps {
    machineScore?: number;
    reviewScore?: number;
    appealScore?: number;
    finalScore: number;
    finalScoreSource: 'machine' | 'review' | 'appeal';
    passingScore?: number;
}

const ScorePanel: React.FC<ScorePanelProps> = ({ machineScore, reviewScore, appealScore, finalScore, finalScoreSource, passingScore = 80 }) => {
    
    const getSourceLabel = (source: typeof finalScoreSource) => {
        switch (source) {
            case 'appeal': return '申诉裁定';
            case 'review': return '人工复核';
            case 'machine': default: return 'AI初检';
        }
    };

    const sourceLabel = getSourceLabel(finalScoreSource);
    const sourceColorClass = {
        appeal: 'bg-green-100 text-green-800 border-green-200',
        review: 'bg-indigo-100 text-indigo-800 border-indigo-200',
        machine: 'bg-gray-100 text-gray-800 border-gray-200',
    }[finalScoreSource];

    const isStepActive = (step: 'machine' | 'review' | 'appeal') => {
        if (finalScoreSource === 'appeal') return true;
        if (finalScoreSource === 'review') return step !== 'appeal';
        if (finalScoreSource === 'machine') return step === 'machine';
        return false;
    };

    const isStepFinal = (step: 'machine' | 'review' | 'appeal') => step === finalScoreSource;

    // 判断是否合格
    const isPassing = finalScore >= passingScore;
    const qualityStatus = isPassing ? '合格' : '不合格';
    const qualityColorClass = isPassing 
        ? 'bg-green-100 text-green-800 border-green-200' 
        : 'bg-red-100 text-red-800 border-red-200';

    // 计算环形进度条
    const circumference = 2 * Math.PI * 45;
    const strokeDashoffset = circumference - (finalScore / 100) * circumference;

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 p-8 rounded-2xl shadow-xl border border-gray-200/50 relative overflow-hidden"
        >
            {/* 背景装饰 */}
            <div className="absolute top-0 right-0 w-48 h-48 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full opacity-20 transform translate-x-24 -translate-y-24"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-green-100 to-blue-100 rounded-full opacity-20 transform -translate-x-16 translate-y-16"></div>

            {/* 环形进度条和得分 */}
            <div className="text-center relative z-10 mb-8">
                <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 150 }}
                    className="relative inline-block"
                >
                    <svg className="w-40 h-40 transform -rotate-90" viewBox="0 0 100 100">
                        {/* 背景圆环 */}
                        <circle
                            cx="50"
                            cy="50"
                            r="45"
                            stroke="#e5e7eb"
                            strokeWidth="8"
                            fill="none"
                        />
                        {/* 进度圆环 */}
                        <motion.circle
                            cx="50"
                            cy="50"
                            r="45"
                            stroke={isPassing ? "url(#greenGradient)" : "url(#redGradient)"}
                            strokeWidth="8"
                            fill="none"
                            strokeLinecap="round"
                            strokeDasharray={circumference}
                            strokeDashoffset={strokeDashoffset}
                            initial={{ strokeDashoffset: circumference }}
                            animate={{ strokeDashoffset }}
                            transition={{ duration: 1.5, ease: "easeOut", delay: 0.3 }}
                        />
                        {/* 渐变定义 */}
                        <defs>
                            <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stopColor="#10b981" />
                                <stop offset="100%" stopColor="#059669" />
                            </linearGradient>
                            <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" stopColor="#ef4444" />
                                <stop offset="100%" stopColor="#dc2626" />
                            </linearGradient>
                        </defs>
                    </svg>
                    
                    {/* 中心内容 */}
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                        <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.8, type: "spring", stiffness: 200 }}
                            className="text-4xl font-bold text-gray-800"
                        >
                            {finalScore}
                        </motion.div>
                        <div className="text-sm text-gray-600 font-medium">分</div>
                    </div>
                </motion.div>

                {/* 状态图标 */}
                <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 }}
                    className="absolute -top-2 -right-2"
                >
                    {isPassing ? (
                        <motion.div
                            initial={{ scale: 0, rotate: -180 }}
                            animate={{ scale: 1, rotate: 0 }}
                            transition={{ delay: 1.2, type: "spring", stiffness: 200 }}
                        >
                            <CheckCircle className="w-10 h-10 text-green-500" />
                        </motion.div>
                    ) : (
                        <motion.div
                            initial={{ scale: 0, rotate: 180 }}
                            animate={{ scale: 1, rotate: 0 }}
                            transition={{ delay: 1.2, type: "spring", stiffness: 200 }}
                        >
                            <AlertCircle className="w-10 h-10 text-red-500" />
                        </motion.div>
                    )}
                </motion.div>

                {/* 状态标签 */}
                <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.1 }}
                    className="mt-6 flex flex-col items-center gap-3"
                >
                    <div className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-bold rounded-full border-2 ${qualityColorClass} shadow-sm`}>
                        {isPassing ? <CheckCircle className="w-4 h-4" /> : <AlertCircle className="w-4 h-4" />}
                        {qualityStatus}
                    </div>
                    <div className={`inline-flex items-center gap-2 px-3 py-1 text-xs font-bold rounded-full ${sourceColorClass}`}>
                        <Award className="w-3 h-3" />
                        {sourceLabel}
                    </div>
                    <div className="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                        合格分数线: {passingScore}分
                    </div>
                </motion.div>
            </div>

            {/* 分数演进流程 - 重新设计 */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="mt-8 relative z-10"
            >
                <h4 className="text-lg font-bold text-gray-700 mb-6 text-center">分数演进流程</h4>
                <div className="relative">
                    {/* 连接线背景 */}
                    <div className="absolute top-8 left-0 right-0 h-1 bg-gray-200 rounded-full">
                        <motion.div
                            initial={{ width: 0 }}
                            animate={{ width: `${((['machine', 'review', 'appeal'].indexOf(finalScoreSource) + 1) / 3) * 100}%` }}
                            transition={{ duration: 1.2, delay: 0.8 }}
                            className="h-full bg-gradient-to-r from-blue-400 via-indigo-400 to-green-400 rounded-full"
                        />
                    </div>

                    <div className="relative flex justify-between items-start">
                        {/* AI初检 */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.8 }}
                            className="flex flex-col items-center"
                        >
                            <motion.div
                                whileHover={{ scale: 1.1 }}
                                className={`w-16 h-16 rounded-full flex items-center justify-center border-4 shadow-xl transition-all duration-300 ${
                                    finalScoreSource === 'machine'
                                        ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white border-blue-400 shadow-blue-200/50'
                                        : isStepActive('machine')
                                        ? 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-700 border-blue-300'
                                        : 'bg-gray-100 text-gray-500 border-gray-300'
                                }`}
                            >
                                <div className="text-center">
                                    <div className="text-lg font-bold">{machineScore ?? 'N/A'}</div>
                                    <Brain className="w-5 h-5 mx-auto" />
                                </div>
                            </motion.div>
                            <div className="mt-2 text-center">
                                <p className="text-sm font-bold text-gray-700">AI初检</p>
                                <p className="text-xs text-gray-500">智能分析</p>
                            </div>
                        </motion.div>

                        {/* 人工复核 */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 1 }}
                            className="flex flex-col items-center"
                        >
                            <motion.div
                                whileHover={{ scale: 1.1 }}
                                className={`w-16 h-16 rounded-full flex items-center justify-center border-4 shadow-xl transition-all duration-300 ${
                                    finalScoreSource === 'review'
                                        ? 'bg-gradient-to-br from-indigo-500 to-purple-600 text-white border-indigo-400 shadow-indigo-200/50'
                                        : isStepActive('review')
                                        ? 'bg-gradient-to-br from-indigo-100 to-purple-200 text-indigo-700 border-indigo-300'
                                        : 'bg-gray-100 text-gray-500 border-gray-300'
                                }`}
                            >
                                <div className="text-center">
                                    <div className="text-lg font-bold">{reviewScore ?? 'N/A'}</div>
                                    <Eye className="w-5 h-5 mx-auto" />
                                </div>
                            </motion.div>
                            <div className="mt-2 text-center">
                                <p className="text-sm font-bold text-gray-700">人工复核</p>
                                <p className="text-xs text-gray-500">专业审核</p>
                            </div>
                        </motion.div>

                        {/* 申诉裁定 */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 1.2 }}
                            className="flex flex-col items-center"
                        >
                            <motion.div
                                whileHover={{ scale: 1.1 }}
                                className={`w-16 h-16 rounded-full flex items-center justify-center border-4 shadow-xl transition-all duration-300 ${
                                    finalScoreSource === 'appeal'
                                        ? 'bg-gradient-to-br from-green-500 to-emerald-600 text-white border-green-400 shadow-green-200/50'
                                        : isStepActive('appeal')
                                        ? 'bg-gradient-to-br from-green-100 to-emerald-200 text-green-700 border-green-300'
                                        : 'bg-gray-100 text-gray-500 border-gray-300'
                                }`}
                            >
                                <div className="text-center">
                                    <div className="text-lg font-bold">{appealScore ?? 'N/A'}</div>
                                    <Shield className="w-5 h-5 mx-auto" />
                                </div>
                            </motion.div>
                            <div className="mt-2 text-center">
                                <p className="text-sm font-bold text-gray-700">申诉裁定</p>
                                <p className="text-xs text-gray-500">最终裁决</p>
                            </div>
                        </motion.div>
                    </div>

                    {/* 当前状态指示 */}
                    <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 1.4 }}
                        className="mt-6 flex justify-center"
                    >
                        <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-bold ${
                            finalScoreSource === 'machine' ? 'bg-blue-100 text-blue-700' :
                            finalScoreSource === 'review' ? 'bg-indigo-100 text-indigo-700' :
                            'bg-green-100 text-green-700'
                        }`}>
                            {finalScoreSource === 'machine' && <Brain className="w-4 h-4" />}
                            {finalScoreSource === 'review' && <Eye className="w-4 h-4" />}
                            {finalScoreSource === 'appeal' && <Shield className="w-4 h-4" />}
                            当前分数来源：{sourceLabel}
                        </div>
                    </motion.div>
                </div>

                {/* 分数变化总结 */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.5 }}
                    className="mt-6 p-4 bg-gradient-to-r from-gray-100 to-blue-100 rounded-xl border border-gray-200/50"
                >
                    <div className="flex items-center gap-3">
                        <Info className="w-5 h-5 text-blue-600" />
                        <div>
                            <p className="text-sm font-bold text-gray-800">
                                最终分数：<span className={`${isPassing ? 'text-green-600' : 'text-red-600'}`}>{finalScore}分 / {passingScore}分</span>
                            </p>
                            <p className="text-xs text-gray-600">
                                经过{finalScoreSource === 'machine' ? 'AI初检' : 
                                     finalScoreSource === 'review' ? '人工复核' : '申诉裁定'}，最终得分为{finalScore}分
                            </p>
                        </div>
                    </div>
                </motion.div>
            </div>
        </motion.div>
    );
};

// --- Sub-components ---
interface AudioPlayerProps {
    duration: number;
    currentTime: number;
    setCurrentTime: React.Dispatch<React.SetStateAction<number>>;
    isPlaying: boolean;
    setIsPlaying: React.Dispatch<React.SetStateAction<boolean>>;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ duration, currentTime, setCurrentTime, isPlaying, setIsPlaying }) => {
    const progressRef = useRef<HTMLDivElement>(null);
    const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
        if (!progressRef.current) return;
        const rect = progressRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const newTime = (x / rect.width) * duration;
        setCurrentTime(newTime);
    };

    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl p-6 border border-gray-200 shadow-sm"
        >
            {/* 音频控制区域 */}
            <div className="flex items-center justify-center gap-8 mb-6">
                <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setCurrentTime(Math.max(0, currentTime - 10))}
                    className="w-12 h-12 bg-white text-gray-600 rounded-full flex items-center justify-center shadow-md hover:text-blue-600 hover:shadow-lg transition-all duration-200"
                >
                    <Rewind className="w-5 h-5" />
                </motion.button>

                <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center shadow-xl hover:shadow-2xl transition-all duration-300"
                >
                    <AnimatePresence mode="wait">
                        {isPlaying ? (
                            <motion.div
                                key="pause"
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                exit={{ scale: 0.8, opacity: 0 }}
                                transition={{ duration: 0.2 }}
                            >
                                <Pause size={36} />
                            </motion.div>
                        ) : (
                            <motion.div
                                key="play"
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                exit={{ scale: 0.8, opacity: 0 }}
                                transition={{ duration: 0.2 }}
                                className="ml-1"
                            >
                                <Play size={36} />
                            </motion.div>
                        )}
                    </AnimatePresence>
                </motion.button>

                <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setCurrentTime(Math.min(duration, currentTime + 10))}
                    className="w-12 h-12 bg-white text-gray-600 rounded-full flex items-center justify-center shadow-md hover:text-blue-600 hover:shadow-lg transition-all duration-200"
                >
                    <FastForward className="w-5 h-5" />
                </motion.button>
            </div>

            {/* 进度条区域 */}
            <div className="space-y-3">
                <div className="flex items-center gap-4 text-sm">
                    <span className="font-mono text-gray-700 bg-white px-2 py-1 rounded-md shadow-sm min-w-[3rem] text-center">
                        {formatTime(currentTime)}
                    </span>
                    <div
                        ref={progressRef}
                        onClick={handleProgressClick}
                        className="flex-1 h-3 bg-white rounded-full cursor-pointer shadow-inner border border-gray-200 relative overflow-hidden"
                    >
                        <motion.div
                            className="h-full bg-gradient-to-r from-blue-400 to-blue-500 rounded-full relative"
                            style={{ width: `${(currentTime / duration) * 100}%` }}
                            transition={{ type: "spring", stiffness: 300, damping: 30 }}
                        >
                            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-md border-2 border-blue-500"></div>
                        </motion.div>
                    </div>
                    <span className="font-mono text-gray-700 bg-white px-2 py-1 rounded-md shadow-sm min-w-[3rem] text-center">
                        {formatTime(duration)}
                    </span>
                </div>

                {/* 播放状态指示 */}
                <div className="flex items-center justify-center gap-2 text-xs text-gray-600">
                    <Headphones className="w-4 h-4" />
                    <span>{isPlaying ? '正在播放' : '已暂停'}</span>
                    <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
                </div>
            </div>
        </motion.div>
    );
};

interface TranscriptPanelProps {
    transcript: TranscriptItem[];
    currentTime: number;
    setCurrentTime: React.Dispatch<React.SetStateAction<number>>;
}

const TranscriptPanel: React.FC<TranscriptPanelProps> = ({ transcript, currentTime, setCurrentTime }) => {
    const activeRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (activeRef.current) {
            activeRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }, [currentTime]);

    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <div className="h-[125vh] overflow-y-auto pr-2 space-y-3 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            {transcript.map((item, i) => {
                const isAgent = item.speaker === 'agent';
                const isActive = currentTime >= item.timestamp && (transcript[i + 1] ? currentTime < transcript[i + 1].timestamp : true);

                return (
                    <motion.div
                        key={i}
                        ref={isActive ? activeRef : null}
                        onClick={() => setCurrentTime(item.timestamp)}
                        initial={{ opacity: 0, x: isAgent ? -20 : 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: i * 0.05 }}
                        whileHover={{ scale: 1.02 }}
                        className={`flex gap-4 cursor-pointer p-4 rounded-xl transition-all duration-300 border-2 ${
                            isActive
                                ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 shadow-md'
                                : 'bg-white border-gray-100 hover:border-gray-200 hover:shadow-sm'
                        }`}
                    >
                        {/* 头像区域 */}
                        <div className="flex-shrink-0">
                            <motion.div
                                className={`w-10 h-10 rounded-full flex items-center justify-center shadow-sm ${
                                    isAgent
                                        ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white'
                                        : 'bg-gradient-to-br from-green-500 to-green-600 text-white'
                                }`}
                                whileHover={{ scale: 1.1 }}
                            >
                                {isAgent ? <Bot className="w-5 h-5" /> : <UserCircle className="w-5 h-5" />}
                            </motion.div>
                        </div>

                        {/* 内容区域 */}
                        <div className="flex-1 min-w-0">
                            <div className="flex justify-between items-center mb-2">
                                <div className="flex items-center gap-2">
                                    <span className={`font-bold text-sm ${
                                        isAgent ? 'text-blue-700' : 'text-green-700'
                                    }`}>
                                        {isAgent ? '坐席' : '客户'}
                                    </span>
                                    {isActive && (
                                        <motion.div
                                            initial={{ scale: 0 }}
                                            animate={{ scale: 1 }}
                                            className="flex items-center gap-1 px-2 py-0.5 bg-blue-500 text-white text-xs rounded-full"
                                        >
                                            <div className="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                            正在播放
                                        </motion.div>
                                    )}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Timer className="w-3 h-3 text-gray-400" />
                                    <span className="text-xs text-gray-500 font-mono bg-gray-100 px-2 py-1 rounded-md">
                                        {formatTime(item.timestamp)}
                                    </span>
                                </div>
                            </div>

                            <motion.p
                                className={`text-gray-800 leading-relaxed ${isActive ? 'font-medium' : ''}`}
                                animate={{
                                    color: isActive ? '#1f2937' : '#374151',
                                    fontWeight: isActive ? 500 : 400
                                }}
                            >
                                {item.text}
                            </motion.p>

                            <EventTag item={item} />
                        </div>
                    </motion.div>
                );
            })}
        </div>
    );
};

interface ReviewPanelProps {
    machineScore?: number;
    reviewScore: number;
    onSubmit: (notes: string) => void;
    onCancel: () => void;
}

const ReviewPanel: React.FC<ReviewPanelProps> = ({ machineScore, reviewScore, onSubmit, onCancel }) => {
  const [reviewNotes, setReviewNotes] = useState('');
  
  const handleSubmit = () => {
    onSubmit(reviewNotes);
  }

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <h2 className="text-xl font-bold text-gray-800 mb-4">复核操作</h2>
      <div className="space-y-4">
        {machineScore !== undefined && (
            <div>
                <label className="text-sm font-medium text-gray-500">AI初检得分</label>
                <div className="mt-1 p-3 bg-gray-100 rounded-lg text-lg font-bold text-gray-800">{machineScore}</div>
            </div>
        )}
        <div>
            <label className="text-sm font-medium text-gray-500">复核后得分</label>
            <div className="mt-1 p-3 bg-blue-50 border border-blue-200 rounded-lg text-2xl font-bold text-blue-600">{reviewScore}</div>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-700" htmlFor="review-notes">复核意见</label>
          <textarea id="review-notes" value={reviewNotes} onChange={(e) => setReviewNotes(e.target.value)} rows={4} placeholder="输入详细的复核意见或备注..." className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
        </div>
        <div className="flex justify-end gap-3 pt-2">
            <button onClick={onCancel} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">取消</button>
            <button onClick={handleSubmit} className="px-4 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">提交复核结果</button>
        </div>
      </div>
    </div>
  );
}

interface AppealPanelProps {
    scoreToAppeal: number;
    scoreType: 'machine' | 'review';
    onSubmit: (decision: 'approve' | 'reject', newScore: number | null, notes: string) => void;
    onCancel: () => void;
}
  
const AppealPanel: React.FC<AppealPanelProps> = ({ scoreToAppeal, scoreType, onSubmit, onCancel }) => {
    const [decision, setDecision] = useState<'approve' | 'reject' | null>(null);
    const [notes, setNotes] = useState('');
    const [newScore, setNewScore] = useState<number | null>(null);
    const scoreLabel = scoreType === 'review' ? '复核后得分' : 'AI初检得分';

    const handleSubmit = () => {
        if (!decision) return;
        onSubmit(decision, newScore, notes);
    };

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h2 className="text-xl font-bold text-gray-800 mb-4">申诉处理</h2>
            <div className="space-y-4">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-sm font-semibold text-yellow-800">坐席申诉理由：</p>
                    <p className="mt-1 text-sm text-yellow-900">"客户实际已经表达了满意的意愿，但模型误判为负面情绪，导致扣分，申请核实。"</p>
                </div>
                <div>
                    <label className="text-sm font-medium text-gray-500">{scoreLabel}</label>
                    <div className="mt-1 p-3 bg-gray-100 rounded-lg text-lg font-bold text-gray-800">{scoreToAppeal}</div>
                </div>
                <div>
                    <span className="text-sm font-medium text-gray-700">处理决定</span>
                    <div className="mt-2 grid grid-cols-2 gap-3">
                        <button onClick={() => setDecision('approve')} className={`px-4 py-2 text-sm font-semibold rounded-lg border-2 transition-colors ${decision === 'approve' ? 'bg-green-600 text-white border-green-600' : 'bg-white text-green-700 border-gray-300 hover:bg-green-50'}`}>同意申诉</button>
                        <button onClick={() => setDecision('reject')} className={`px-4 py-2 text-sm font-semibold rounded-lg border-2 transition-colors ${decision === 'reject' ? 'bg-red-600 text-white border-red-600' : 'bg-white text-red-700 border-gray-300 hover:bg-red-50'}`}>驳回申诉</button>
                    </div>
                </div>
                {decision === 'approve' && (
                    <div>
                        <label className="text-sm font-medium text-gray-700">调整后分数</label>
                        <div className="mt-2">
                            <input
                                type="number"
                                min="0"
                                max="100"
                                value={newScore ?? ''}
                                onChange={(e) => setNewScore(e.target.value ? Number(e.target.value) : null)}
                                placeholder="输入新的分数"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg font-bold text-blue-600"
                            />
                        </div>
                        <p className="mt-1 text-xs text-gray-500">请输入0-100之间的分数</p>
                    </div>
                )}
                <div>
                    <label className="text-sm font-medium text-gray-700" htmlFor="appeal-notes">处理意见</label>
                    <textarea id="appeal-notes" value={notes} onChange={(e) => setNotes(e.target.value)} rows={3} placeholder="输入处理说明..." className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
                </div>
                <div className="flex justify-end gap-3 pt-2">
                    <button onClick={onCancel} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">取消</button>
                    <button
                        onClick={handleSubmit}
                        className={`px-6 py-2 text-sm font-semibold text-white rounded-lg transition-colors ${
                            !decision || (decision === 'approve' && newScore === null)
                                ? 'bg-gray-400 cursor-not-allowed'
                                : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                        disabled={!decision || (decision === 'approve' && newScore === null)}
                    >
                        提交处理结果
                    </button>
                </div>
            </div>
        </div>
    );
}

interface PerformanceCheckPanelProps {
    appealStatus: SessionData['appealStatus'];
    onInitiateAppeal: () => void;
}

const PerformanceCheckPanel: React.FC<PerformanceCheckPanelProps> = ({ appealStatus, onInitiateAppeal }) => {
    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-bold text-gray-800">申诉操作</h3>
            <p className="mt-2 text-sm text-gray-500">回顾本次质检，并采取下一步行动。</p>
            <div className="mt-4 space-y-3">
                {appealStatus === 'eligible' && (
                    <button 
                        onClick={onInitiateAppeal}
                        className="w-full px-4 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
                        发起申诉
                    </button>
                )}
            </div>
        </div>
    );
};

interface InitiateAppealPanelProps {
    onSubmit: (reason: string) => void;
    onCancel: () => void;
}

const InitiateAppealPanel: React.FC<InitiateAppealPanelProps> = ({ onSubmit, onCancel }) => {
    const [reason, setReason] = useState('');
    
    const handleSubmit = () => {
        if (!reason.trim()) {
            alert('申诉理由不能为空');
            return;
        }
        onSubmit(reason);
    }

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h2 className="text-xl font-bold text-gray-800 mb-4">发起申诉</h2>
            <div className="space-y-4">
                <div>
                    <label className="text-sm font-medium text-gray-700" htmlFor="appeal-reason">申诉理由</label>
                    <textarea 
                        id="appeal-reason" 
                        value={reason} 
                        onChange={(e) => setReason(e.target.value)} 
                        rows={5} 
                        placeholder="请详细填写您的申诉理由，例如说明您认为评分不准确的具体情况和原因。" 
                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                    />
                </div>
                <div className="flex justify-end gap-3 pt-2">
                    <button onClick={onCancel} className="px-4 py-2 text-sm font-semibold text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">取消</button>
                    <button onClick={handleSubmit} className="px-6 py-2 text-sm font-semibold text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">提交申诉</button>
                </div>
            </div>
        </div>
    );
}

const BasicViewPanel = () => {
    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-bold text-gray-800">可用操作</h3>
            <p className="mt-2 text-sm text-gray-500">当前为只读查看模式，无可用操作。</p>
        </div>
    );
};

const ReviewTriggerPanel: React.FC<{ triggers: string[] }> = ({ triggers }) => {
    if (!triggers || triggers.length === 0) {
        return null;
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-orange-50 to-amber-50 p-6 rounded-2xl shadow-lg border border-orange-200/50 relative overflow-hidden"
        >
            {/* 背景装饰 */}
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-orange-200 to-amber-200 rounded-full opacity-30 transform translate-x-10 -translate-y-10"></div>

            <div className="relative z-10">
                <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg">
                        <Flag className="w-5 h-5 text-white" />
                    </div>
                    <div>
                        <h3 className="text-xl font-bold text-gray-800">触发复核原因</h3>
                        <p className="text-sm text-orange-700">以下条件触发了人工复核</p>
                    </div>
                </div>

                <div className="space-y-3">
                    {triggers.map((trigger, index) => (
                        <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex items-start gap-4 p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-orange-200/50 shadow-sm hover:shadow-md transition-all duration-200"
                        >
                            <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: index * 0.1 + 0.2, type: "spring", stiffness: 200 }}
                                className="w-6 h-6 mt-0.5 rounded-full bg-gradient-to-br from-orange-500 to-amber-600 text-white flex items-center justify-center flex-shrink-0 text-sm font-bold shadow-md"
                            >
                                !
                            </motion.div>
                            <div className="flex-1">
                                <span className="text-gray-800 font-medium leading-relaxed">{trigger}</span>
                            </div>
                        </motion.div>
                    ))}
                </div>

                <div className="mt-6 p-4 bg-orange-100/50 rounded-xl border border-orange-200/30">
                    <div className="flex items-center gap-2 text-sm text-orange-800">
                        <Info className="w-4 h-4" />
                        <span className="font-medium">
                            共 {triggers.length} 个触发条件，需要进行人工复核确认
                        </span>
                    </div>
                </div>
            </div>
        </motion.div>
    );
};

interface ScoringDetailsPanelProps {
    rules: ScoringRule[],
    originalRules?: ScoringRule[],
    onRuleClick: (ts: number) => void,
    viewMode: string,
    taskStatus: string | null,
    onReviewChange?: (ruleId: string, newIsHit: boolean) => void,
    onReviewScoreChange?: (ruleId: string, newScore: number) => void
}

const ScoringDetailsPanel: React.FC<ScoringDetailsPanelProps> = ({ rules, originalRules, onRuleClick, viewMode, taskStatus, onReviewChange, onReviewScoreChange }) => {
    const [expandedRules, setExpandedRules] = useState<Set<string>>(new Set());

    const toggleRuleExpansion = (ruleId: string) => {
        setExpandedRules(prev => {
            const newSet = new Set(prev);
            if (newSet.has(ruleId)) {
                newSet.delete(ruleId);
            } else {
                newSet.add(ruleId);
            }
            return newSet;
        });
    };

    const getRuleTypeIcon = (ruleName: string) => {
        if (ruleName.includes('开场白')) return <MessageSquare className="w-4 h-4" />;
        if (ruleName.includes('合规')) return <Shield className="w-4 h-4" />;
        if (ruleName.includes('情绪')) return <Frown className="w-4 h-4" />;
        if (ruleName.includes('重复')) return <RotateCcw className="w-4 h-4" />;
        if (ruleName.includes('辱骂')) return <AlertTriangle className="w-4 h-4" />;
        return <FileText className="w-4 h-4" />;
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-white to-gray-50 p-8 rounded-2xl shadow-lg border border-gray-200/50 relative overflow-hidden"
        >
            {/* 背景装饰 */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-100 to-orange-100 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-green-100 to-blue-100 rounded-full opacity-20 transform -translate-x-12 translate-y-12"></div>

            <div className="relative z-10">
                <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                        <ClipboardList className="w-5 h-5 text-white" />
                    </div>
                    <div>
                        <h3 className="text-xl font-bold text-gray-800">评分详情</h3>
                        <p className="text-sm text-gray-600">详细展示各项评分规则及扣分情况</p>
                    </div>
                </div>

                <div className="space-y-4">
                    {rules.map((rule, index) => {
                        const isClickable = rule.isHit && typeof rule.timestamp === 'number';
                        const canReview = viewMode === 'review' && taskStatus !== 'completed' && onReviewChange && onReviewScoreChange;
                        const isExpanded = expandedRules.has(rule.id);
                        
                        // Logic to check for changes against original machine-scored rules
                        const baseRules = originalRules || [];
                        const originalRule = baseRules.find(r => r.id === rule.id);
                        const hasChanged = originalRule && (originalRule.isHit !== rule.isHit || (rule.isHit && originalRule.score !== rule.score));

                        const cardBgClass = rule.isHit 
                            ? 'bg-gradient-to-r from-red-50/80 to-orange-50/80' 
                            : 'bg-gradient-to-r from-green-50/80 to-emerald-50/80';
                        
                        const cardBorderClass = hasChanged 
                            ? 'border-blue-400 shadow-blue-100' 
                            : (rule.isHit ? 'border-red-200 shadow-red-100' : 'border-green-200 shadow-green-100');

                        return (
                            <motion.div
                                key={rule.id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className={`relative p-5 rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${cardBgClass} border ${cardBorderClass}`}
                            >
                                <div className="flex items-start gap-4">
                                    <div className="flex-shrink-0">
                                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center shadow-lg transition-all duration-300 ${
                                            rule.isHit 
                                                ? 'bg-gradient-to-br from-red-500 to-orange-500 text-white' 
                                                : 'bg-gradient-to-br from-green-500 to-emerald-500 text-white'
                                        }`}>
                                            {rule.isHit ? <X className="w-6 h-6" /> : <Check className="w-6 h-6" />}
                                        </div>
                                        {hasChanged && (
                                            <motion.div
                                                initial={{ scale: 0 }}
                                                animate={{ scale: 1 }}
                                                className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-md"
                                            >
                                                改
                                            </motion.div>
                                        )}
                                    </div>

                                    <div className="flex-grow min-w-0">
                                        <div className="flex items-start justify-between gap-4">
                                            <div className="flex-grow">
                                                <div className="flex items-center gap-2 mb-2">
                                                    <h4 className="font-bold text-gray-800 text-lg">{rule.name}</h4>
                                                    {rule.timestamp && (
                                                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                                            {Math.floor(rule.timestamp / 60)}:{(rule.timestamp % 60).toString().padStart(2, '0')}
                                                        </span>
                                                    )}
                                                </div>
                                                <p className="text-sm text-gray-600 leading-relaxed">{rule.description}</p>
                                            </div>

                                            <div className="flex-shrink-0 text-right ml-4">
                                                <div className="flex flex-col items-end gap-1">
                                                    <div className={`text-2xl font-bold ${
                                                        rule.isHit ? 'text-red-600' : 'text-green-600'
                                                    }`}>
                                                        {rule.isHit ? rule.score : '+0'}
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                                                            rule.isHit 
                                                                ? 'bg-red-100 text-red-700' 
                                                                : 'bg-green-100 text-green-700'
                                                        }`}>
                                                            {rule.isHit ? '已扣分' : '已通过'}
                                                        </span>
                                                        {isClickable && (
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    onRuleClick(rule.timestamp!);
                                                                }}
                                                                className="text-blue-500 hover:text-blue-700 transition-colors"
                                                                title="跳转到对应位置"
                                                            >
                                                                <ExternalLink className="w-4 h-4" />
                                                            </button>
                                                        )}
                                                    </div>
                                                </div>

                                                {hasChanged && originalRule && (
                                                    <motion.div
                                                        initial={{ opacity: 0, y: -10 }}
                                                        animate={{ opacity: 1, y: 0 }}
                                                        className="mt-2 text-xs text-gray-500"
                                                    >
                                                        <span className="line-through">机审: {originalRule.isHit ? originalRule.score : '+0'}</span>
                                                        <span className="ml-2 text-blue-600 font-medium">→ 复核调整</span>
                                                    </motion.div>
                                                )}
                                            </div>
                                        </div>

                                        {canReview && rule.isHit && (
                                            <motion.div
                                                initial={{ opacity: 0, height: 0 }}
                                                animate={{ opacity: 1, height: 'auto' }}
                                                className="mt-3 pt-3 border-t border-gray-200"
                                            >
                                                <div className="flex items-center gap-3">
                                                    <label className="text-sm font-medium text-gray-700">调整扣分:</label>
                                                    <input
                                                        type="number"
                                                        value={rule.score}
                                                        onChange={(e) => onReviewScoreChange(rule.id, parseInt(e.target.value, 10))}
                                                        className="w-20 px-2 py-1 text-center border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-bold text-red-600"
                                                        min="-100" max="0"
                                                    />
                                                    <div className="flex gap-2">
                                                        <Tooltip text="标记为通过">
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    onReviewChange(rule.id, false);
                                                                }}
                                                                className="p-1.5 text-green-600 hover:bg-green-100 rounded-lg transition-colors"
                                                            >
                                                                <CheckCircle className="w-4 h-4" />
                                                            </button>
                                                        </Tooltip>
                                                        <Tooltip text="重置为机审结果">
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    if (originalRule) {
                                                                        onReviewChange(rule.id, originalRule.isHit);
                                                                        onReviewScoreChange(rule.id, originalRule.score);
                                                                    }
                                                                }}
                                                                className="p-1.5 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                                                            >
                                                                <RotateCcw className="w-4 h-4" />
                                                            </button>
                                                        </Tooltip>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        )}
                                    </div>
                                </div>
                            </motion.div>
                        );
                    })}

                    {rules.length === 0 && (
                        <div className="text-center py-12">
                            <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                            <p className="text-gray-500">暂无评分规则</p>
                        </div>
                    )}
                </div>
            </div>
        </motion.div>
    );
};

// --- New Info Panels for Read-only Views ---
const ProcessTimeline: React.FC<{ session: SessionData, viewMode: string, taskStatus: string | null }> = ({ session, viewMode, taskStatus }) => {
    const timelineItems = [
        {
            id: 'ai-check',
            title: 'AI初检完成',
            description: '智能质检系统完成初步分析',
            time: session.startTime,
            icon: Brain,
            color: 'from-blue-500 to-indigo-600',
            bgColor: 'bg-blue-100',
            textColor: 'text-blue-700',
            isCompleted: true
        },
        ...(session.reviewDetails ? [{
            id: 'review',
            title: '人工复核',
            description: `质检专员 ${session.reviewDetails.reviewer.name} 进行人工审核`,
            time: session.reviewDetails.timestamp,
            icon: Eye,
            color: 'from-indigo-500 to-purple-600',
            bgColor: 'bg-indigo-100',
            textColor: 'text-indigo-700',
            isCompleted: true
        }] : []),
        ...(session.appealDetails ? [{
            id: 'appeal',
            title: '申诉发起',
            description: `坐席 ${session.appealDetails.appellant.name} 提起申诉`,
            time: session.appealDetails.timestamp,
            icon: Shield,
            color: 'from-amber-500 to-orange-600',
            bgColor: 'bg-amber-100',
            textColor: 'text-amber-700',
            isCompleted: true
        }] : []),
        ...(session.appealDetails?.processor && (viewMode !== 'appeal_processing' || taskStatus === 'completed') ? [{
            id: 'process',
            title: `申诉${session.appealDetails.status === 'approved' ? '通过' : '驳回'}`,
            description: `处理人 ${session.appealDetails.processor.name}`,
            time: session.appealDetails.processedTimestamp,
            icon: session.appealDetails.status === 'approved' ? CheckCircle : XCircle,
            color: session.appealDetails.status === 'approved' ? 'from-green-500 to-emerald-600' : 'from-red-500 to-rose-600',
            bgColor: session.appealDetails.status === 'approved' ? 'bg-green-100' : 'bg-red-100',
            textColor: session.appealDetails.status === 'approved' ? 'text-green-700' : 'text-red-700',
            isCompleted: true
        }] : [])
    ];

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-br from-gray-50 via-white to-blue-50 p-6 rounded-2xl shadow-lg border border-gray-200/50 relative overflow-hidden"
        >
            {/* 背景装饰 */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-gray-200 to-blue-200 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-200 to-indigo-200 rounded-full opacity-20 transform -translate-x-12 translate-y-12"></div>
            
            <div className="relative z-10">
                <div className="flex items-center gap-3 mb-6">
                    <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                        className="w-12 h-12 bg-gradient-to-br from-gray-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg"
                    >
                        <Timer className="w-6 h-6 text-white" />
                    </motion.div>
                    <div>
                        <h3 className="text-xl font-bold text-gray-800">处理时间轴</h3>
                        <p className="text-sm text-gray-600">质检流程完整记录</p>
                    </div>
                </div>

                <div className="relative">
                    {timelineItems.map((item, index) => (
                        <motion.div
                            key={item.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.3 + index * 0.2 }}
                            className="relative flex items-start gap-4 mb-8 last:mb-0"
                        >
                            {/* 连接线 */}
                            {index < timelineItems.length - 1 && (
                                <div className="absolute left-6 top-12 bottom-0 w-0.5 bg-gradient-to-b from-gray-300 to-transparent"></div>
                            )}
                            
                            {/* 时间点 */}
                            <motion.div
                                whileHover={{ scale: 1.1 }}
                                className={`relative flex-shrink-0 w-12 h-12 bg-gradient-to-br ${item.color} rounded-full flex items-center justify-center shadow-lg z-10`}
                            >
                                <item.icon className="w-6 h-6 text-white" />
                                <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 0.5 + index * 0.2 }}
                                    className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center border-2 border-white"
                                >
                                    <Check className="w-3 h-3 text-white" />
                                </motion.div>
                            </motion.div>
                            
                            {/* 内容卡片 */}
                            <motion.div
                                whileHover={{ scale: 1.02 }}
                                className={`flex-1 p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200`}
                            >
                                <div className="flex items-start justify-between mb-2">
                                    <h4 className="font-bold text-gray-800 text-lg">{item.title}</h4>
                                    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${item.bgColor} ${item.textColor}`}>
                                        <item.icon className="w-3 h-3" />
                                        <span>完成</span>
                                    </div>
                                </div>
                                
                                <p className="text-sm text-gray-600 mb-2 leading-relaxed">{item.description}</p>
                                
                                <div className="flex items-center gap-2 text-sm text-gray-500">
                                    <Calendar className="w-4 h-4" />
                                    <span className="font-medium">{item.time}</span>
                                </div>
                                
                                {/* 进度条 */}
                                <div className="mt-3 h-1 bg-gray-200 rounded-full overflow-hidden">
                                    <motion.div
                                        initial={{ width: 0 }}
                                        animate={{ width: '100%' }}
                                        transition={{ delay: 0.5 + index * 0.2, duration: 0.8 }}
                                        className={`h-full bg-gradient-to-r ${item.color} rounded-full`}
                                    />
                                </div>
                            </motion.div>
                        </motion.div>
                    ))}
                </div>

                {/* 状态总结 */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                    className="mt-6 p-4 bg-gradient-to-r from-gray-100 to-blue-100 rounded-xl border border-gray-200/50"
                >
                    <div className="flex items-center gap-3">
                        <Info className="w-5 h-5 text-blue-600" />
                        <div>
                            <p className="text-sm font-bold text-gray-800">
                                处理状态：<span className="text-blue-600">已完成 {timelineItems.length} 个步骤</span>
                            </p>
                            <p className="text-xs text-gray-600">整个质检流程已完整记录，可追溯每个环节</p>
                        </div>
                    </div>
                </motion.div>
            </div>
        </motion.div>
    );
};

const ReviewInfoPanel: React.FC<{ details: ReviewDetails }> = ({ details }) => (
    <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-br from-indigo-50 via-white to-purple-50 p-6 rounded-2xl shadow-lg border border-indigo-100 relative overflow-hidden"
    >
        {/* 背景装饰 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-200 to-purple-200 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-200 to-indigo-200 rounded-full opacity-20 transform -translate-x-12 translate-y-12"></div>
        
        <div className="relative z-10">
            <div className="flex items-center gap-3 mb-6">
                <motion.div 
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg"
                >
                    <FileText className="w-6 h-6 text-white" />
                </motion.div>
                <div>
                    <h3 className="text-xl font-bold text-gray-800">人工复核详情</h3>
                    <p className="text-sm text-indigo-600 font-medium">专业质检复核记录</p>
                </div>
            </div>
            
            <div className="space-y-4">
                {/* 复核人信息 */}
                <motion.div 
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="flex items-center gap-4 p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-indigo-200/50"
                >
                    <div className="w-10 h-10 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-full flex items-center justify-center shadow-md">
                        <UserCircle className="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <p className="font-bold text-gray-800 text-lg">{details.reviewer.name}</p>
                        <p className="text-sm text-gray-600">质检复核专员</p>
                    </div>
                </motion.div>

                {/* 复核时间 */}
                <motion.div 
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                    className="flex items-center gap-3 p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-gray-200/50"
                >
                    <Calendar className="w-5 h-5 text-indigo-500" />
                    <div>
                        <p className="text-sm text-gray-600">复核时间</p>
                        <p className="font-semibold text-gray-800">{details.timestamp}</p>
                    </div>
                </motion.div>

                {/* 复核意见 */}
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="relative"
                >
                    <div className="absolute -top-2 left-4 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-xs font-bold px-3 py-1 rounded-full">
                        复核意见
                    </div>
                    <div className="pt-6 pl-4 border-l-4 border-indigo-200">
                        <div className="p-4 bg-white rounded-xl border border-indigo-100 shadow-sm">
                            <div className="flex items-start gap-3">
                                <Quote className="w-5 h-5 text-indigo-400 mt-1 flex-shrink-0" />
                                <p className="text-gray-700 leading-relaxed italic">
                                    {details.notes}
                                </p>
                            </div>
                        </div>
                    </div>
                </motion.div>
            </div>
        </div>
    </motion.div>
);

const AppealInfoPanel: React.FC<{ details: AppealDetails }> = ({ details }) => (
    <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-br from-amber-50 via-white to-orange-50 p-6 rounded-2xl shadow-lg border border-amber-100 relative overflow-hidden"
    >
        {/* 背景装饰 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-amber-200 to-orange-200 rounded-full opacity-20 transform translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-yellow-200 to-amber-200 rounded-full opacity-20 transform -translate-x-12 translate-y-12"></div>
        
        <div className="relative z-10">
            <div className="flex items-center gap-3 mb-6">
                <motion.div 
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg"
                >
                    <Shield className="w-6 h-6 text-white" />
                </motion.div>
                <div>
                    <h3 className="text-xl font-bold text-gray-800">申诉处理详情</h3>
                    <p className="text-sm text-amber-600 font-medium">坐席申诉处理记录</p>
                </div>
            </div>

            {/* 申诉流程时间轴 */}
            <div className="space-y-6">
                {/* 申诉发起 */}
                <motion.div 
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="relative"
                >
                    <div className="absolute left-4 top-6 bottom-0 w-0.5 bg-amber-200"></div>
                    <div className="flex items-start gap-4">
                        <motion.div 
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.4 }}
                            className="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-500 rounded-full flex items-center justify-center shadow-md z-10"
                        >
                            <span className="text-white text-xs font-bold">1</span>
                        </motion.div>
                        <div className="flex-1">
                            <p className="font-semibold text-gray-800 mb-2">申诉发起</p>
                            <div className="p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-amber-200/50">
                                <div className="flex items-center gap-3 mb-2">
                                    <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                                        <UserCircle className="w-4 h-4 text-white" />
                                    </div>
                                    <div>
                                        <p className="font-bold text-gray-800">{details.appellant.name}</p>
                                        <p className="text-sm text-gray-600">{details.appellant.teamName} • {details.timestamp}</p>
                                    </div>
                                </div>
                                <div className="p-3 bg-amber-50 rounded-lg border border-amber-200/50">
                                    <div className="flex items-start gap-2">
                                        <Quote className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                                        <p className="text-sm text-gray-700 leading-relaxed">{details.reason}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </motion.div>

                {/* 处理结果 */}
                {details.status && details.processor && (
                    <motion.div 
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                        className="relative"
                    >
                        <div className="flex items-start gap-4">
                            <motion.div 
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.6 }}
                                className={`w-8 h-8 rounded-full flex items-center justify-center shadow-md z-10 ${
                                    details.status === 'approved' 
                                        ? 'bg-gradient-to-br from-green-500 to-emerald-500' 
                                        : 'bg-gradient-to-br from-red-500 to-rose-500'
                                }`}
                            >
                                <span className="text-white text-xs font-bold">2</span>
                            </motion.div>
                            <div className="flex-1">
                                <p className="font-semibold text-gray-800 mb-2">处理结果</p>
                                <div className="p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                            details.status === 'approved' 
                                                ? 'bg-gradient-to-br from-green-400 to-emerald-500' 
                                                : 'bg-gradient-to-br from-red-400 to-rose-500'
                                        }`}>
                                            {details.status === 'approved' ? 
                                                <CheckCircle className="w-4 h-4 text-white" /> : 
                                                <XCircle className="w-4 h-4 text-white" />
                                            }
                                        </div>
                                        <div>
                                            <p className="font-bold text-gray-800">{details.processor.name}</p>
                                            <p className="text-sm text-gray-600">{details.processedTimestamp}</p>
                                        </div>
                                    </div>
                                    
                                    <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-bold mb-3 ${
                                        details.status === 'approved' 
                                            ? 'bg-green-100 text-green-800 border border-green-200' 
                                            : 'bg-red-100 text-red-800 border border-red-200'
                                    }`}>
                                        {details.status === 'approved' ? 
                                            <CheckCircle className="w-4 h-4" /> : 
                                            <XCircle className="w-4 h-4" />
                                        }
                                        {details.status === 'approved' ? '申诉通过' : '申诉驳回'}
                                    </div>
                                    
                                    <div className="p-3 bg-gray-50 rounded-lg border border-gray-200/50">
                                        <div className="flex items-start gap-2">
                                            <Info className="w-4 h-4 text-gray-600 mt-0.5 flex-shrink-0" />
                                            <p className="text-sm text-gray-700 leading-relaxed">{details.processorNotes}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                )}
            </div>
        </div>
    </motion.div>
);

const TaskInfoPanel: React.FC<{ details: TaskDetails; session: SessionData; viewMode: string; }> = ({ details, session, viewMode }) => {
    
    const statusMap = {
        eligible: { text: '可申诉', color: 'blue' },
        in_progress: { text: '申诉中', color: 'yellow' },
        processed: { text: '已处理', color: 'gray' },
        expired: { text: '已过期', color: 'red' },
    };
    const appealStatusInfo = statusMap[session.appealStatus];

    const isAppealView = viewMode === 'appeal_processing';
    const isPerformanceView = viewMode === 'performance_check';

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                <ClipboardList className="w-5 h-5 text-purple-500" />
                <span>任务信息</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-5 text-sm">
                <div>
                    <p className="text-gray-500">记录编号</p>
                    <p className="font-medium text-gray-800 mt-1">{session.id}</p>
                </div>
                <div>
                    <p className="text-gray-500">所属任务</p>
                    <p className="font-medium text-gray-800 mt-1">{details.taskName}</p>
                </div>
                <div>
                    <p className="text-gray-500">质检方案</p>
                    <p className="font-medium text-gray-800 mt-1">{details.scoringSchemeName}</p>
                </div>
                <div>
                    <p className="text-gray-500">基准总分</p>
                    <p className="font-medium text-gray-800 mt-1">{details.baseScore}</p>
                </div>
                <div>
                    <p className="text-gray-500">合格分数线</p>
                    <p className="font-medium text-gray-800 mt-1">{details.passingScore}</p>
                </div>
                
                {isAppealView && (
                    <div>
                        <p className="text-gray-500">申请时间</p>
                        <p className="font-medium text-gray-800 mt-1">{session.appealDetails?.timestamp}</p>
                    </div>
                )}
                
                {isPerformanceView && (
                    <>
                        <div>
                            <p className="text-gray-500">申诉状态</p>
                            <p className="font-medium text-gray-800 mt-1">
                                <span className={`px-2 py-1 text-xs font-medium bg-${appealStatusInfo.color}-100 text-${appealStatusInfo.color}-800 rounded-full`}>
                                    {appealStatusInfo.text}
                                </span>
                            </p>
                        </div>
                        <div>
                            <p className="text-gray-500">申诉有效期</p>
                            <p className="font-medium text-gray-800 mt-1">{session.appealDeadline || 'N/A'}</p>
                        </div>
                    </>
                )}

                { !isAppealView && !isPerformanceView && viewMode !== 'basic_view' && (
                     <div>
                        <p className="text-gray-500">分配时间</p>
                        <p className="font-medium text-gray-800 mt-1">{details.assignTime}</p>
                    </div>
                )}
            </div>
        </div>
    );
};

// 在其他面板组件后添加新的CallInfoPanel组件
const CallInfoPanel: React.FC<{ session: SessionData; viewMode: string; }> = ({ session, viewMode }) => {
    const isAppeal = viewMode === 'appeal_processing' && session.appealDetails;
    const isPerformance = viewMode === 'performance_check';

    const getAgentLabel = () => {
        if (isPerformance) return '坐席';
        if (isAppeal) return '申请坐席';
        return '被检坐席';
    };

    const getAgentInfo = () => {
        const agent = isAppeal ? session.appealDetails!.appellant : session.agent;
        return `${agent.name}/${agent.id}`;
    };

    const formatDuration = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}分${secs}秒`;
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-gray-200/50 relative overflow-hidden"
        >
            {/* 背景装饰 */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full opacity-30 transform translate-x-12 -translate-y-12"></div>

            <div className="relative z-10">
                <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                        <Phone className="w-5 h-5 text-white" />
                    </div>
                    <div>
                        <h3 className="text-xl font-bold text-gray-800">通话信息</h3>
                        <p className="text-sm text-gray-600">本次通话的基本信息</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.1 }}
                        className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200/50"
                    >
                        <div className="flex items-center gap-2 mb-2">
                            <Users className="w-4 h-4 text-blue-600" />
                            <p className="text-sm font-medium text-blue-700">{getAgentLabel()}</p>
                        </div>
                        <p className="font-bold text-gray-800 text-lg">{getAgentInfo()}</p>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.2 }}
                        className="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200/50"
                    >
                        <div className="flex items-center gap-2 mb-2">
                            <Building className="w-4 h-4 text-green-600" />
                            <p className="text-sm font-medium text-green-700">所属班组</p>
                        </div>
                        <p className="font-bold text-gray-800 text-lg">
                            {isAppeal ? session.appealDetails!.appellant.teamName : session.agent.teamName}
                        </p>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.3 }}
                        className="bg-gradient-to-br from-purple-50 to-violet-50 p-4 rounded-xl border border-purple-200/50"
                    >
                        <div className="flex items-center gap-2 mb-2">
                            <Phone className="w-4 h-4 text-purple-600" />
                            <p className="text-sm font-medium text-purple-700">客户号码</p>
                        </div>
                        <p className="font-bold text-gray-800 text-lg">{session.customer.phone}</p>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.4 }}
                        className="bg-gradient-to-br from-orange-50 to-amber-50 p-4 rounded-xl border border-orange-200/50"
                    >
                        <div className="flex items-center gap-2 mb-2">
                            <Calendar className="w-4 h-4 text-orange-600" />
                            <p className="text-sm font-medium text-orange-700">开始时间</p>
                        </div>
                        <p className="font-bold text-gray-800 text-lg">{session.startTime}</p>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.5 }}
                        className="bg-gradient-to-br from-pink-50 to-rose-50 p-4 rounded-xl border border-pink-200/50"
                    >
                        <div className="flex items-center gap-2 mb-2">
                            <Timer className="w-4 h-4 text-pink-600" />
                            <p className="text-sm font-medium text-pink-700">通话时长</p>
                        </div>
                        <p className="font-bold text-gray-800 text-lg">{formatDuration(session.duration)}</p>
                    </motion.div>
                </div>
            </div>
        </motion.div>
    );
};

// --- Main Component ---
const MultiModeSessionDetailPage: React.FC = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const viewMode = queryParams.get('viewMode') || 'basic_view';
    const taskStatus = queryParams.get('status');

    const [session, setSession] = useState<SessionData>(mockSessionData);
    const [currentTime, setCurrentTime] = useState(0);
    const [isPlaying, setIsPlaying] = useState(false);
    const [isInitiatingAppeal, setIsInitiatingAppeal] = useState(false);
    
    // State for review modifications
    const [reviewedScoringRules, setReviewedScoringRules] = useState<ScoringRule[]>(session.reviewedScoringRules || session.scoringRules);

    const calculatedReviewScore = useMemo(() => {
        const deductions = reviewedScoringRules.reduce((acc, rule) => {
            return acc + (rule.isHit ? Math.abs(rule.score) : 0);
        }, 0);
        return (session.taskDetails?.baseScore ?? 100) - deductions;
    }, [reviewedScoringRules, session.taskDetails?.baseScore]);

    useEffect(() => {
        // Reset review state if session data changes
        setReviewedScoringRules(session.reviewedScoringRules || session.scoringRules);
    }, [session]);
    
    const handleRuleReviewChange = (ruleId: string, newIsHit: boolean) => {
        setReviewedScoringRules(prevRules =>
            prevRules.map(rule =>
                rule.id === ruleId ? { ...rule, isHit: newIsHit } : rule
            )
        );
    };

    const handleRuleScoreChange = (ruleId: string, newScore: number) => {
        setReviewedScoringRules(prevRules =>
            prevRules.map(rule =>
                rule.id === ruleId ? { ...rule, score: isNaN(newScore) ? 0 : newScore } : rule
            )
        );
    };

    const displayedRules = useMemo(() => {
        if (viewMode === 'review') {
            return reviewedScoringRules;
        }
        return session.reviewedScoringRules || session.scoringRules;
    }, [viewMode, reviewedScoringRules, session]);

    useEffect(() => {
        let interval: number;
        if (isPlaying && currentTime < session.duration) {
            interval = window.setInterval(() => {
                setCurrentTime(prevTime => prevTime + 1);
            }, 1000);
        }
        return () => window.clearInterval(interval);
    }, [isPlaying, currentTime, session.duration]);

    const handleInitiateAppeal = () => {
        setIsInitiatingAppeal(true);
    };

    const handleCancelInitiateAppeal = () => {
        setIsInitiatingAppeal(false);
    };

    const handleSubmitInitiateAppeal = (reason: string) => {
        alert(`申诉已提交！\n理由：${reason}`);
        setSession(prev => ({
            ...prev,
            appealStatus: 'in_progress',
            appealDetails: {
                appellant: prev.agent,
                timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
                reason: reason,
            }
        }));
        setIsInitiatingAppeal(false);
    };

    const scorePanelProps: ScorePanelProps = (() => {
        const passingScore = session.taskDetails?.passingScore || 80;
        switch (viewMode) {
            case 'review':
                return {
                    machineScore: session.machineScore,
                    reviewScore: calculatedReviewScore,
                    appealScore: undefined,
                    finalScore: calculatedReviewScore,
                    finalScoreSource: 'review',
                    passingScore,
                };
            case 'appeal_processing':
                if (taskStatus === 'completed') {
                    // 查看已完成的申诉，显示最终结果
                    return {
                        machineScore: session.machineScore,
                        reviewScore: session.reviewScore,
                        appealScore: session.appealScore,
                        finalScore: session.finalScore,
                        finalScoreSource: session.finalScoreSource,
                        passingScore,
                    };
                }
                // 正在处理申诉，显示申诉前的分数
                return {
                    machineScore: session.machineScore,
                    reviewScore: session.reviewScore,
                    appealScore: undefined,
                    finalScore: session.reviewScore || session.machineScore || 0,
                    finalScoreSource: session.reviewScore ? 'review' : 'machine',
                    passingScore,
                };
            case 'performance_check':
                if (isInitiatingAppeal) {
                    return {
                        machineScore: session.machineScore,
                        reviewScore: session.reviewScore,
                        appealScore: undefined,
                        finalScore: session.reviewScore || session.machineScore || 0,
                        finalScoreSource: session.reviewScore ? 'review' : 'machine',
                        passingScore,
                    };
                }
                return {
                    machineScore: session.machineScore,
                    reviewScore: session.reviewScore,
                    appealScore: session.appealScore,
                    finalScore: session.finalScore,
                    finalScoreSource: session.finalScoreSource,
                    passingScore,
                };
            default:
                return {
                    machineScore: session.machineScore,
                    reviewScore: session.reviewScore,
                    appealScore: session.appealScore,
                    finalScore: session.finalScore,
                    finalScoreSource: session.finalScoreSource,
                    passingScore,
                };
        }
    })();

    const renderActionPanel = () => {
        switch (viewMode) {
            case 'review':
                if (taskStatus === 'completed') {
                    return session.reviewDetails ? <ReviewInfoPanel details={session.reviewDetails} /> : null;
                }
                return <ReviewPanel 
                    machineScore={session.machineScore} 
                    reviewScore={calculatedReviewScore} 
                    onSubmit={handleSubmitReview}
                    onCancel={handleCancel}
                />;
            case 'appeal_processing':
                if (taskStatus === 'completed') {
                    return session.appealDetails ? <AppealInfoPanel details={session.appealDetails} /> : null;
                }
                return <AppealPanel 
                    scoreToAppeal={session.reviewScore ?? session.machineScore ?? 0}
                    scoreType={session.reviewScore !== undefined ? 'review' : 'machine'}
                    onSubmit={handleSubmitAppeal}
                    onCancel={handleCancelAppeal}
                />;
            case 'performance_check':
                if (isInitiatingAppeal) {
                    return <InitiateAppealPanel 
                        onSubmit={handleSubmitInitiateAppeal}
                        onCancel={handleCancelInitiateAppeal}
                    />;
                }
                return <PerformanceCheckPanel appealStatus={session.appealStatus} onInitiateAppeal={handleInitiateAppeal} />;
            case 'basic_view':
            default:
                return <BasicViewPanel />;
        }
    };

    const handleCancel = () => {
        navigate('/final-design/my-review-tasks');
    }

    const handleSubmitReview = (notes: string) => {
        alert(`提交成功!\n复核后得分: ${calculatedReviewScore}\n复核备注: ${notes}`);
        navigate('/final-design/my-review-tasks');
    }

    const handleCancelAppeal = () => {
        navigate('/final-design/appeal-processing');
    };

    const handleSubmitAppeal = (decision: 'approve' | 'reject', newScore: number | null, notes: string) => {
        alert(`申诉处理提交成功!\n决定: ${decision === 'approve' ? '同意' : '驳回'}\n调整后分数: ${newScore ?? '未调整'}\n处理备注: ${notes}`);
        navigate('/final-design/appeal-processing');
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30 flex flex-col">
            {/* 顶部导航栏 */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-200/50 sticky top-0 z-50"
            >
                <div className="max-w-screen-2xl mx-auto px-6">
                    <div className="flex items-center justify-between py-4">
                        <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => navigate(-1)}
                            className="flex items-center gap-2 text-gray-600 hover:text-blue-600 transition-all duration-200 px-3 py-2 rounded-lg hover:bg-blue-50"
                        >
                            <ChevronLeft className="w-5 h-5" />
                            <span className="font-medium">返回列表</span>
                        </motion.button>

                        <div className="flex items-center gap-3">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Phone className="w-4 h-4" />
                                <span>会话详情</span>
                            </div>
                            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>
                </div>
            </motion.div>

            {/* 主要内容区域 */}
            <main className="flex-1 p-6 overflow-auto">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="max-w-screen-2xl mx-auto"
                >
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* 左侧主要内容 */}
                        <div className="lg:col-span-2 space-y-8">
                            {session.taskDetails && (
                                <motion.div
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.2 }}
                                >
                                    <TaskInfoPanel
                                        details={session.taskDetails}
                                        session={session}
                                        viewMode={viewMode}
                                    />
                                </motion.div>
                            )}

                            <motion.div
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.3 }}
                            >
                                <CallInfoPanel session={session} viewMode={viewMode} />
                            </motion.div>

                            <motion.div
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.4 }}
                                className="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-gray-200/50"
                            >
                                <div className="flex items-center gap-3 mb-6">
                                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                                        <Headphones className="w-5 h-5 text-white" />
                                    </div>
                                    <div>
                                        <h3 className="text-xl font-bold text-gray-800">音频播放器</h3>
                                        <p className="text-sm text-gray-600">点击播放按钮开始收听通话录音</p>
                                    </div>
                                </div>

                                <AudioPlayer
                                    duration={session.duration}
                                    currentTime={currentTime}
                                    setCurrentTime={setCurrentTime}
                                    isPlaying={isPlaying}
                                    setIsPlaying={setIsPlaying}
                                />

                                <div className="mt-8 border-t border-gray-200/50 pt-8">
                                    <div className="flex items-center gap-3 mb-6">
                                        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                            <MessageSquare className="w-4 h-4 text-white" />
                                        </div>
                                        <h3 className="text-lg font-bold text-gray-800">通话文本</h3>
                                    </div>
                                    <TranscriptPanel
                                        transcript={(session as SessionData & { transcript: TranscriptItem[] }).transcript}
                                        currentTime={currentTime}
                                        setCurrentTime={setCurrentTime}
                                    />
                                </div>
                            </motion.div>
                        </div>

                        {/* 右侧侧边栏 */}
                        <div className="space-y-6">
                            {viewMode === 'review' && session.reviewTriggers && (
                                <motion.div
                                    initial={{ opacity: 0, x: 20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.2 }}
                                >
                                    <ReviewTriggerPanel triggers={session.reviewTriggers} />
                                </motion.div>
                            )}

                            <motion.div
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.3 }}
                            >
                                <ScorePanel {...scorePanelProps} />
                            </motion.div>

                            {viewMode !== 'review' && (
                                <motion.div
                                    initial={{ opacity: 0, x: 20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.4 }}
                                >
                                    <ProcessTimeline session={session} viewMode={viewMode} taskStatus={taskStatus} />
                                </motion.div>
                            )}

                            <motion.div
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.5 }}
                            >
                                <ScoringDetailsPanel
                                    rules={displayedRules}
                                    originalRules={session.scoringRules}
                                    onRuleClick={(timestamp) => setCurrentTime(timestamp)}
                                    viewMode={viewMode}
                                    taskStatus={taskStatus}
                                    onReviewChange={handleRuleReviewChange}
                                    onReviewScoreChange={handleRuleScoreChange}
                                />
                            </motion.div>

                            {viewMode !== 'review' && session.reviewDetails && (
                                <motion.div
                                    initial={{ opacity: 0, x: 20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.6 }}
                                >
                                    <ReviewInfoPanel details={session.reviewDetails} />
                                </motion.div>
                            )}

                            {viewMode !== 'review' && viewMode !== 'appeal_processing' && session.appealDetails && (
                                <motion.div
                                    initial={{ opacity: 0, x: 20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.7 }}
                                >
                                    <AppealInfoPanel details={session.appealDetails} />
                                </motion.div>
                            )}

                            <motion.div
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.8 }}
                            >
                                {renderActionPanel()}
                            </motion.div>
                        </div>
                    </div>
                </motion.div>
            </main>
        </div>
    );
};

export default MultiModeSessionDetailPage;