import React, { useState } from 'react';
import { Card, Button, Typography, Space, Divider, Progress, Avatar, Badge, Tooltip } from 'antd';
import { Button as CustomButton } from '../../ui/button';
import { 
  CheckCircle, 
  Clock, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle, 
  Bell,
  FileText,
  Calendar,
  Phone,
  Award,
  Target,
  BarChart3,
  UserCheck,
  HelpCircle
} from 'lucide-react';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import { MetricCard } from './common/MetricCard';
import { MetricDescription } from './common/MetricHelpButton';
import { GlobalStyles } from './common/GlobalStyles';

const { Text } = Typography;

// 复核员视角指标说明数据
const reviewerMetricDescriptions: Record<string, MetricDescription> = {
  totalReviews: {
    title: "复核总量",
    description: "在特定时间段内，复核员完成的质检/复核任务的总数量。",
    calculation: "统计周期内所有已完成的复核任务数量。"
  },
  pendingReviews: {
    title: "待复核量",
    description: "当前分配给该复核员，但尚未完成的复核任务数量。",
    calculation: "计算当前指派给该复核员且状态为\"待处理\"或\"进行中\"的任务总数。"
  },
  dailyAverageReviews: {
    title: "日均复核量",
    description: "在特定时间段内，复核员平均每个工作日完成的复核任务数量。",
    calculation: "日均复核量 = 周期内复核总量 / 周期内的工作天数"
  },
  aiCorrectionCount: {
    title: "AI结果纠错数",
    description: "在复核过程中，复核员修正（或推翻）AI自动质检结果的次数。这个指标衡量了人工复核的价值。",
    calculation: "统计在周期内，复核员对AI预处理的质检项（如分数、标签等）进行了修改的案例总数。"
  }
};



/**
 * 复核员绩效数据接口
 */
interface ReviewerPerformanceData {
  /** 复核总量 */
  totalReviews: number;
  /** 待复核量 */
  pendingReviews: number;
  /** 日均复核量 */
  dailyAverageReviews: number;
  /** AI结果纠错数 */
  aiCorrectionCount: number;
}

/**
 * 工作量趋势数据接口
 */
interface WorkloadTrendData {
  /** 日期 */
  date: string;
  /** 完成复核数量 */
  completedReviews: number;
  /** 纠错数量 */
  corrections: number;
}

/**
 * 通知公告接口
 */
interface Notification {
  /** 通知ID */
  id: number;
  /** 通知类型 */
  type: 'system' | 'task' | 'announcement' | 'result' | 'appeal';
  /** 通知标题 */
  title: string;
  /** 通知内容 */
  content: string;
  /** 发布时间 */
  time: string;
  /** 是否已读 */
  isRead: boolean;
  /** 优先级 */
  priority: 'high' | 'medium' | 'low';
}

/**
 * 待复核任务接口
 */
interface ReviewTask {
  /** 任务ID */
  id: string;
  /** 记录编号 */
  recordId: string;
  /** 客服姓名 */
  agentName: string;
  /** 客服头像 */
  agentAvatar: string;
  /** 通话时间 */
  callTime: string;
  /** 通话时长 */
  callDuration: string;
  /** AI初始评分 */
  aiScore: number;
  /** 分配时间 */
  assignedTime: string;
  /** 质检类型 */
  reviewType: string;
}

// 模拟绩效数据
const performanceData: ReviewerPerformanceData = {
  totalReviews: 156,
  pendingReviews: 23,
  dailyAverageReviews: 12.3,
  aiCorrectionCount: 34
};

// 模拟工作量趋势数据
const workloadTrendData: WorkloadTrendData[] = [
  { date: '01-15', completedReviews: 15, corrections: 3 },
  { date: '01-16', completedReviews: 12, corrections: 2 },
  { date: '01-17', completedReviews: 18, corrections: 5 },
  { date: '01-18', completedReviews: 14, corrections: 4 },
  { date: '01-19', completedReviews: 16, corrections: 3 },
  { date: '01-20', completedReviews: 13, corrections: 2 },
  { date: '01-21', completedReviews: 11, corrections: 1 }
];

// 通知公告数据
const notifications: Notification[] = [
  {
    id: 1,
    type: 'task',
    title: '紧急复核任务提醒',
    content: '您有3个高优先级复核任务待处理，请及时完成复核工作',
    time: '30分钟前',
    isRead: false,
    priority: 'high'
  },
  {
    id: 2,
    type: 'task',
    title: '复核任务分配通知',
    content: '您本周需要完成80份复核任务，当前进度：56/80，请合理安排时间',
    time: '2小时前',
    isRead: false,
    priority: 'medium'
  },
  {
    id: 3,
    type: 'system',
    title: '系统公告',
    content: '关于五一假期排班及调休的通知已发布，请查看相关安排',
    time: '1天前',
    isRead: true,
    priority: 'medium'
  },
  {
    id: 4,
    type: 'system',
    title: '质检标准更新通知',
    content: '客服规范V2.1版本已发布，新增3项质检规则，请及时学习掌握',
    time: '2天前',
    isRead: true,
    priority: 'low'
  }
];

// 模拟待复核任务数据
const reviewTasks: ReviewTask[] = [
  {
    id: 'RT001',
    recordId: 'QM2501210001',
    agentName: '张小明',
    agentAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhang',
    callTime: '14:30',
    callDuration: '8分32秒',
    aiScore: 73,
    assignedTime: '2024-01-21 15:00',
    reviewType: '投诉处理'
  },
  {
    id: 'RT002',
    recordId: 'QM2501210002',
    agentName: '李小红',
    agentAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=li',
    callTime: '13:45',
    callDuration: '12分18秒',
    aiScore: 78,
    assignedTime: '2024-01-21 14:30',
    reviewType: '合规检查'
  },
  {
    id: 'RT003',
    recordId: 'QM2501210003',
    agentName: '王小华',
    agentAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wang',
    callTime: '12:20',
    callDuration: '6分45秒',
    aiScore: 92,
    assignedTime: '2024-01-21 13:00',
    reviewType: '话术规范'
  },
  {
    id: 'RT004',
    recordId: 'QM2501210004',
    agentName: '赵小刚',
    agentAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhao',
    callTime: '11:15',
    callDuration: '15分22秒',
    aiScore: 85,
    assignedTime: '2024-01-21 12:00',
    reviewType: '服务质量'
  },
  {
    id: 'RT005',
    recordId: 'QM2501210005',
    agentName: '陈小美',
    agentAvatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=chen',
    callTime: '10:30',
    callDuration: '9分56秒',
    aiScore: 68,
    assignedTime: '2024-01-21 11:15',
    reviewType: '销售技巧'
  }
];

/**
 * 复核员主页组件
 */
const FinalReviewerHomePage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getNotificationPriorityStyle = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-red-500 bg-red-50';
      case 'medium':
        return 'border-l-4 border-yellow-500 bg-yellow-50';
      case 'low':
        return 'border-l-4 border-green-500 bg-green-50';
      default:
        return 'border-l-4 border-blue-500 bg-blue-50';
    }
  };

  return (
    <div className="unified-page-container">
      <GlobalStyles />
      <UnifiedPageHeader
        title="复核员视角"
        subtitle="欢迎回来，李复核 | 质检部-复核组"
        icon={UserCheck}
        badge={{ text: "在线", color: "green" }}
        showDesignGuide={true}
        designGuideContent={
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">页面：复核员视角 (FinalReviewerHomePage.tsx) - 业务设计说明</h3>
              <p className="text-gray-700 leading-relaxed">
                该页面是<strong>质检复核员</strong>登录后的个人工作台（Dashboard）。其核心目标是帮助复核员清晰地了解自己的<strong>工作负荷、效率和质量</strong>，并提供快速进入待办任务列表的入口。它强调的是对个人工作状态的监控，而非对他人的绩效评估。
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">1. 页面头部 (UnifiedPageHeader)</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 标题"复核员视角"；显示复核员个人信息（如"欢迎回来，李复核 | 质检部-复核组"）；`UserCheck`图标象征审核与确认；徽章显示复核员的关键状态（如"在线"）。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 复核员信息从用户管理服务获取；在线状态可能与工作流或排班系统集成。</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">2. 核心工作指标 (KPIs)</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <span><strong>业务内容</strong>: 衡量复核员工作量和核心价值的KPI，包括复核总量、待复核量、日均复核量和AI结果纠错数。卡片包含趋势指示和指标说明。</span>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <span><strong>业务计算/实现</strong>: 数据主要来源于复核任务管理系统。</span>
                    <ul>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>复核总量</strong>: 统计周期内已完成的复核任务总数。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>待复核量</strong>: 统计当前分配给该复核员且状态为待处理/进行中的任务数。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>日均复核量</strong>: 复核总量除以周期内工作天数。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>AI结果纠错数</strong>: 统计复核员修正AI初检结果的次数，体现人工复核价值和AI优化点。</span>
                      </li>
                    </ul>
                  </div>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">3. 工作量趋势分析 (折线图)</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 双折线图同时展示"完成复核数"（蓝色）和"纠错数量"（红色）的每日趋势，便于分析工作量波动和复核难度。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 数据来源于每日完成的复核任务统计和AI结果修正记录。前端将数据映射到双折线图，支持Tooltip交互。</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">4. 通知公告</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <span><strong>业务内容</strong>: 聚合与复核员工作直接相关的信息，如紧急任务提醒、任务分配通知、系统公告与标准更新等。采用优先级视觉提示。</span>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 数据从系统通知服务获取并筛选，根据类型和优先级排序展示。</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">5. 待复核任务列表</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <span><strong>业务内容</strong>: 复核员的快速待办列表，展示最新或最高优先级的待复核任务，包含记录ID、被检坐席、通话信息、AI初检分数、质检类型等。</span>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <span><strong>业务计算/实现</strong>: 数据来源于待处理的复核任务分配列表。每条任务末尾的"开始复核"按钮会直接跳转到[多模式会话详情页](复核模式)开始工作。</span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        }
        actions={[
          // {
          //   label: "本周数据",
          //   icon: Calendar,
          //   variant: "outline",
          //   onClick: () => setSelectedPeriod('week')
          // }
        ]}
      />

      {/* 主要内容区域 */}
      <div className="unified-content-area space-y-6">
        {/* 绩效数据概览 */}
        <div className="unified-metric-grid unified-metric-grid-4 unified-section-spacing">
          <MetricCard
            title="复核总量"
            value={performanceData.totalReviews}
            icon={CheckCircle}
            iconColor="text-blue-600"
            iconBgColor="bg-blue-100"
            valueColor="!text-blue-600"
            trend={{
              type: 'up',
              value: '+12',
              text: '较上周 +12'
            }}
            helpInfo={reviewerMetricDescriptions.totalReviews}
          />

          <MetricCard
            title="待复核量"
            value={performanceData.pendingReviews}
            icon={Clock}
            iconColor="text-orange-600"
            iconBgColor="bg-orange-100"
            valueColor="!text-orange-600"
            trend={{
              type: 'up',
              value: '+5',
              text: '较昨日 +5'
            }}
            helpInfo={reviewerMetricDescriptions.pendingReviews}
          />

          <MetricCard
            title="日均复核量"
            value={performanceData.dailyAverageReviews}
            icon={BarChart3}
            iconColor="text-green-600"
            iconBgColor="bg-green-100"
            valueColor="!text-green-600"
            trend={{
              type: 'up',
              value: '+1.2',
              text: '较上周 +1.2'
            }}
            helpInfo={reviewerMetricDescriptions.dailyAverageReviews}
          />

          <MetricCard
            title="AI结果纠错数"
            value={performanceData.aiCorrectionCount}
            icon={Target}
            iconColor="text-purple-600"
            iconBgColor="bg-purple-100"
            valueColor="!text-purple-600"
            trend={{
              type: 'stable',
              value: '',
              text: '纠错准确率 95.2%'
            }}
            helpInfo={reviewerMetricDescriptions.aiCorrectionCount}
          />
        </div>

      {/* 工作量趋势图表 */}
      <Card 
        className="shadow-sm hover:shadow-md transition-shadow duration-200"
        title={
          <Space>
            <TrendingUp className="w-5 h-5 text-blue-600" />
            <span className="font-semibold text-gray-800">工作量趋势</span>
          </Space>
        }
      >
        <ResponsiveContainer width="100%" height={320}>
          <LineChart 
            data={workloadTrendData}
            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
          >
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="#e5e7eb" 
              horizontal={true}
              vertical={false}
            />
            <XAxis 
              dataKey="date" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              dy={10}
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              dx={-10}
            />
            <RechartsTooltip 
              contentStyle={{
                backgroundColor: '#1f2937',
                border: 'none',
                borderRadius: '8px',
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                color: '#ffffff'
              }}
              labelStyle={{ color: '#d1d5db' }}
              formatter={(value: any, name: string) => [
                `${value}${name === '完成复核数' ? '个' : '次'}`, 
                name
              ]}
              labelFormatter={(label) => `日期: ${label}`}
            />
            <Line 
              type="monotone" 
              dataKey="completedReviews" 
              stroke="#3b82f6" 
              strokeWidth={3}
              name="完成复核数"
              dot={{ 
                fill: '#3b82f6', 
                strokeWidth: 2, 
                stroke: '#ffffff',
                r: 4,
                filter: 'drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3))'
              }}
              activeDot={{ 
                r: 6, 
                fill: '#3b82f6',
                stroke: '#ffffff',
                strokeWidth: 3,
                filter: 'drop-shadow(0 4px 8px rgba(59, 130, 246, 0.4))'
              }}
              strokeLinecap="round"
            />
            <Line 
              type="monotone" 
              dataKey="corrections" 
              stroke="#ef4444" 
              strokeWidth={3}
              name="纠错数量"
              dot={{ 
                fill: '#ef4444', 
                strokeWidth: 2, 
                stroke: '#ffffff',
                r: 4,
                filter: 'drop-shadow(0 2px 4px rgba(239, 68, 68, 0.3))'
              }}
              activeDot={{ 
                r: 6, 
                fill: '#ef4444',
                stroke: '#ffffff',
                strokeWidth: 3,
                filter: 'drop-shadow(0 4px 8px rgba(239, 68, 68, 0.4))'
              }}
              strokeLinecap="round"
            />
          </LineChart>
        </ResponsiveContainer>
      </Card>

      {/* 通知公告和待复核任务 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 通知公告 */}
        <Card 
          title={
            <Space>
              <Bell className="w-5 h-5" />
              <span>通知公告</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <div className="space-y-3">
            {notifications.slice(0, 4).map((notification) => (
              <div 
                key={notification.id} 
                className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${
                  getNotificationPriorityStyle(notification.priority)
                }`}
              >
                <div className={`w-2 h-2 rounded-full mt-2 ${notification.isRead ? 'bg-gray-300' : 'bg-blue-500'}`} />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium">{notification.title}</p>
                    {notification.priority === 'high' && (
                      <Badge count="重要" style={{ backgroundColor: '#f5222d' }} />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{notification.content}</p>
                  <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* 待复核任务列表 */}
        <Card 
          title={
            <Space>
              <FileText className="w-5 h-5" />
              <span>待复核任务</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <div className="space-y-3">
            {reviewTasks.slice(0, 5).map((task) => (
              <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{task.recordId}</span>
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                    <span>{task.agentName}</span>
                    <span className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {task.callTime}
                    </span>
                    <span>{task.callDuration}</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">{task.reviewType}</p>
                </div>
                <div className="text-right flex items-center space-x-3">
                  <div>
                    <p className={`text-lg font-bold ${task.aiScore >= 90 ? 'text-green-600' : task.aiScore >= 80 ? 'text-yellow-600' : 'text-red-600'}`}>
                      {task.aiScore}
                    </p>
                    <p className="text-xs text-gray-500">AI评分</p>
                </div>
                <Button type="primary" size="small">
                  开始复核
                </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
        </div>
        </div>
      </div>
    );
  };

  export default FinalReviewerHomePage;

// 添加自定义样式
const styles = `
  .reviewer-metric-help-tooltip .ant-tooltip-inner {
    background-color: #1f2937;
    border-radius: 8px;
    padding: 12px;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = styles;
  document.head.appendChild(styleElement);
}