import React, { useState } from 'react';
import { Filter, ChevronDown, X } from 'lucide-react';

/**
 * 统一查询区域组件的配置接口
 */
export interface FilterField {
  key: string;
  label: string;
  type: 'text' | 'select' | 'dateRange' | 'numberRange' | 'datetime' | 'datetimeRange';
  placeholder?: string | [string, string];
  options?: { value: string; label: string }[];
  width?: string;
  required?: boolean;
}

export interface UnifiedSearchFilterProps {
  fields: FilterField[];
  filters: Record<string, any>;
  onFiltersChange: (filters: Record<string, any>) => void;
  onSearch: () => void;
  onReset: () => void;
  className?: string;
  isExpanded?: boolean;
  onToggleExpanded?: () => void;
  showFilterCount?: boolean;
}

/**
 * 统一的查询筛选区域组件
 */
export const UnifiedSearchFilter: React.FC<UnifiedSearchFilterProps> = ({
  fields,
  filters,
  onFiltersChange,
  onSearch,
  onReset,
  className = '',
  isExpanded = false,
  onToggleExpanded,
  showFilterCount = true
}) => {
  const [internalExpanded, setInternalExpanded] = useState(isExpanded);
  const currentExpanded = onToggleExpanded ? isExpanded : internalExpanded;

  /**
   * 获取当前已选筛选条件数量
   */
  const getActiveFiltersCount = () => {
    let count = 0;
    fields.forEach(field => {
      const value = filters[field.key];
      if (field.type === 'numberRange' || field.type === 'datetimeRange') {
        const minKey = field.key + 'Min';
        const maxKey = field.key + 'Max';
        if (filters[minKey] || filters[maxKey]) count++;
      } else if (field.type === 'dateRange') {
        if (value && value.length === 2 && (value[0] || value[1])) count++;
      } else if (value && value !== '') {
        count++;
      }
    });
    return count;
  };

  /**
   * 处理字段值变化
   */
  const handleFieldChange = (key: string, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  /**
   * 处理展开/收起切换
   */
  const handleToggleExpanded = () => {
    if (onToggleExpanded) {
      onToggleExpanded();
    } else {
      setInternalExpanded(!internalExpanded);
    }
  };

  /**
   * 渲染字段输入组件
   */
  const renderField = (field: FilterField) => {
    const { key, label, type, placeholder, options, width = 'w-full' } = field;
    const value = filters[key];

    switch (type) {
      case 'text':
        return (
          <div key={key} className={width}>
            <label className="block text-sm font-medium text-gray-700 mb-0.5">{label}</label>
            <input
              type="text"
              value={value || ''}
              onChange={(e) => handleFieldChange(key, e.target.value)}
              placeholder={typeof placeholder === 'string' ? placeholder : `请输入${label}`}
              className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        );

      case 'select':
        return (
          <div key={key} className={width}>
            <label className="block text-sm font-medium text-gray-700 mb-0.5">{label}</label>
            <select
              value={value || ''}
              onChange={(e) => handleFieldChange(key, e.target.value)}
              className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
            >
              <option value="">{typeof placeholder === 'string' ? placeholder : `全部${label}`}</option>
              {options?.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
        );

      case 'numberRange':
        const minKey = key + 'Min';
        const maxKey = key + 'Max';
        return (
          <div key={key} className="flex space-x-2">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-0.5">{label}</label>
              <input
                type="number"
                value={filters[minKey] || ''}
                onChange={(e) => handleFieldChange(minKey, e.target.value)}
                placeholder="最低分"
                className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="flex-1 mt-5">
              <input
                type="number"
                value={filters[maxKey] || ''}
                onChange={(e) => handleFieldChange(maxKey, e.target.value)}
                placeholder="最高分"
                className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        );

      case 'datetime':
        return (
          <div key={key} className={width}>
            <label className="block text-sm font-medium text-gray-700 mb-0.5">{label}</label>
            <input
              type="datetime-local"
              value={value || ''}
              onChange={(e) => handleFieldChange(key, e.target.value)}
              className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        );

      case 'datetimeRange':
        const startKey = key + 'Start';
        const endKey = key + 'End';
        return (
          <div key={key} className="flex space-x-2">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-0.5">{label}</label>
              <input
                type="datetime-local"
                value={filters[startKey] || ''}
                onChange={(e) => handleFieldChange(startKey, e.target.value)}
                className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="flex-1 mt-5">
              <input
                type="datetime-local"
                value={filters[endKey] || ''}
                onChange={(e) => handleFieldChange(endKey, e.target.value)}
                className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        );

      case 'dateRange':
        return (
          <div key={key} className={width}>
            <label className="block text-sm font-medium text-gray-700 mb-0.5">{label}</label>
            <div className="flex space-x-2">
              <input
                type="date"
                value={value?.[0] || ''}
                onChange={(e) => handleFieldChange(key, [e.target.value, value?.[1] || ''])}
                placeholder={Array.isArray(placeholder) ? placeholder[0] : ''}
                className="flex-1 px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="date"
                value={value?.[1] || ''}
                onChange={(e) => handleFieldChange(key, [value?.[0] || '', e.target.value])}
                placeholder={Array.isArray(placeholder) ? placeholder[1] : ''}
                className="flex-1 px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* 筛选器头部 */}
      <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleToggleExpanded}
            className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
          >
            <Filter className="w-5 h-5" />
            <span className="font-medium">筛选条件</span>
            <ChevronDown className={`w-4 h-4 transform transition-transform ${currentExpanded ? 'rotate-180' : ''}`} />
          </button>
          {showFilterCount && getActiveFiltersCount() > 0 && (
            <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
              {getActiveFiltersCount()} 个筛选条件
            </span>
          )}
        </div>
      </div>

      {/* 筛选条件内容 */}
      {currentExpanded && (
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 mb-4">
            {fields.map(renderField)}
          </div>
          
          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-3 pt-3 border-t border-gray-200">
            <button
              onClick={onReset}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              重置
            </button>
            <button
              onClick={onSearch}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              查询
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default UnifiedSearchFilter;