import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, Descriptions, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Badge } from 'antd';
import { AlertTriangle, Clock, Hash, HelpCircle, User, Users, Phone, FileText, Link as LinkIcon, Edit, CheckCircle, Eye, Shield, Bot, UserCircle as UserAvatar, PlayCircle, PauseCircle } from 'lucide-react';
import { Link } from 'react-router-dom';

export interface FollowUpTaskInfo {
    id: string;
    title: string;
    status: 'completed' | 'pending' | 'overdue';
    assignee: string;
    dueDate: string;
}

export interface ContextLine {
    speaker: 'agent' | 'customer';
    text: string;
    timestamp: number;
    isHighlight?: boolean;
}

export interface AlertDetail {
    id: string;
    triggerTime: string;
    ruleName: string;
    ruleDescription: string;
    level: 'critical' | 'high' | 'medium' | 'low';
    agent: { name: string; id: string; team: string };
    customer: { phone: string; };
    callId: string;
    disposition: 'ignored' | 'task_created' | 'unread' | 'processing' | 'resolved' | 'read';
    handler?: string;
    handleTime?: string;
    taskInfo?: FollowUpTaskInfo;
    contextSnippet: ContextLine[];
}

interface AlertDetailDrawerProps {
    alert: AlertDetail | null;
    visible: boolean;
    onClose: () => void;
    onAction?: (alertId: string, action: 'mark-read' | 'mark-processing' | 'create-task') => void;
    isReadOnly: boolean;
}

const levelMap = {
    critical: { text: '严重', color: 'volcano', icon: <AlertTriangle className="w-4 h-4 text-red-600" /> },
    high: { text: '高', color: 'orange', icon: <AlertTriangle className="w-4 h-4 text-orange-500" /> },
    medium: { text: '中', color: 'gold', icon: <AlertTriangle className="w-4 h-4 text-yellow-500" /> },
    low: { text: '低', color: 'blue', icon: <AlertTriangle className="w-4 h-4 text-blue-500" /> },
};

const dispositionMap = {
    unread: { text: '新预警', color: 'blue' },
    processing: { text: '处理中', color: 'gold' },
    ignored: { text: '已忽略 (历史)', color: 'default' },
    read: { text: '已读', color: 'default' },
    task_created: { text: '已创建任务', color: 'purple' },
    resolved: { text: '已解决', color: 'green' },
};

const taskStatusMap = {
    completed: { text: '已完成', color: 'green' },
    pending: { text: '处理中', color: 'blue' },
    overdue: { text: '已逾期', color: 'red' },
};

const RuleDescription = ({ description }: { description: string }) => (
    <div style={{ maxWidth: 300 }}>
        <p className="font-semibold mb-2">规则说明</p>
        <p className="text-gray-600">{description}</p>
    </div>
);

export const AlertDetailDrawer: React.FC<AlertDetailDrawerProps> = ({ alert, visible, onClose, onAction, isReadOnly }) => {
    if (!alert) return null;

    const { level, ruleName, ruleDescription, triggerTime, agent, customer, callId, contextSnippet, disposition, handler, handleTime, taskInfo } = alert;
    const { text: levelText, color: levelColor, icon: levelIcon } = levelMap[level];
    const { text: dispositionText, color: dispositionColor } = dispositionMap[disposition] || dispositionMap.ignored;

    const [playingTimestamp, setPlayingTimestamp] = useState<number | null>(null);

    const handlePlayPause = (timestamp: number) => {
        if (playingTimestamp === timestamp) {
            setPlayingTimestamp(null); // Pause
        } else {
            setPlayingTimestamp(timestamp); // Play
            // In a real app, you would add logic here to seek the audio player to the timestamp.
        }
    };

    const drawerTitle = (
        <div className="flex items-center justify-between">
            <div className="flex flex-col">
                <span className="text-lg font-bold text-gray-800">预警详情</span>
                <span className="text-xs text-gray-500 font-mono">ID: {alert.id}</span>
            </div>
            <Badge color={dispositionColor} text={dispositionText} />
        </div>
    );
    
    const footerActions = !isReadOnly ? (
        <div className="flex justify-end space-x-2">
            <Button icon={<Eye className="w-4 h-4" />} onClick={() => onAction?.(alert.id, 'mark-read')}>标记已读</Button>
            <Button icon={<CheckCircle className="w-4 h-4" />} onClick={() => onAction?.(alert.id, 'mark-processing')}>标记处理中</Button>
            <Button type="primary" icon={<Edit className="w-4 h-4" />} onClick={() => onAction?.(alert.id, 'create-task')}>创建跟进任务</Button>
        </div>
    ) : null;

    return (
        <Drawer
            title={drawerTitle}
            width={720}
            onClose={onClose}
            open={visible}
            bodyStyle={{ backgroundColor: '#f9fafb', padding: 0 }}
            footer={footerActions}
            destroyOnClose
        >
            <div className="p-6 space-y-6">
                <Card bordered={false} className="shadow-sm">
                    <div className="flex items-start space-x-4">
                        <div className={`p-2 rounded-full bg-${levelColor}-100`}>{levelIcon}</div>
                        <div className="flex-grow">
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="预警等级">
                                    <Tag color={levelColor}>{levelText}</Tag>
                                </Descriptions.Item>
                                <Descriptions.Item label="触发时间">{triggerTime}</Descriptions.Item>
                                <Descriptions.Item label="触发规则" span={2}>
                                    <div className="flex items-center space-x-2">
                                        <span>{ruleName}</span>
                                        <Popover content={<RuleDescription description={ruleDescription} />} title="规则详情" trigger="hover">
                                            <HelpCircle className="w-4 h-4 text-gray-400 cursor-pointer" />
                                        </Popover>
                                    </div>
                                </Descriptions.Item>
                            </Descriptions>
                        </div>
                    </div>
                </Card>

                <Card title={<div className="font-semibold flex items-center"><FileText className="w-5 h-5 mr-2 text-blue-600" />事发上下文</div>} bordered={false} className="shadow-sm">
                    <Descriptions column={2} size="small">
                        <Descriptions.Item label="关联坐席" span={2}>
                            <div className="flex items-center space-x-2">
                                <User className="w-4 h-4 text-gray-500" />
                                <span>{`${agent.name} (ID: ${agent.id})`}</span>
                                <Tag>{agent.team}</Tag>
                            </div>
                        </Descriptions.Item>
                        <Descriptions.Item label="客户信息">
                            <div className="flex items-center space-x-2">
                                <Phone className="w-4 h-4 text-gray-500" />
                                <span>{customer.phone}</span>
                            </div>
                        </Descriptions.Item>
                        <Descriptions.Item label="关联通话ID">
                           <div className="flex items-center space-x-2">
                                <Hash className="w-4 h-4 text-gray-500" />
                                <span className="font-mono">{callId}</span>
                                <Tooltip title="查看完整通话详情">
                                    <Link to={`/final-design/multi-mode-session-detail/${callId}`} target="_blank" className="text-blue-600 hover:text-blue-800">
                                        <LinkIcon className="w-4 h-4" />
                                    </Link>
                                </Tooltip>
                            </div>
                        </Descriptions.Item>
                    </Descriptions>
                    <div className="mt-4 pt-4 border-t border-gray-200">
                        <p className="text-sm font-semibold text-gray-700 mb-2">上下文摘要:</p>
                        <div className="space-y-1 bg-gray-50 p-3 rounded-lg max-h-64 overflow-y-auto custom-scrollbar">
                            {contextSnippet.map((line, index) => {
                                const isPlaying = playingTimestamp === line.timestamp;
                                return (
                                    <div 
                                        key={index} 
                                        className={`flex items-start gap-3 p-2 rounded-md transition-all duration-200 ${line.isHighlight ? 'bg-yellow-100/70' : 'hover:bg-gray-200/50'} ${isPlaying ? 'bg-blue-100' : ''}`}
                                    >
                                        <button onClick={() => handlePlayPause(line.timestamp)} className="mt-0.5 text-gray-500 hover:text-blue-600 transition-colors">
                                            {isPlaying 
                                                ? <PauseCircle className="w-5 h-5 text-blue-600" /> 
                                                : <PlayCircle className="w-5 h-5" />}
                                        </button>
                                        {line.speaker === 'agent' 
                                            ? <Bot className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" /> 
                                            : <UserAvatar className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />}
                                        <div className="flex-1">
                                            <div className="flex justify-between items-center">
                                                <p className={`font-semibold text-sm ${line.speaker === 'agent' ? 'text-blue-700' : 'text-green-700'}`}>
                                                    {line.speaker === 'agent' ? '坐席' : '客户'}
                                                </p>
                                                <span className="text-xs text-gray-400 font-mono">
                                                    {new Date(line.timestamp * 1000).toISOString().substr(14, 5)}
                                                </span>
                                            </div>
                                            <p className={`text-gray-800 text-sm ${line.isHighlight ? 'font-bold' : ''}`}>{line.text}</p>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </Card>

                <Card title={<div className="font-semibold flex items-center"><Shield className="w-5 h-5 mr-2 text-purple-600" />处置与跟进记录</div>} bordered={false} className="shadow-sm">
                    <Descriptions column={2} size="small">
                        <Descriptions.Item label="处置状态" span={2}>
                           <Tag color={dispositionColor}>{dispositionText}</Tag>
                        </Descriptions.Item>
                        {handler && <Descriptions.Item label="处置人">{handler}</Descriptions.Item>}
                        {handleTime && <Descriptions.Item label="处置时间">{handleTime}</Descriptions.Item>}
                    </Descriptions>

                    {taskInfo && (
                         <div className="mt-4 pt-4 border-t border-gray-200">
                             <p className="text-sm font-semibold text-gray-700 mb-2">关联的跟进任务:</p>
                             <Card size="small" className="bg-purple-50 border-purple-200">
                                <Descriptions column={2} size="small">
                                    <Descriptions.Item label="任务标题" span={2}>{taskInfo.title}</Descriptions.Item>
                                    <Descriptions.Item label="任务状态">
                                        <Tag color={taskStatusMap[taskInfo.status].color}>{taskStatusMap[taskInfo.status].text}</Tag>
                                    </Descriptions.Item>
                                    <Descriptions.Item label="负责人">{taskInfo.assignee}</Descriptions.Item>
                                    <Descriptions.Item label="截止日期">{taskInfo.dueDate}</Descriptions.Item>
                                    <Descriptions.Item label="操作">
                                        <Link to={`/final-design/task-detail/${taskInfo.id}`} className="text-blue-600 hover:underline">查看任务详情</Link>
                                    </Descriptions.Item>
                                </Descriptions>
                             </Card>
                         </div>
                    )}
                </Card>
            </div>
        </Drawer>
    );
}; 