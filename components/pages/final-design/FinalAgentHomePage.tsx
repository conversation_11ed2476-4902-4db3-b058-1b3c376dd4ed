import React, { useState } from 'react';
import { Card, Button, Typography, Space, Divider, Progress, Avatar, Badge, Tooltip } from 'antd';
import { 
  TrendingUp, 
  TrendingDown,
  AlertTriangle, 
  CheckCircle,
  Clock,
  Phone,
  Award,
  Target,
  BarChart3,
  Calendar,
  Bell,
  FileText,
  User,
  Users,
  MessageSquare,
  XCircle,
  HelpCircle
} from 'lucide-react';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import { MetricCard } from './common/MetricCard';
import { MetricDescription } from './common/MetricHelpButton';
import { GlobalStyles } from './common/GlobalStyles';

const { Text } = Typography;

// 模拟数据
const performanceData = {
  averageScore: 88.5,
  teamRank: 3,
  totalTeamMembers: 15,
  qualificationRate: 92.3,
  appealCount: 2,
  appealSuccessRate: 50.0,
  firstPassRate: 89.7
};

const trendData = [
  { date: '01-15', score: 85.2 },
  { date: '01-16', score: 87.1 },
  { date: '01-17', score: 86.8 },
  { date: '01-18', score: 89.3 },
  { date: '01-19', score: 88.5 },
  { date: '01-20', score: 90.1 },
  { date: '01-21', score: 88.9 }
];

const seriousErrors = [
  { name: '未核实客户身份', count: 2, severity: 'high' },
  { name: '违规承诺退款', count: 1, severity: 'high' },
  { name: '未经授权查询客户信息', count: 1, severity: 'high' },
  { name: '泄露客户隐私信息', count: 0, severity: 'high' },
  { name: '违规操作系统权限', count: 0, severity: 'high' }
];

const frequentDeductions = [
  { name: '服务态度不佳', count: 5, points: -10 },
  { name: '未主动询问需求', count: 3, points: -6 },
  { name: '话术不规范', count: 2, points: -4 },
  { name: '响应时间过长', count: 2, points: -4 },
  { name: '未做总结确认', count: 1, points: -2 }
];

// 统一通知接口定义
interface Notification {
  id: number;
  type: 'system' | 'task' | 'announcement' | 'result' | 'appeal';
  title: string;
  content: string;
  time: string;
  isRead: boolean;
  priority: 'high' | 'medium' | 'low';
}

const notifications: Notification[] = [
  {
    id: 1,
    type: 'result',
    title: '质检结果通知',
    content: '您的质检记录 QM2507210001 已出分，最终得分 88.5 分',
    time: '1小时前',
    isRead: false,
    priority: 'high'
  },
  {
    id: 2,
    type: 'result',
    title: '质检结果通知',
    content: '您的质检记录 QM2507200095 已出分，最终得分 92.0 分',
    time: '1天前',
    isRead: true,
    priority: 'medium'
  },
  {
    id: 3,
    type: 'system',
    title: '系统公告',
    content: '关于五一假期排班及调休的通知已发布，请全体员工查看',
    time: '2天前',
    isRead: true,
    priority: 'medium'
  },
  {
    id: 4,
    type: 'system',
    title: '质检标准更新通知',
    content: '客服规范V2.1版本已发布，新增3项质检规则，请及时学习掌握',
    time: '3天前',
    isRead: true,
    priority: 'low'
  }
];

const recentScores = [
  {
    id: 'QM2507210001',
    task: '日常质检任务-0121',
    customerPhone: '138****5678',
    callTime: '14:30',
    duration: '5分42秒',
    finalScore: 90.5,
    result: 'qualified',
    appealStatus: 'none'
  },
  {
    id: 'QM2507210002',
    task: '日常质检任务-0121',
    customerPhone: '139****9012',
    callTime: '10:15',
    duration: '3分28秒',
    finalScore: 85.0,
    result: 'qualified',
    appealStatus: 'none'
  },
  {
    id: 'QM2507200003',
    task: '日常质检任务-0120',
    customerPhone: '136****3456',
    callTime: '16:45',
    duration: '7分15秒',
    finalScore: 78.5,
    result: 'unqualified',
    appealStatus: 'processing'
  }
];

// 指标说明数据
const metricDescriptions: Record<string, MetricDescription> = {
  averageScore: {
    title: "平均分数",
    description: "在特定时间段内，坐席员所有被质检录音的平均得分。",
    calculation: "平均分数 = 周期内所有质检项得分总和 / 周期内已完成的质检总次数"
  },
  teamRank: {
    title: "团队排名",
    description: "坐席员在所属团队中，基于某项关键指标（通常是平均分数）的相对位置。",
    calculation: "将团队内所有成员的平均分进行降序排列后，确定该坐席员所处的位置。"
  },
  qualificationRate: {
    title: "质检合格率",
    description: "在特定时间段内，质检结果为合格的次数占总质检次数的百分比。",
    calculation: "质检合格率 = ( 周期内质检合格的次数 / 周期内已完成的质检总次数 ) × 100%"
  },
  firstPassRate: {
    title: "一次性通过率",
    description: "在特定时间段内，首次质检即合格，且后续未发起申诉或申诉未成功的次数占总质检次数的百分比。",
    calculation: "一次性通过率 = ( 首次质检即合格的次数 / 周期内已完成的质检总次数 ) × 100%"
  },
  appealCount: {
    title: "申诉次数",
    description: "在特定时间段内，坐席员对质检结果发起的申诉总次数。",
    calculation: "统计周期内发起的申诉流程数量。"
  },
  appealSuccessRate: {
    title: "申诉成功率",
    description: "在特定时间段内，申诉成功（即质检结果被修改）的次数占总申诉次数的百分比。",
    calculation: "申诉成功率 = ( 周期内申诉成功的次数 / 周期内发起的总申诉次数 ) × 100%"
  }
};



const FinalAgentHomePage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 获取通知优先级样式
  const getNotificationPriorityStyle = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-red-500 bg-red-50';
      case 'medium':
        return 'border-l-4 border-yellow-500 bg-yellow-50';
      case 'low':
        return 'border-l-4 border-green-500 bg-green-50';
      default:
        return 'border-l-4 border-blue-500 bg-blue-50';
    }
  };

  const getResultBadge = (result: string) => {
    return result === 'qualified' ? (
      <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">合格</span>
    ) : (
      <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">不合格</span>
    );
  };

  const getAppealStatusBadge = (status: string) => {
    switch (status) {
      case 'processing':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">申诉中</span>;
      case 'success':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">申诉成功</span>;
      case 'failed':
        return <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">申诉失败</span>;
      default:
        return null;
    }
  };

  return (
    <div className="unified-page-container">
      <GlobalStyles />
      <UnifiedPageHeader
        title="坐席视角"
        subtitle="欢迎回来，张三 | 客服部-A班组"
        icon={User}
        badge={{ text: "在线", color: "green" }}
        showDesignGuide={true}
        designGuideContent={
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">页面：坐席员视角 (FinalAgentHomePage.tsx) - 业务设计说明</h3>
              <p className="text-gray-700 leading-relaxed">
                该页面是智能质检系统为<strong>客服坐席</strong>精心打造的个人工作台，其核心业务目标是：<strong>为坐席提供一个全面、直观、可行动的个人绩效仪表盘</strong>，帮助他们快速了解自身表现、发现问题、接收关键通知，并明确改进方向。页面设计旨在促进坐席的自我管理和持续成长。
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">1. 页面头部 (UnifiedPageHeader)</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 显示"坐席员视角"作为页面的主要功能定位；提供个性化的欢迎信息（例如："欢迎回来，张三 | 客服部-A班组"）；展示坐席的当前工作状态（如"在线"）。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 欢迎信息和班组信息通常从用户管理或组织架构服务中获取；工作状态是实时业务状态，可能与呼叫中心系统或排班系统集成。</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">2. 核心绩效指标 (KPIs)</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 包括平均分数、团队排名、质检合格率、一次性通过率、申诉次数和申诉成功率。每个指标都附带趋势指示器（上升/下降/持平）和与上个周期的对比值。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 数据来源于质检结果数据库和申诉管理系统，后端服务会根据考核周期聚合数据。
                    <ul>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>平均分数</strong>: 汇总周期内所有已出分质检任务的最终得分并计算平均值。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>团队排名</strong>: 计算班组内所有坐席的平均分后进行降序排列确定位次。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>合格率</strong>: 统计周期内质检结果为"合格"的记录数除以总质检记录数。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>一次性通过率</strong>: 需标记首次质检结果并追踪后续申诉情况。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>申诉次数/成功率</strong>: 从申诉管理模块获取并统计申诉工单数量和成功率。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>趋势计算</strong>: 比较当前周期与上一个同周期的数据。</span>
                      </li>
                    </ul>
                  </span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">3. 成绩趋势分析 (折线图)</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>: 可视化展现坐席近期（例如：近7天）每日质检平均分数的波动和变化趋势。</span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 数据来源于质检结果数据库，后端服务按天汇总平均分数，前端将"日期-分数"数据映射到折线图。</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">4. 错误分析统计</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>:
                    <ul>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>严重错误项 Top5</strong>: 坐席最频繁触犯的、业务影响最严重的五项质检规则及其发生次数（"红线"问题）。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>高频失分项 Top5</strong>: 导致坐席被扣分次数最多的五项质检规则及其发生次数（影响绩效提升的关键点）。</span>
                      </li>
                    </ul>
                  </span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>: 数据来源于质检记录中的扣分项明细和规则违规记录。质检规则需包含"严重性"或"扣分值"属性。
                    <ul>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>统计与排序</strong>: 遍历坐席质检记录，根据规则属性统计违规/扣分次数，并按次数降序取前五项。</span>
                      </li>
                    </ul>
                  </span>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">5. 通知公告与近期成绩</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务内容</strong>:
                    <ul>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>通知公告</strong>: 展示与坐席相关的最新系统通知、质检结果通知、质检标准更新等，按优先级区分。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>近期成绩</strong>: 列出坐席最近完成的几条质检记录，包含质检ID、最终得分、合格/不合格结果、申诉状态及通话基本信息。</span>
                      </li>
                    </ul>
                  </span>
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <span><strong>业务计算/实现</strong>:
                    <ul>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>通知数据</strong>: 从系统通知服务获取并筛选与当前坐席相关的未读或最新通知。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>近期成绩数据</strong>: 从质检结果数据库查询最新已出分质检记录。</span>
                      </li>
                      <li className="flex items-start ml-4">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span><strong>交互逻辑</strong>: 点击通知或成绩记录时，应触发路由跳转到对应的详情页；"查看全部"按钮导航到完整的通知中心或历史质检成绩页面。</span>
                      </li>
                    </ul>
                  </span>
                </li>
              </ul>
            </div>
          </div>
        }
        actions={[
          // {
          //   label: "本周数据",
          //   icon: Calendar,
          //   variant: "outline",
          //   onClick: () => setSelectedPeriod('week')
          // }
        ]}
      />

      {/* 主要内容区域 */}
      <div className="unified-content-area space-y-6">

        {/* 绩效数据概览 */}
        <div className="unified-metric-grid unified-metric-grid-3 unified-section-spacing">
          <MetricCard
            title="平均分数"
            value={performanceData.averageScore}
            icon={Award}
            iconColor="text-blue-600"
            iconBgColor="bg-blue-100"
            valueColor="!text-blue-600"
            trend={{
              type: 'up',
              value: '+2.3',
              text: '较上周 +2.3'
            }}
            helpInfo={metricDescriptions.averageScore}
          />

          <MetricCard
            title="团队排名"
            value={`${performanceData.teamRank}/${performanceData.totalTeamMembers}`}
            icon={Users}
            iconColor="text-purple-600"
            iconBgColor="bg-purple-100"
            valueColor="!text-purple-600"
            trend={{
              type: 'up',
              value: '1位',
              text: '上升 1 位'
            }}
            helpInfo={metricDescriptions.teamRank}
          />

          <MetricCard
            title="质检合格率"
            value={`${performanceData.qualificationRate}%`}
            icon={CheckCircle}
            iconColor="text-green-600"
            iconBgColor="bg-green-100"
            valueColor="!text-green-600"
            trend={{
              type: 'up',
              value: '+1.5%',
              text: '较上周 +1.5%'
            }}
            helpInfo={metricDescriptions.qualificationRate}
          />

          <MetricCard
            title="一次性通过率"
            value={`${performanceData.firstPassRate}%`}
            icon={BarChart3}
            iconColor="text-orange-600"
            iconBgColor="bg-orange-100"
            valueColor="!text-orange-600"
            trend={{
              type: 'down',
              value: '-1.2%',
              text: '较上周 -1.2%'
            }}
            helpInfo={metricDescriptions.firstPassRate}
          />

          <MetricCard
            title="申诉次数"
            value={performanceData.appealCount}
            icon={MessageSquare}
            iconColor="text-red-600"
            iconBgColor="bg-red-100"
            valueColor="!text-red-600"
            trend={{
              type: 'stable',
              value: '',
              text: '本周期内发起申诉'
            }}
            helpInfo={metricDescriptions.appealCount}
          />

          <MetricCard
            title="申诉成功率"
            value={`${performanceData.appealSuccessRate}%`}
            icon={CheckCircle}
            iconColor="text-yellow-600"
            iconBgColor="bg-yellow-100"
            valueColor="!text-yellow-600"
            trend={{
              type: 'stable',
              value: '',
              text: '1/2 申诉成功'
            }}
            helpInfo={metricDescriptions.appealSuccessRate}
          />
        </div>

      {/* 分数趋势 */}
       <Card 
         title={
           <Space>
             <TrendingUp className="w-5 h-5 text-blue-600" />
             <span className="text-gray-800 font-semibold">成绩趋势分析</span>
           </Space>
         }
         className="border-0 shadow-sm hover:shadow-md transition-shadow duration-200"
       >
         <ResponsiveContainer width="100%" height={280}>
           <LineChart 
             data={trendData}
             margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
           >
             <CartesianGrid 
               strokeDasharray="3 3" 
               stroke="#e5e7eb" 
               strokeOpacity={0.6}
               horizontal={true}
               vertical={false}
             />
             <XAxis 
               dataKey="date" 
               axisLine={false}
               tickLine={false}
               tick={{ fontSize: 12, fill: '#6b7280' }}
               dy={10}
             />
             <YAxis 
               domain={[75, 95]} 
               axisLine={false}
               tickLine={false}
               tick={{ fontSize: 12, fill: '#6b7280' }}
               dx={-10}
               tickFormatter={(value) => `${value}分`}
             />
             <RechartsTooltip 
               contentStyle={{
                 backgroundColor: '#ffffff',
                 border: '1px solid #e5e7eb',
                 borderRadius: '8px',
                 boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                 fontSize: '14px'
               }}
               labelStyle={{ color: '#374151', fontWeight: '600' }}
               formatter={(value: any, name: string) => [
                 <span style={{ color: '#3b82f6', fontWeight: '600' }}>{value}分</span>,
                 '质检分数'
               ]}
               labelFormatter={(label) => `日期: ${label}`}
             />
             <Line 
               type="monotone" 
               dataKey="score" 
               stroke="#3b82f6" 
               strokeWidth={3}
               dot={{ 
                 fill: '#3b82f6', 
                 strokeWidth: 2, 
                 stroke: '#ffffff',
                 r: 5
               }}
               activeDot={{ 
                 r: 7, 
                 fill: '#1d4ed8',
                 stroke: '#ffffff',
                 strokeWidth: 3,
                 style: { filter: 'drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3))' }
               }}
               strokeLinecap="round"
               strokeLinejoin="round"
             />
           </LineChart>
         </ResponsiveContainer>
       </Card>

       {/* 错误分析 */}
       <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
         {/* 严重错误项 */}
         <Card 
           title={
             <Space>
               <AlertTriangle className="w-5 h-5 text-red-500" />
               <span>严重错误项 Top5</span>
             </Space>
           }
         >
           <div className="space-y-2">
             {seriousErrors.slice(0, 5).map((error, index) => (
               <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded-lg">
                 <div className="flex items-center space-x-2">
                   <span className="w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                     {index + 1}
                   </span>
                   <span className="text-sm font-medium">{error.name}</span>
                 </div>
                 <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    error.count > 0 ? "bg-red-100 text-red-800" : "bg-green-100 text-green-800"
                  }`}>
                    {error.count} 次
                  </span>
               </div>
             ))}
           </div>
         </Card>

         {/* 高频失分项 */}
         <Card 
           title={
             <Space>
               <XCircle className="w-5 h-5 text-orange-500" />
               <span>高频失分项 Top5</span>
             </Space>
           }
         >
           <div className="space-y-2">
             {frequentDeductions.slice(0, 5).map((item, index) => (
               <div key={index} className="flex items-center justify-between p-2 bg-orange-50 rounded-lg">
                 <div className="flex items-center space-x-2">
                   <span className="w-6 h-6 bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                     {index + 1}
                   </span>
                   <span className="text-sm font-medium">{item.name}</span>
                 </div>
                 <span className="px-2 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-800">
                   {item.count} 次
                 </span>
               </div>
             ))}
           </div>
         </Card>
       </div>

      {/* 通知公告和近期成绩 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 通知公告 */}
        <Card 
          title={
            <Space>
              <Bell className="w-5 h-5" />
              <span>通知公告</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <div className="space-y-3">
            {notifications.slice(0, 4).map((notification) => (
              <div 
                key={notification.id} 
                className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${
                  getNotificationPriorityStyle(notification.priority)
                }`}
              >
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  notification.isRead ? 'bg-gray-300' : 'bg-blue-500'
                }`} />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium">{notification.title}</p>
                    {notification.priority === 'high' && (
                      <Badge count="重要" style={{ backgroundColor: '#f5222d' }} />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{notification.content}</p>
                  <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* 近期成绩列表 */}
        <Card 
          title={
            <Space>
              <BarChart3 className="w-5 h-5" />
              <span>近期成绩</span>
            </Space>
          }
          extra={<Button type="default" size="small">查看全部</Button>}
        >
          <div className="space-y-2">
            {recentScores.map((score) => (
              <div key={score.id} className="flex items-center justify-between p-2 border rounded-lg hover:bg-gray-50">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{score.id}</span>
                    {getResultBadge(score.result)}
                    {getAppealStatusBadge(score.appealStatus)}
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                    <span className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {score.callTime}
                    </span>
                    <span>{score.duration}</span>
                    <span>{score.task}</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-lg font-bold ${getScoreColor(score.finalScore)}`}>
                    {score.finalScore}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
      </div>
    </div>
  );
};

export default FinalAgentHomePage;

// 添加全局样式
const styles = `
  .metric-help-tooltip .ant-tooltip-inner {
    background: #1f2937;
    border-radius: 8px;
    padding: 12px;
    max-width: 300px;
  }
  
  .metric-help-tooltip .ant-tooltip-arrow::before {
    background: #1f2937;
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = styles;
  document.head.appendChild(styleElement);
}