import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { ClipboardCheck } from 'lucide-react';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';
import UnifiedPageHeader from './components/UnifiedPageHeader';

/**
 * 复核任务数据接口
 */
interface ReviewTask {
  id: string;
  recordNumber: string;
  taskName: string;
  agentName: string;
  agentId: string;
  teamName: string; // 所属班组
  callDuration: string;
  reviewReason: string;
  aiScore: number;
  finalScore?: number; // 最终得分
  assignTime: string;
  processingTime?: string; // 处理时间
  status: 'pending' | 'completed';
  qualityResult?: '合格' | '不合格'; // 新增质检结果
}

/**
 * 搜索条件接口
 */
interface SearchFilters {
  recordNumber: string; // 记录编号
  taskName: string;
  agentName: string; // 被检坐席
  teamName: string; // 所属班组
  reviewReason: string;
  aiScoreMin: string;
  aiScoreMax: string;
  assignTime: string[];
  finalScoreMin: string;
  finalScoreMax: string;
  qualityResult: '' | '合格' | '不合格'; // 新增质检结果筛选
}

/**
 * 选项数据
 */
const agentOptions = [
  { name: '张三', id: '10001', team: 'A组' },
  { name: '李四', id: '10002', team: 'A组' },
  { name: '王五', id: '10003', team: 'B组' },
  { name: '赵六', id: '10004', team: 'B组' },
];

const teamOptions = ['A组', 'B组', 'C组'];
const reviewReasonOptions = ['低分触发复核', '随机抽样复核', '关键词触发复核'];

/**
 * 模拟数据
 */
const mockData: ReviewTask[] = Array.from({ length: 4 }, (_, index) => {
  const id = (index + 1).toString().padStart(3, '0');
  const isCompleted = index >= 2;
  const aiScore = Math.floor(Math.random() * 41) + 60;

  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * 7));
  
  const assignDate = new Date(date.getTime() + (15 + Math.floor(Math.random() * 45)) * 60000);
  const assignTime = assignDate.toISOString().replace('T', ' ').substring(0, 19);

  const processingDate = isCompleted ? new Date(assignDate.getTime() + (60 * 60000 * (Math.random() * 24))) : undefined;
  const processingTime = processingDate?.toISOString().replace('T', ' ').substring(0, 19);
  
  const durationMinutes = Math.floor(Math.random() * 17) + 3;
  const durationSeconds = Math.floor(Math.random() * 60);
  const callDuration = `${durationMinutes.toString().padStart(2, '0')}:${durationSeconds.toString().padStart(2, '0')}`;

  const agent = agentOptions[Math.floor(Math.random() * agentOptions.length)];
  const finalScore = isCompleted ? Math.min(100, aiScore + Math.floor(Math.random() * 10) - 3) : undefined;
  const scoreToEvaluate = finalScore !== undefined ? finalScore : aiScore;
  const qualityResult = scoreToEvaluate >= 80 ? '合格' : '不合格';

  return {
    id: `TASK_${id}`,
    recordNumber: `QM250702${id}`,
    taskName: `客服质检任务_${Math.floor(index / 5) + 1}`,
    agentName: agent.name,
    agentId: agent.id,
    teamName: agent.team,
    callDuration,
    reviewReason: reviewReasonOptions[Math.floor(Math.random() * reviewReasonOptions.length)],
    aiScore,
    finalScore,
    assignTime,
    processingTime,
    status: isCompleted ? 'completed' : 'pending',
    qualityResult,
  };
});

/**
 * 最终设计 - 我的复核任务列表页
 */
export const FinalMyReviewTasksPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'pending' | 'completed'>('pending');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(true);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    recordNumber: '',
    taskName: '',
    agentName: '',
    teamName: '',
    reviewReason: '',
    aiScoreMin: '',
    aiScoreMax: '',
    assignTime: ['', ''],
    finalScoreMin: '',
    finalScoreMax: '',
    qualityResult: '',
  });

  // 筛选字段配置
  const filterFields: FilterField[] = [
    { key: 'recordNumber', label: '记录编号', type: 'text' as const, placeholder: '请输入记录编号' },
    { key: 'taskName', label: '所属任务', type: 'text' as const, placeholder: '请输入任务名称' },
    { key: 'agentName', label: '被检坐席', type: 'text' as const, placeholder: '请输入坐席姓名或工号' },
    { key: 'teamName', label: '所属班组', type: 'text' as const, placeholder: '请输入班组名称' },
    { key: 'reviewReason', label: '触发复核原因', type: 'select' as const, options: reviewReasonOptions.map(reason => ({ value: reason, label: reason })) },
    { key: 'qualityResult', label: '质检结果', type: 'select' as const, options: [
      { value: '合格', label: '合格' },
      { value: '不合格', label: '不合格' }
    ]},
    { key: 'aiScore', label: 'AI初检得分', type: 'numberRange' as const },
    { key: 'assignTime', label: '分配时间', type: 'dateRange' as const },
    ...(activeTab === 'completed' ? [{ key: 'finalScore', label: '最终得分', type: 'numberRange' as const }] : [])
  ];

  /**
   * 过滤和分页数据
   */
  const filteredAndPaginatedData = useMemo(() => {
    let filtered = mockData.filter(task => {
      // 状态过滤
      if (task.status !== activeTab) return false;
      
      // 记录编号过滤
      if (searchFilters.recordNumber && !task.recordNumber.includes(searchFilters.recordNumber)) return false;

      // 任务名称过滤
      if (searchFilters.taskName && !task.taskName.includes(searchFilters.taskName)) return false;
      
      // 坐席过滤
      if (searchFilters.agentName && !`${task.agentName} ${task.agentId}`.includes(searchFilters.agentName)) return false;
      
      // 班组过滤
      if (searchFilters.teamName && !task.teamName.includes(searchFilters.teamName)) return false;

      // 复核原因过滤
      if (searchFilters.reviewReason && task.reviewReason !== searchFilters.reviewReason) return false;
      
      // AI得分过滤
      if (searchFilters.aiScoreMin && task.aiScore < parseInt(searchFilters.aiScoreMin)) return false;
      if (searchFilters.aiScoreMax && task.aiScore > parseInt(searchFilters.aiScoreMax)) return false;
      
      // 质检结果过滤
      if (searchFilters.qualityResult && task.qualityResult !== searchFilters.qualityResult) return false;
      
      // 分配时间过滤
      if (searchFilters.assignTime[0] && new Date(task.assignTime) < new Date(searchFilters.assignTime[0])) return false;
      if (searchFilters.assignTime[1] && new Date(task.assignTime) > new Date(searchFilters.assignTime[1])) return false;

      // '已完成'页签下的额外过滤条件
      if (activeTab === 'completed') {
        if (searchFilters.finalScoreMin && task.finalScore && task.finalScore < parseInt(searchFilters.finalScoreMin, 10)) return false;
        if (searchFilters.finalScoreMax && task.finalScore && task.finalScore > parseInt(searchFilters.finalScoreMax, 10)) return false;
      }
      
      return true;
    });

    const total = filtered.length;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const currentData = filtered.slice(startIndex, endIndex);
    const totalPages = Math.ceil(total / pageSize);

    return { data: currentData, total, totalPages };
  }, [activeTab, searchFilters, currentPage, pageSize]);

  /**
   * 处理筛选条件变化
   */
  const handleFiltersChange = (newFilters: Record<string, any>) => {
    setSearchFilters(newFilters as SearchFilters);
  };

  /**
   * 处理查询
   */
  const handleSearch = () => {
    setCurrentPage(1);
  };

  /**
   * 重置搜索条件
   */
  const handleReset = () => {
    setSearchFilters({
      recordNumber: '',
      taskName: '',
      agentName: '',
      teamName: '',
      reviewReason: '',
      aiScoreMin: '',
      aiScoreMax: '',
      assignTime: ['', ''],
      finalScoreMin: '',
      finalScoreMax: '',
      qualityResult: '',
    });
    setCurrentPage(1);
  };

  /**
   * 处理任务
   */
  const handleProcessTask = (taskId: string, status: 'pending' | 'completed') => {
    const viewMode = status === 'pending' ? 'review' : 'review_view';
    navigate(`/final-design/review-workstation/${taskId}?viewMode=${viewMode}&status=${status}`);
  };

  return (
    <div className="min-h-screen bg-gray-50/50">
      <UnifiedPageHeader
        title="我的复核任务"
        subtitle="管理和处理分配给我的质检复核任务"
        icon={ClipboardCheck}
        badge={{ text: `待处理 ${mockData.filter(t => t.status === 'pending').length}`, color: "yellow" }}
      />
      <main className="p-6 md:p-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        >
          {/* 统一查询区域 */}
          <UnifiedSearchFilter
            fields={filterFields}
            filters={searchFilters}
            onFiltersChange={handleFiltersChange}
            onSearch={handleSearch}
            onReset={handleReset}
          />

          {/* Tab切换和表格区域 */}
          <div className="mt-6">
            {/* Tab切换 */}
            <div className="border-b border-gray-200">
              <div className="flex">
                <button
                  onClick={() => setActiveTab('pending')}
                  className={`px-6 py-3 font-medium text-sm border-b-2 ${
                    activeTab === 'pending'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  待处理 ({mockData.filter(t => t.status === 'pending').length})
                </button>
                <button
                  onClick={() => setActiveTab('completed')}
                  className={`px-6 py-3 font-medium text-sm border-b-2 ${
                    activeTab === 'completed'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  已完成 ({mockData.filter(t => t.status === 'completed').length})
                </button>
              </div>
            </div>

            {/* 表格 */}
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left text-gray-600">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                  <tr>
                    <th scope="col" className="px-4 py-3">序号</th>
                    <th scope="col" className="px-6 py-3">记录编号</th>
                    <th scope="col" className="px-6 py-3">所属任务</th>
                    <th scope="col" className="px-6 py-3">被检坐席</th>
                    <th scope="col" className="px-6 py-3">所属班组</th>
                    <th scope="col" className="px-6 py-3">通话时长</th>
                    <th scope="col" className="px-6 py-3">触发复核原因</th>
                    <th scope="col" className="px-6 py-3">AI初检得分</th>
                    {activeTab === 'completed' && <th scope="col" className="px-6 py-3">最终得分</th>}
                    <th scope="col" className="px-6 py-3">质检结果</th>
                    <th scope="col" className="px-6 py-3">分配时间</th>
                    {activeTab === 'completed' && <th scope="col" className="px-6 py-3">处理时间</th>}
                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAndPaginatedData.data.map((task, index) => (
                    <tr key={task.id} className="bg-white border-b hover:bg-gray-50">
                      <td className="px-4 py-4 text-gray-700">
                        {(currentPage - 1) * pageSize + index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-medium">
                        {task.recordNumber}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {task.taskName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {`${task.agentName}/${task.agentId}`}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {task.teamName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {task.callDuration}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className="px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded-full">
                          {task.reviewReason}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`font-medium ${
                          task.aiScore >= 80 ? 'text-green-600' :
                          task.aiScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {task.aiScore}
                        </span>
                      </td>
                      {activeTab === 'completed' && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`font-bold ${
                            task.finalScore && task.finalScore >= 80 ? 'text-blue-700' :
                            task.finalScore && task.finalScore >= 60 ? 'text-yellow-700' : 'text-red-700'
                          }`}>
                            {task.finalScore ?? 'N/A'}
                          </span>
                        </td>
                      )}
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          task.qualityResult === '合格' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {task.qualityResult}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {task.assignTime}
                      </td>
                      {activeTab === 'completed' && (
                         <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {task.processingTime ?? 'N/A'}
                        </td>
                      )}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        {activeTab === 'pending' ? (
                          <button
                            onClick={() => handleProcessTask(task.id, 'pending')}
                            className="px-3 py-1 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            处理
                          </button>
                        ) : (
                          <button
                            onClick={() => handleProcessTask(task.id, 'completed')}
                            className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                          >
                            查看
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {filteredAndPaginatedData.data.length === 0 && (
              <div className="text-center py-10 text-gray-500">
                <p>未找到匹配的任务。</p>
              </div>
            )}

            {/* 分页 */}
           {filteredAndPaginatedData.data.length > 0 && (
             <UnifiedPagination
               current={1}
               total={filteredAndPaginatedData.data.length}
               pageSize={10}
               onChange={() => {}}
             />
           )}
          </div>
        </motion.div>
      </main>
    </div>
  );
};

export default FinalMyReviewTasksPage;