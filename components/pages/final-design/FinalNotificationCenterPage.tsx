import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Bell, 
  Search, 
  Filter,
  CheckCircle,
  Circle,
  Trash2,
  AlertTriangle,
  FileText,
  Settings,
  Megaphone,
  Eye,
  EyeOff,
  Calendar,
  MoreHorizontal,
  ExternalLink,
  RotateCcw,
  X,
  <PERSON><PERSON>,
  ClipboardList
} from 'lucide-react';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';

/**
 * 通知类型枚举
 */
enum NotificationType {
  TASK_REMINDER = 'task_reminder',    // 任务提醒
  RESULT_NOTIFICATION = 'result_notification', // 结果通知
  SYSTEM_ANNOUNCEMENT = 'system_announcement', // 系统公告
  REALTIME_ALERT = 'realtime_alert',         // 新增：实时预警通知
}

/**
 * 通知项接口定义
 */
interface NotificationItem {
  id: string;
  type: NotificationType;
  title: string;
  content: string;
  time: string;
  isRead: boolean;
  actionUrl?: string;
  taskStatus?: 'todo' | 'done';
}

/**
 * 筛选条件接口
 */
interface FilterConditions {
  searchText: string;
  readStatus: 'all' | 'read' | 'unread';
  dateRange: [string | null, string | null];
}

/**
 * 通知分类配置
 */
const notificationCategories = [
  {
    key: 'all',
    name: '全部通知',
    icon: Bell,
    type: 'all' as const
  },
  {
    key: 'realtime_alert',
    name: '实时预警',
    icon: Siren,
    type: NotificationType.REALTIME_ALERT
  },
  {
    key: 'task_reminder',
    name: '任务提醒',
    icon: Settings,
    type: NotificationType.TASK_REMINDER
  },
  {
    key: 'result_notification',
    name: '结果通知',
    icon: FileText,
    type: NotificationType.RESULT_NOTIFICATION
  },
  {
    key: 'system_announcement',
    name: '系统公告',
    icon: Megaphone,
    type: NotificationType.SYSTEM_ANNOUNCEMENT
  }
];

/**
 * 模拟通知数据
 */
const mockNotifications: NotificationItem[] = [
  {
    id: '1',
    type: NotificationType.TASK_REMINDER,
    title: '新的复核任务',
    content: '系统向您分配了 5 条新的复核任务，请尽快处理。',
    time: '2024-01-15 14:30:00',
    isRead: false,
    actionUrl: '/final-design/my-review-tasks'
  },
  {
    id: '2',
    type: NotificationType.TASK_REMINDER,
    title: '申诉待处理',
    content: '您收到一条新的坐席申诉待处理。申诉人：[A班组-张三]，记录编号：[QM2507020003]。',
    time: '2024-01-15 13:45:00',
    isRead: false,
    actionUrl: '/final-design/appeal-processing'
  },
  {
    id: '3',
    type: NotificationType.RESULT_NOTIFICATION,
    title: '质检成绩发布',
    content: '您的质检记录 [QM2507020001] 已出分，最终得分 88 分，请查收。',
    time: '2024-01-15 12:20:00',
    isRead: true,
    actionUrl: '/final-design/review-workstation/QM2507020001?viewMode=performance_check'
  },
  {
    id: '4',
    type: NotificationType.RESULT_NOTIFICATION,
    title: '申诉处理结果',
    content: '您对记录 [QM2507010095] 提起的申诉已有处理结果：申诉成功。',
    time: '2024-01-15 11:15:00',
    isRead: false,
    actionUrl: '/final-design/review-workstation/QM2507010095?viewMode=performance_check'
  },
  {
    id: '5',
    type: NotificationType.SYSTEM_ANNOUNCEMENT,
    title: '假期排班通知',
    content: '关于五一假期排班及调休的通知 已发布，请全体员工查看。',
    time: '2024-01-14 18:00:00',
    isRead: false,
    actionUrl: '/announcements/holiday-schedule-2024'
  },
  {
    id: '6',
    type: NotificationType.SYSTEM_ANNOUNCEMENT,
    title: '系统维护公告',
    content: '系统将于本周六凌晨2:00-6:00进行例行维护，期间服务可能中断。',
    time: '2024-01-14 16:30:00',
    isRead: true,
    actionUrl: '/announcements/system-maintenance-2024'
  },
  {
    id: '7',
    type: NotificationType.REALTIME_ALERT,
    title: '[严重] 客户提及"投诉"关键词',
    content: '坐席 [李四 (B组)] 的通话 [QM2507151008] 中，客户提及高风险关键词"投诉"。摘要："你们这样天天骚扰，我受不了了！再打电话过来，我就要去银监会投诉你们！"',
    time: '2024-01-15 15:10:05',
    isRead: false,
    actionUrl: '/final-design/multi-mode-session-detail/QM2507151008?viewMode=basic_view&timestamp=125'
  },
  {
    id: '8',
    type: NotificationType.TASK_REMINDER,
    title: '新的预警跟进任务已指派',
    content: '关于[李四]触发[客户投诉预警]的跟进任务已指派给您，请在今天内处理。',
    time: '2024-01-15 15:12:00',
    isRead: false,
    actionUrl: '/final-design/alert-follow-up-center',
    taskStatus: 'todo'
  },
  {
    id: '9',
    type: NotificationType.TASK_REMINDER,
    title: '跟进任务已完成',
    content: '关于[王五]触发[长时间静音]的跟进任务已被处理完成。',
    time: '2024-01-14 10:00:00',
    isRead: true,
    actionUrl: '/final-design/alert-follow-up-center',
    taskStatus: 'done'
  }
];



/**
 * 通知中心页面组件
 */
export const FinalNotificationCenterPage: React.FC = () => {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<NotificationItem[]>(mockNotifications);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedNotifications, setSelectedNotifications] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const [filters, setFilters] = useState<FilterConditions>({
    searchText: '',
    readStatus: 'all',
    dateRange: [null, null]
  });

  /**
   * 根据筛选条件过滤通知
   */
  const filteredNotifications = useMemo(() => {
    return notifications.filter(notification => {
      // 分类筛选
      if (selectedCategory !== 'all' && notification.type !== selectedCategory) {
        return false;
      }

      // 搜索文本筛选
      if (filters.searchText && 
          !notification.title.toLowerCase().includes(filters.searchText.toLowerCase()) &&
          !notification.content.toLowerCase().includes(filters.searchText.toLowerCase())) {
        return false;
      }

      // 读取状态筛选
      if (filters.readStatus === 'read' && !notification.isRead) {
        return false;
      }
      if (filters.readStatus === 'unread' && notification.isRead) {
        return false;
      }

      // 时间范围筛选
      const notificationDate = new Date(notification.time);
      const [startDate, endDate] = filters.dateRange;
      if (startDate && notificationDate < new Date(startDate)) {
        return false;
      }
      if (endDate) {
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999); // 包含结束当天
        if (notificationDate > end) {
          return false;
        }
      }

      return true;
    });
  }, [notifications, selectedCategory, filters]);

  /**
   * 分页后的通知列表
   */
  const paginatedNotifications = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredNotifications.slice(startIndex, endIndex);
  }, [filteredNotifications, currentPage, pageSize]);

  /**
   * 总页数
   */
  const totalPages = Math.ceil(filteredNotifications.length / pageSize);

  /**
   * 计算各分类的未读数量
   */
  const unreadCounts = useMemo(() => {
    const counts = {
      all: 0,
      [NotificationType.REALTIME_ALERT]: 0,
      [NotificationType.TASK_REMINDER]: 0,
      [NotificationType.RESULT_NOTIFICATION]: 0,
      [NotificationType.SYSTEM_ANNOUNCEMENT]: 0
    };

    notifications.forEach(notification => {
      if (!notification.isRead) {
        counts.all++;
        if (Object.prototype.hasOwnProperty.call(counts, notification.type)) {
          (counts as any)[notification.type]++;
        }
      }
    });

    return counts;
  }, [notifications]);

  /**
   * 切换通知选中状态
   */
  const toggleNotificationSelection = (notificationId: string) => {
    const newSelected = new Set(selectedNotifications);
    if (newSelected.has(notificationId)) {
      newSelected.delete(notificationId);
    } else {
      newSelected.add(notificationId);
    }
    setSelectedNotifications(newSelected);
  };

  /**
   * 全选/取消全选（仅当前页）
   */
  const toggleSelectAll = () => {
    // 检查当前页的通知是否全部已选中
    const currentPageSelected = paginatedNotifications.filter(n => selectedNotifications.has(n.id));
    const allCurrentPageSelected = currentPageSelected.length === paginatedNotifications.length && paginatedNotifications.length > 0;
    
    if (allCurrentPageSelected) {
      // 取消选中当前页的所有通知
      const newSelected = new Set(selectedNotifications);
      paginatedNotifications.forEach(n => newSelected.delete(n.id));
      setSelectedNotifications(newSelected);
    } else {
      // 选中当前页的所有通知
      const newSelected = new Set(selectedNotifications);
      paginatedNotifications.forEach(n => newSelected.add(n.id));
      setSelectedNotifications(newSelected);
    }
  };

  /**
   * 标记为已读/未读
   */
  const markAsRead = (notificationIds: string[], isRead: boolean) => {
    setNotifications(prev => 
      prev.map(notification => 
        notificationIds.includes(notification.id)
          ? { ...notification, isRead }
          : notification
      )
    );
  };

  /**
   * 删除通知
   */
  const deleteNotifications = (notificationIds: string[]) => {
    setNotifications(prev => prev.filter(n => !notificationIds.includes(n.id)));
    setSelectedNotifications(new Set());
  };

  /**
   * 批量标记为已读
   */
  const markAllAsRead = () => {
    const unreadIds = filteredNotifications
      .filter(n => !n.isRead)
      .map(n => n.id);
    markAsRead(unreadIds, true);
  };

  /**
   * 清空所有通知
   */
  const clearAllNotifications = () => {
    const idsToDelete = filteredNotifications.map(n => n.id);
    deleteNotifications(idsToDelete);
  };

  /**
   * 获取通知类型的显示名称
   */
  const getNotificationTypeName = (type: NotificationType) => {
    const category = notificationCategories.find(cat => cat.type === type);
    return category?.name || type;
  };

  /**
   * 处理通知点击跳转
   */
  const handleNotificationClick = (notification: NotificationItem) => {
    // 标记为已读
    if (!notification.isRead) {
      markAsRead([notification.id], true);
    }
    
    // 跳转到对应页面
    if (notification.actionUrl) {
      // 判断是否为外部链接
      if (notification.actionUrl.startsWith('http') || notification.actionUrl.startsWith('/announcements')) {
        window.open(notification.actionUrl, '_blank');
      } else {
        // 内部路由跳转
        navigate(notification.actionUrl);
      }
    }
  };

  /**
   * 重置筛选条件
   */
  const resetFilters = () => {
    setFilters({
      searchText: '',
      readStatus: 'all',
      dateRange: [null, null]
    });
    setCurrentPage(1);
    setSelectedNotifications(new Set());
  };

  /**
   * 执行搜索
   */
  const handleSearch = () => {
    setCurrentPage(1);
    setSelectedNotifications(new Set());
  };

  /**
   * 切换页码
   */
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // 切换页面时清除当前选择
    setSelectedNotifications(new Set());
  };

  const filterFields: FilterField[] = [
      {
          key: 'searchText',
          label: '搜索内容',
          type: 'text',
          placeholder: '搜索通知内容...',
      },
      {
          key: 'readStatus',
          label: '读取状态',
          type: 'select',
          placeholder: '全部状态',
          options: [
              { value: 'all', label: '全部状态' },
              { value: 'unread', label: '未读' },
              { value: 'read', label: '已读' },
          ],
      },
      {
          key: 'dateRange',
          label: '时间范围',
          type: 'dateRange',
          placeholder: ['开始日期', '结束日期'],
      },
  ];

  const handleFiltersChange = (newFilters: Record<string, any>) => {
      setFilters(newFilters as FilterConditions);
      setCurrentPage(1); // Reset to first page on filter change
  };

  return (
    <div className="min-h-screen bg-gray-50/50">
      <UnifiedPageHeader
        title="通知中心"
        subtitle="查看和管理系统通知消息"
        icon={Bell}
        badge={{ text: `未读 ${unreadCounts.all}`, color: "red" }}
        actions={[
          {
            label: "全部已读",
            icon: CheckCircle,
            variant: "outline",
            onClick: markAllAsRead
          },
          {
            label: "清空通知",
            icon: Trash2,
            variant: "outline",
            onClick: clearAllNotifications
          }
        ]}
      />

      <main className="p-6 md:p-10">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="flex h-full">
        {/* 左侧分类导航 */}
        <div className="w-64 bg-white border-r border-gray-200 flex-shrink-0">
          <div className="p-4">
            <h2 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
              通知分类
            </h2>
            <nav className="space-y-1">
              {notificationCategories.map(category => {
                const Icon = category.icon;
                const unreadCount = category.type === 'all' 
                  ? unreadCounts.all 
                  : unreadCounts[category.type as keyof typeof unreadCounts] || 0;
                
                return (
                  <button
                    key={category.key}
                    onClick={() => {
                      setSelectedCategory(category.key);
                      setCurrentPage(1);
                      setSelectedNotifications(new Set());
                    }}
                    className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-md transition-colors ${
                      selectedCategory === category.key
                        ? 'bg-blue-100 text-blue-700 font-medium'
                        : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="w-4 h-4" />
                      <span>{category.name}</span>
                    </div>
                    {unreadCount > 0 && (
                      <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] h-5 flex items-center justify-center">
                        {unreadCount}
                      </span>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* 右侧主内容区 */}
        <div className="flex-1 flex flex-col">
          {/* 筛选条件 */}
          <div className="p-6 border-b border-gray-200">
            <UnifiedSearchFilter
                fields={filterFields}
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onSearch={handleSearch}
                onReset={resetFilters}
                showFilterCount={false}
            />
          </div>

          {/* 操作栏 */}
          <div className="bg-white border-b border-gray-200 px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={paginatedNotifications.length > 0 && paginatedNotifications.every(n => selectedNotifications.has(n.id))}
                    onChange={toggleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">全选</span>
                </label>
                
                {selectedNotifications.size > 0 && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => markAsRead(Array.from(selectedNotifications), true)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      标为已读
                    </button>
                    <button
                      onClick={() => markAsRead(Array.from(selectedNotifications), false)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      标为未读
                    </button>
                    <button
                      onClick={() => deleteNotifications(Array.from(selectedNotifications))}
                      className="text-sm text-red-600 hover:text-red-800"
                    >
                      删除选中
                    </button>
                  </div>
                )}
              </div>


            </div>
          </div>

          {/* 通知列表 */}
          <div className="flex-1 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64">
                <Bell className="w-12 h-12 text-gray-400 mb-4" />
                <p className="text-gray-500 text-lg">暂无通知</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {paginatedNotifications.map(notification => {
                 const isFollowUpTask = !!notification.taskStatus;
                 return (
                 <div
                   key={notification.id}
                   className={`p-4 transition-colors ${
                     !notification.isRead
                       ? 'bg-blue-50 hover:bg-gray-50' 
                       : 'bg-white hover:bg-gray-50'
                   }`}
                 >
                  <div className="flex items-start space-x-3">
                    {/* 选择框 */}
                    <input
                      type="checkbox"
                      checked={selectedNotifications.has(notification.id)}
                      onChange={() => toggleNotificationSelection(notification.id)}
                      className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />

                    {/* 已读/未读状态 */}
                    <div className="mt-1">
                      {notification.isRead ? (
                        <CheckCircle className="w-4 h-4 text-gray-400" />
                      ) : (
                        <Circle className="w-4 h-4 text-blue-500" />
                      )}
                    </div>

                                             {/* 通知内容 */}
                     <div 
                       className={`flex-1 min-w-0 ${
                         notification.actionUrl ? 'cursor-pointer' : ''
                       }`}
                       onClick={() => notification.actionUrl && handleNotificationClick(notification)}
                     >
                       <div className="flex items-center space-x-2 mb-1">
                         <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                             notification.type === NotificationType.REALTIME_ALERT
                             ? 'bg-red-100 text-red-800'
                             : notification.type === NotificationType.TASK_REMINDER
                             ? 'bg-yellow-100 text-yellow-800'
                             : notification.type === NotificationType.RESULT_NOTIFICATION
                             ? 'bg-green-100 text-green-800'
                             : 'bg-gray-100 text-gray-800'
                         }`}>
                           {getNotificationTypeName(notification.type)}
                         </span>
                       </div>
                       <h3 className={`text-sm font-medium mb-1 ${
                           notification.isRead
                           ? 'text-gray-700' 
                           : 'text-gray-900'
                       } ${notification.actionUrl ? 'hover:text-blue-600' : ''}`}>
                         {notification.title}
                       </h3>
                       <p className={`text-sm mb-2 ${
                         notification.isRead ? 'text-gray-500' : 'text-gray-700'
                       }`}>
                         {notification.content}
                       </p>
                       <div className="flex items-center justify-between">
                         <span className="text-xs text-gray-400 flex items-center">
                           <Calendar className="w-3 h-3 mr-1" />
                           {notification.time}
                         </span>
                       </div>
                     </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => markAsRead([notification.id], !notification.isRead)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title={notification.isRead ? "标为未读" : "标为已读"}
                      >
                        {notification.isRead ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                      <button
                        onClick={() => deleteNotifications([notification.id])}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="删除"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
                );
              })}
              </div>
            )}
          </div>

          {/* 分页 */}
          {totalPages > 1 && (
            <UnifiedPagination
              current={currentPage}
              pageSize={pageSize}
              total={filteredNotifications.length}
              onChange={(page) => handlePageChange(page)}
            />
          )}
        </div>
        </div>
      </div>
    </main>
  </div>
  );
};
