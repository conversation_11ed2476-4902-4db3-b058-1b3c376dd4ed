import React, { useEffect } from 'react';
import {
    Card, Input, DatePicker, Button, Space, Radio, Tree, Select, Row, Col, Typography,
    Form, TimePicker, message
} from 'antd';
import { motion, AnimatePresence } from 'framer-motion';
import { CloseOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Title } = Typography;

// Mock Data for Data Sources, inspired by DataSourceManagementPage
const mockDataSources = [
    {
      id: '1',
      name: '呼叫中心录音SFTP目录',
      type: 'directory',
    },
    {
      id: '2',
      name: '客服系统A通话记录API',
      type: 'api',
    },
    {
      id: '3',
      name: '呼叫中心业务数据库',
      type: 'database',
    }
];

// Mock Data for Tree Select
const treeData = [
    {
        title: '客服中心',
        key: '0-0',
        children: [
            { title: '客服A组', key: '0-0-0' },
            { title: '客服B组', key: '0-0-1' },
        ],
    },
    {
        title: '营销中心',
        key: '0-1',
        children: [{ title: '营销A组', key: '0-1-0' }],
    },
];

interface FinalCreatePlanPageProps {
    onClose: () => void;
    onSubmit: (values: any) => void;
    planId?: string;
    initialValues?: any;
}

const drawerVariants = {
    hidden: { x: '100%' },
    visible: { x: 0 },
};

const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
}

/**
 * 质检计划管理 - 新建/编辑计划页
 * @constructor
 */
const FinalCreatePlanPage: React.FC<FinalCreatePlanPageProps> = ({ onClose, onSubmit, planId, initialValues }) => {
    const isEditing = !!planId;
    const [form] = Form.useForm();

    const dataSourceId = Form.useWatch('dataSourceId', form);
    const qualityMode = Form.useWatch('qualityMode', form);
    const targetType = Form.useWatch('targetType', form);

    useEffect(() => {
        if (isEditing) {
            // Mock data for editing - in a real app, you'd fetch this
            const mockEditData = {
                planName: '每周服务质量抽检计划 (编辑中)',
                planDescription: '对服务部门进行10%的随机抽检',
                frequency: 'weekly',
                executionTime: dayjs('09:00:00', 'HH:mm:ss'),
                dataSourceId: '1',
                dynamicDateRange: 'lastWeek',
                targetType: 'byStruct',
                targetKeys: ['0-0-0', '0-0-1'],
                qualityScheme: 'scheme1',
                qualityMode: 'sampling',
                samplingRatio: 10,
            };
            form.setFieldsValue(initialValues || mockEditData);
        } else {
            // Default values for new plan
            form.setFieldsValue({
                frequency: 'daily',
                dataSourceId: '1',
                dynamicDateRange: 'yesterday',
                targetType: 'all',
                qualityMode: 'full',
                samplingRatio: 10,
                ...(initialValues || {})
            });
        }
    }, [isEditing, form, initialValues]);

    const onFinish = (values: any) => {
        onSubmit({ ...values, planId });
    };

    return (
        <AnimatePresence>
            <motion.div
                variants={backdropVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                className="fixed inset-0 z-50 flex justify-end bg-black bg-opacity-40"
                onClick={onClose}
            >
                <motion.div
                    variants={drawerVariants}
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    className="bg-gray-50 h-full w-full max-w-4xl shadow-2xl flex flex-col"
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex-shrink-0 flex items-center justify-between p-6 border-b border-gray-200 bg-white">
                        <h2 className="text-xl font-semibold text-gray-800">{isEditing ? "编辑质检计划" : "新建质检计划"}</h2>
                        <button
                            onClick={onClose}
                            className="p-2 text-gray-400 rounded-full hover:bg-gray-100 hover:text-gray-600 transition-colors"
                        >
                            <CloseOutlined className="w-6 h-6" />
                        </button>
                    </div>

                    <Form form={form} layout="vertical" onFinish={onFinish} className="flex-grow flex flex-col overflow-hidden">
                        <div className="flex-grow p-6 space-y-6 overflow-y-auto">

                            <Card title={<Title level={5}>第一部分：基本信息</Title>}>
                                <Form.Item label="计划名称" name="planName" required rules={[{ required: true }]}>
                                    <Input placeholder="例如：每日营销流程合规检查" />
                                </Form.Item>
                                <Form.Item label="计划描述" name="planDescription">
                                    <Input.TextArea rows={2} placeholder="可选，用于备注计划目的等" />
                                </Form.Item>
                            </Card>

                            <Card title={<Title level={5}>第二部分：执行计划</Title>}>
                                <Row gutter={16}>
                                    <Col span={8}>
                                        <Form.Item label="重复频率" name="frequency" required>
                                            <Select>
                                                <Select.Option value="daily">每天</Select.Option>
                                                <Select.Option value="weekly">每周</Select.Option>
                                                <Select.Option value="monthly">每月</Select.Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label="执行时间" name="executionTime" required>
                                            <TimePicker className="w-full" format="HH:mm" />
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label="结束于" name="endDate">
                                            <DatePicker className="w-full" placeholder="不填则永不结束" />
                                        </Form.Item>
                                    </Col>
                                </Row>
                            </Card>

                            <Card title={<Title level={5}>第三部分：质检范围</Title>}>
                                <Form.Item label="数据来源" name="dataSourceId" required>
                                    <Select placeholder="请选择一个数据来源">
                                        {mockDataSources.map(ds => (
                                            <Select.Option key={ds.id} value={ds.id}>{ds.name}</Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>

                                {!!dataSourceId && <>
                                    <Form.Item label="通话时间范围" name="dynamicDateRange" required help="计划任务必须选择动态时间范围">
                                        <Select placeholder="选择相对时间规则">
                                            <Select.Option value="last24h">过去24小时</Select.Option>
                                            <Select.Option value="yesterday">昨天</Select.Option>
                                            <Select.Option value="thisWeek">本周</Select.Option>
                                            <Select.Option value="lastWeek">上周</Select.Option>
                                            <Select.Option value="thisMonth">本月</Select.Option>
                                            <Select.Option value="lastMonth">上月</Select.Option>
                                        </Select>
                                    </Form.Item>
                                    <Form.Item label="质检对象" name="targetType" required>
                                        <Radio.Group>
                                            <Radio value="all">全部坐席</Radio>
                                            <Radio value="byStruct">按组织架构选择</Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                    {targetType === 'byStruct' && (
                                        <Form.Item name="targetKeys" valuePropName="checkedKeys">
                                            <Tree checkable defaultExpandAll treeData={treeData} />
                                        </Form.Item>
                                    )}
                                </>}
                            </Card>

                            <Card title={<Title level={5}>第四部分：质检规则</Title>}>
                                <Form.Item label="质检方案" name="qualityScheme" required rules={[{ required: true }]}>
                                    <Select placeholder="请选择一个质检方案">
                                        <Select.Option value="scheme1">标准服务流程质检方案</Select.Option>
                                        <Select.Option value="scheme2">营销流程质检方案</Select.Option>
                                    </Select>
                                </Form.Item>
                                <Form.Item label="质检模式" name="qualityMode" required>
                                    <Radio.Group>
                                        <Radio value="full">全量质检</Radio>
                                        <Radio value="sampling">抽样质检</Radio>
                                    </Radio.Group>
                                </Form.Item>
                                {qualityMode === 'sampling' && (
                                    <Form.Item label="抽样比例" name="samplingRatio">
                                        <Input type='number' addonAfter="%" style={{ maxWidth: 150 }} />
                                    </Form.Item>
                                )}
                            </Card>
                        </div>
                        <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-gray-50">
                            <div className="flex justify-end gap-3">
                                <Button onClick={onClose}>取消</Button>
                                <Button type="primary" htmlType="submit">
                                    {isEditing ? '保存计划' : '创建计划'}
                                </Button>
                            </div>
                        </div>
                    </Form>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

export default FinalCreatePlanPage;