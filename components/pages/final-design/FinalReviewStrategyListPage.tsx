import React, { useState, useEffect } from 'react';
import { Button, Space, Tag, Tooltip, message, Popconfirm, Switch } from 'antd';
import { Edit, Trash2, Plus, Eye, Settings } from 'lucide-react';
import { motion } from 'framer-motion';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';
import FinalCreateReviewStrategyPage from './FinalCreateReviewStrategyPage';

/**
 * 复核策略配置 - 策略列表页
 * @constructor
 */
const FinalReviewStrategyListPage: React.FC = () => {
    interface ReviewStrategy {
        key: string;
        name: string;
        description: string;
        conditions: string;
        fullConditions?: string[]; // Optional, for detailed tooltip
        allocation: string;
        status: 'enabled' | 'disabled';
        creator: string;
        createTime: string;
    }

    interface SearchFilters {
        strategyName: string;
        status: 'all' | 'enabled' | 'disabled';
    }

    const initialDataSource: ReviewStrategy[] = [
        {
            key: '1',
            name: '低分录音复核策略',
            description: '总分低于60分的录音将自动进入复核流程',
            conditions: 'AI初检得分 < 60',
            allocation: '轮询分配给 张三,李四',
            status: 'enabled',
            creator: '张主管',
            createTime: '2023-10-15 11:30',
        },
        {
            key: '2',
            name: '合规声明复核策略',
            description: '未进行合规声明的录音将自动进入复核流程',
            conditions: '命中规则 [合规声明-录音告知]',
            allocation: '指定分配给 王五',
            status: 'enabled',
            creator: '风控部门',
            createTime: '2023-09-01 09:00',
        },
        {
            key: '3',
            name: '高分录音复核策略',
            description: '总分高于95分的录音将自动进入复核流程',
            conditions: 'AI初检得分 > 95',
            allocation: '指定分配给 李四',
            status: 'disabled',
            creator: '李经理',
            createTime: '2023-11-20 16:00',
        },
        {
            key: '4',
            name: '严重违规复核策略',
            description: '所有"严重违规"的规则命中后都将进入复核',
            conditions: '质检规则重要程度 是 严重违规',
            allocation: '指定分配给 王五',
            status: 'enabled',
            creator: '风控部门',
            createTime: '2023-12-01 10:00',
        },
        {
            key: '5',
            name: '长通话复核策略',
            description: '通话时长超过300秒的录音将进入复核',
            conditions: '通话时长 > 300秒',
            allocation: '指定分配给 王五',
            status: 'enabled',
            creator: '李经理',
            createTime: '2023-12-05 11:00',
        },
        {
            key: '6',
            name: '新人复核策略',
            description: '班组A的新人需要全量复核',
            conditions: '班组 是 班组A',
            allocation: '指定分配给 张主管',
            status: 'enabled',
            creator: '培训部',
            createTime: '2023-12-10 15:00',
        },
        {
            key: '7',
            name: 'VIP客户复核策略',
            description: '特定VIP客户的通话将进入复核',
            conditions: '客户号码 是 188********',
            allocation: '指定分配给 张主管',
            status: 'enabled',
            creator: '销售部',
            createTime: '2023-12-12 10:00',
        }
    ];

    const [strategies, setStrategies] = useState<ReviewStrategy[]>(initialDataSource);
    const [drawerOpen, setDrawerOpen] = useState(false);
    const [editingStrategyId, setEditingStrategyId] = useState<string | undefined>(undefined);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
    });
    const [searchFilters, setSearchFilters] = useState<SearchFilters>({ strategyName: '', status: 'all' });

    const filteredStrategies = React.useMemo(() => {
        return strategies.filter(strategy => {
            const matchesSearch = strategy.name.toLowerCase().includes(searchFilters.strategyName.toLowerCase());
            const matchesStatus = searchFilters.status === 'all' || strategy.status === searchFilters.status;
            return matchesSearch && matchesStatus;
        });
    }, [strategies, searchFilters.strategyName, searchFilters.status]);

    const paginatedStrategies = filteredStrategies.slice(
        (pagination.current - 1) * pagination.pageSize,
        pagination.current * pagination.pageSize
    );

    const handleCreate = () => {
        setEditingStrategyId(undefined);
        setDrawerOpen(true);
    };

    const handleEdit = (strategyId: string) => {
        setEditingStrategyId(strategyId);
        setDrawerOpen(true);
    };

    const handleFormSubmit = (values: any) => {
        if (editingStrategyId) {
            setStrategies(strategies.map(s => s.key === editingStrategyId ? { ...s, ...values, name: values.strategyName, description: values.strategyDescription } : s));
        } else {
            const newStrategy: ReviewStrategy = {
                key: String(Date.now()),
                name: values.strategyName,
                description: values.strategyDescription,
                conditions: '新条件',
                allocation: '新分配',
                status: 'enabled',
                creator: '当前用户',
                createTime: new Date().toLocaleString(),
            };
            setStrategies([newStrategy, ...strategies]);
        }
        setDrawerOpen(false);
    };

    const handleDelete = (key: string) => {
        setStrategies(strategies.filter(s => s.key !== key));
        message.success('删除成功');
    };

    const handleStatusToggle = (key: string) => {
        setStrategies(strategies.map(strategy => {
            if (strategy.key === key) {
                const newStatus = strategy.status === 'enabled' ? 'disabled' : 'enabled';
                message.success(`${newStatus === 'enabled' ? '启用' : '禁用'}成功`);
                return { ...strategy, status: newStatus };
            }
            return strategy;
        }));
    };

    const filterFields: FilterField[] = [
        {
            key: 'strategyName',
            label: '策略名称',
            type: 'text',
            placeholder: '按策略名称搜索...',
        },
        {
            key: 'status',
            label: '状态',
            type: 'select',
            placeholder: '所有状态',
            options: [
                { value: 'all', label: '所有状态' },
                { value: 'enabled', label: '已启用' },
                { value: 'disabled', label: '已禁用' },
            ],
        },
    ];

    const handleFiltersChange = (newFilters: Record<string, any>) => {
        setSearchFilters(newFilters as SearchFilters);
        setPagination(prev => ({...prev, current: 1}));
    };

    const handleSearch = () => {
        setPagination(prev => ({...prev, current: 1}));
    };

    const handleReset = () => {
        setSearchFilters({ strategyName: '', status: 'all' });
        setPagination(prev => ({...prev, current: 1}));
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <UnifiedPageHeader
                title="复核策略管理"
                subtitle="管理质检复核策略，配置触发条件和分配规则"
                icon={Settings}
                badge={{ text: "复核策略管理", color: "blue" }}
                actions={[
                    {
                        label: '新建策略',
                        variant: 'primary',
                        icon: Plus,
                        onClick: handleCreate,
                    },
                ]}
            />
            <main className="p-6 md:p-10">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    {/* 统一查询区域 */}
                    <UnifiedSearchFilter
                        fields={filterFields}
                        filters={searchFilters}
                        onFiltersChange={handleFiltersChange}
                        onSearch={handleSearch}
                        onReset={handleReset}
                        showFilterCount={false}
                    />
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm text-left text-gray-600">
                                <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                    <tr>
                                        <th scope="col" className="px-4 py-3">序号</th>
                                        <th scope="col" className="px-6 py-3">策略名称</th>
                                        <th scope="col" className="px-6 py-3">触发条件</th>
                                        <th scope="col" className="px-6 py-3">分配规则</th>
                                        <th scope="col" className="px-6 py-3">创建人</th>
                                        <th scope="col" className="px-6 py-3">创建时间</th>
                                        <th scope="col" className="px-6 py-3 text-right">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedStrategies.map((strategy, index) => (
                                        <tr 
                                            key={strategy.key}
                                            className="bg-white border-b hover:bg-gray-50"
                                        >
                                            <td className="px-4 py-4 text-gray-700">
                                                {(pagination.current - 1) * pagination.pageSize + index + 1}
                                            </td>
                                            <td className="px-6 py-4 align-top">
                                                <div className="flex items-center">
                                                    <span className="font-semibold text-gray-900">{strategy.name}</span>
                                                    <span className={`ml-3 text-xs font-medium px-2 py-0.5 rounded-full ${
                                                        strategy.status === 'enabled' 
                                                            ? 'bg-green-100 text-green-800' 
                                                            : 'bg-gray-100 text-gray-800'
                                                    }`}>
                                                        {strategy.status === 'enabled' ? '已启用' : '已禁用'}
                                                    </span>
                                                </div>
                                                <p className={`mt-1 text-xs ${
                                                    strategy.status === 'enabled' ? 'text-gray-500' : 'text-gray-400'
                                                }`}>
                                                    {strategy.description}
                                                </p>
                                            </td>
                                            <td className="px-6 py-4">
                                                {strategy.fullConditions ? (
                                                    <Tooltip title={
                                                        <ul>
                                                            {strategy.fullConditions.map((cond: string, index: number) => <li key={index}>{cond}</li>)}
                                                        </ul>
                                                    }>
                                                        <span className="text-sm text-gray-900">{strategy.conditions} 等{strategy.fullConditions.length}个条件</span>
                                                    </Tooltip>
                                                ) : (
                                                    <span className="text-sm text-gray-900">{strategy.conditions}</span>
                                                )}
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className="text-sm text-gray-900">{strategy.allocation}</span>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className="text-sm text-gray-900">{strategy.creator}</span>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className="text-sm text-gray-900">{strategy.createTime}</span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <button
                                                    onClick={() => handleEdit(strategy.key)}
                                                    className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors"
                                                    title="编辑"
                                                >
                                                    <Edit className="w-4 h-4" />
                                                </button>
                                                <button
                                                    onClick={() => handleStatusToggle(strategy.key)}
                                                    className={`p-1.5 rounded-lg transition-colors mx-1 ${
                                                        strategy.status === 'enabled' 
                                                            ? 'text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50' 
                                                            : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                                                    }`}
                                                    title={strategy.status === 'enabled' ? '禁用' : '启用'}
                                                >
                                                    <Eye className="w-4 h-4" />
                                                </button>
                                                <Popconfirm
                                                    title="确定要删除这个复核策略吗？"
                                                    onConfirm={() => handleDelete(strategy.key)}
                                                    okText="确定"
                                                    cancelText="取消"
                                                >
                                                    <button
                                                        className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors"
                                                        title="删除"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </button>
                                                </Popconfirm>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                        {/* 空状态 */}
                        {filteredStrategies.length === 0 && (
                            <div className="text-center py-12">
                                <div className="text-4xl mb-4">📋</div>
                                <div className="text-lg font-medium mb-2 text-gray-900">暂无复核策略</div>
                                <div className="text-sm text-gray-500">点击上方"新建策略"按钮创建第一个复核策略</div>
                            </div>
                        )}
                        
                        {/* 分页 */}
                        {filteredStrategies.length > 0 && (
                            <UnifiedPagination
                                current={pagination.current}
                                pageSize={pagination.pageSize}
                                total={filteredStrategies.length}
                                onChange={(page) => setPagination(prev => ({ ...prev, current: page }))}
                            />
                        )}
                    </motion.div>
                </main>
                
                {drawerOpen && (
                    <FinalCreateReviewStrategyPage
                        onClose={() => setDrawerOpen(false)}
                        onSubmit={handleFormSubmit}
                        strategyId={editingStrategyId}
                    />
                )}
        </div>
    );
};

export default FinalReviewStrategyListPage;
