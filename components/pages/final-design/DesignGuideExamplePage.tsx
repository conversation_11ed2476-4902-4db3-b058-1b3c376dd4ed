import React from 'react';
import { Database, Plus } from 'lucide-react';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';

/**
 * 设计说明示例页面
 * 展示如何使用UnifiedPageHeader组件的详细设计说明功能
 */
const DesignGuideExamplePage: React.FC = () => {
  // 示例设计说明内容
  const designGuideContent = (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-semibold text-gray-900 mb-3">页面概述</h4>
        <p className="text-gray-700 leading-relaxed">
          这是一个数据源管理页面的设计说明示例。该页面主要用于管理系统中的各种数据源连接，
          包括数据库、API接口、文件系统等不同类型的数据源。
        </p>
      </div>
      
      <div>
        <h4 className="text-lg font-semibold text-gray-900 mb-3">功能特性</h4>
        <ul className="list-disc list-inside space-y-2 text-gray-700">
          <li>支持多种数据源类型的统一管理</li>
          <li>提供数据源连接状态的实时监控</li>
          <li>支持数据源的增删改查操作</li>
          <li>提供连接测试功能确保数据源可用性</li>
          <li>支持批量操作提高管理效率</li>
        </ul>
      </div>
      
      <div>
        <h4 className="text-lg font-semibold text-gray-900 mb-3">设计原则</h4>
        <div className="bg-blue-50 p-4 rounded-lg">
          <ul className="list-disc list-inside space-y-1 text-blue-800">
            <li><strong>一致性：</strong>保持与系统其他页面的视觉和交互一致性</li>
            <li><strong>易用性：</strong>简化操作流程，降低用户学习成本</li>
            <li><strong>可靠性：</strong>提供清晰的状态反馈和错误处理</li>
            <li><strong>扩展性：</strong>支持未来新增数据源类型的扩展</li>
          </ul>
        </div>
      </div>
      
      <div>
        <h4 className="text-lg font-semibold text-gray-900 mb-3">技术实现</h4>
        <div className="bg-gray-50 p-4 rounded-lg">
          <p className="text-gray-700 mb-2"><strong>前端技术栈：</strong></p>
          <ul className="list-disc list-inside space-y-1 text-gray-600 ml-4">
            <li>React + TypeScript</li>
            <li>Tailwind CSS 样式框架</li>
            <li>Lucide React 图标库</li>
            <li>统一的组件设计系统</li>
          </ul>
        </div>
      </div>
    </div>
  );

  const handleAddDataSource = () => {
    alert('添加数据源功能');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedPageHeader
        title="数据源管理"
        subtitle="管理和配置系统中的各种数据源连接，确保数据的可靠获取和处理"
        icon={Database}
        iconColor="text-blue-600"
        iconBgColor="bg-blue-100"
        showDesignGuide={true}
        designGuideContent={designGuideContent}
        breadcrumbs={[
          { label: '首页', href: '#' },
          { label: '系统管理', href: '#' },
          { label: '数据源管理' }
        ]}
        badge={{
          text: '3个数据源',
          color: 'blue'
        }}
        actions={[
          {
            label: '添加数据源',
            onClick: handleAddDataSource,
            icon: Plus,
            variant: 'primary'
          }
        ]}
      />
      
      {/* 页面内容 */}
      <div className="px-6 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">设计说明功能演示</h3>
          <p className="text-gray-600 mb-4">
            点击页面头部的"详细设计说明"按钮，可以查看该页面的详细设计文档。
            这个功能可以帮助开发团队和产品团队更好地理解页面的设计思路和实现细节。
          </p>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="text-yellow-800 font-medium mb-2">使用说明</h4>
            <ul className="text-yellow-700 text-sm space-y-1">
              <li>• 设置 <code className="bg-yellow-100 px-1 rounded">showDesignGuide={true}</code> 来显示设计说明按钮</li>
              <li>• 通过 <code className="bg-yellow-100 px-1 rounded">designGuideContent</code> 属性传入自定义内容</li>
              <li>• 如果不提供内容，将显示默认的"建设中"页面</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DesignGuideExamplePage;