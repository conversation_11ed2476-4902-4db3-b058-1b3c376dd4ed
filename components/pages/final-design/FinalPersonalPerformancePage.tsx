import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Info, Eye, BarChart3 } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './common/tooltip';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';
import UnifiedPageHeader from './components/UnifiedPageHeader';

/**
 * 个人成绩记录数据接口
 */
interface PerformanceRecord {
  id: string;
  recordNumber: string;
  taskName: string;
  agentName: string;
  agentId: string;
  teamName: string; // 所属班组
  customerPhone: string;
  callStartTime: string;
  callDuration: string;
  aiScore: number;
  reviewScore?: number;
  appealScore?: number;
  finalScore: number;
  appealStatus: 'eligible' | 'in_progress' | 'processed' | 'expired';
  appealDeadline?: string; // 申诉有效期
  appealTime?: string;
  appealProcessingTime?: string;
  appealResult?: 'approved' | 'rejected';
  qualityResult?: '合格' | '不合格'; // 新增质检结果
}

/**
 * 搜索条件接口
 */
interface SearchFilters {
  recordNumber: string;
  taskName: string;
  agent: string;
  teamName: string;
  appealStatus: string;
  appealResult: string;
  finalScoreMin: string;
  finalScoreMax: string;
  callStartTime: [string, string];
  customerPhone: string;
  qualityResult: '' | '合格' | '不合格';
}

const mockAgents = [
  { name: '张三', id: '10001', team: '客服A组' },
  { name: '李四', id: '10002', team: '客服A组' },
  { name: '王五', id: '10003', team: '客服B组' },
];

const appealStatusOptions = [
    { value: 'eligible', label: '可申诉' },
    { value: 'in_progress', label: '申诉中' },
    { value: 'processed', label: '已处理' },
    { value: 'expired', label: '已过期' },
];
const appealResultOptions = [
    { value: 'approved', label: '申诉成功' },
    { value: 'rejected', label: '申诉失败' },
];

/**
 * 模拟数据
 */
const mockData: PerformanceRecord[] = Array.from({ length: 15 }, (_, index) => {
    const id = `PERF_${(index + 1).toString().padStart(3, '0')}`;
    const agent = mockAgents[index % mockAgents.length];
    const aiScore = Math.floor(Math.random() * 31) + 65; // 65-95
    const hasReview = Math.random() > 0.4;
    const reviewScore = hasReview ? Math.min(100, aiScore + Math.floor(Math.random() * 10) - 4) : undefined;
    
    const appealStatus = appealStatusOptions[index % appealStatusOptions.length].value as PerformanceRecord['appealStatus'];
    let appealScore: number | undefined;
    let finalScore = reviewScore ?? aiScore;
    let appealResult: PerformanceRecord['appealResult'];
    let appealDeadline: string | undefined;

    if (appealStatus === 'processed') {
        appealResult = Math.random() > 0.5 ? 'approved' : 'rejected';
        if (appealResult === 'approved') {
            appealScore = Math.min(100, finalScore + 5);
            finalScore = appealScore;
        } else {
            appealScore = finalScore;
        }
    } else if (appealStatus === 'eligible') {
        const deadline = new Date(new Date().getTime() + 3 * 24 * 60 * 60000); // 3天后过期
        appealDeadline = deadline.toISOString().replace('T', ' ').substring(0, 16);
    }
    
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * 30));
  
    const qualityResult = finalScore >= 80 ? '合格' : '不合格'; // 根据finalScore判断
  
    return {
      id,
        recordNumber: `REC_2024_P${(index + 1).toString().padStart(3, '0')}`,
        taskName: `常规质检任务_${Math.floor(index / 5) + 1}`,
        agentName: agent.name,
        agentId: agent.id,
        teamName: agent.team,
        customerPhone: `1${Math.floor(Math.random() * 3) + 3}${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3'),
        callStartTime: new Date(date.getTime() - (index * 24 * 60 * 60000)).toISOString().replace('T', ' ').substring(0, 19),
        callDuration: `${Math.floor(Math.random() * 15) + 2}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`,
      aiScore,
      reviewScore,
        appealScore,
        finalScore,
        appealStatus,
        appealDeadline,
        appealTime: ['in_progress', 'processed'].includes(appealStatus) ? new Date(date.getTime() + 60 * 60000).toISOString().replace('T', ' ').substring(0, 19) : undefined,
        appealProcessingTime: appealStatus === 'processed' ? new Date(date.getTime() + 25 * 60 * 60000).toISOString().replace('T', ' ').substring(0, 19) : undefined,
        appealResult,
        qualityResult,
    };
});


/**
 * 最终设计 - 个人成绩管理页
 */
export const FinalPersonalPerformancePage: React.FC = () => {
  const navigate = useNavigate();
  const [userRole, setUserRole] = useState<'agent' | 'team_leader' | 'supervisor'>('agent');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(true);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    recordNumber: '',
    taskName: '',
    agent: '',
    teamName: '',
    appealStatus: '',
    appealResult: '',
    finalScoreMin: '',
    finalScoreMax: '',
    callStartTime: ['', ''],
    customerPhone: '',
    qualityResult: '',
  });

  const pageTitle = userRole === 'agent' 
    ? "质检成绩管理" 
    : userRole === 'team_leader' 
    ? "质检成绩管理"
    : "质检成绩管理";
  const pageSubtitle = userRole === 'agent' 
    ? "查看我的历史质检成绩、得分详情和申诉记录" 
    : userRole === 'team_leader'
    ? "查看您所在班组成员的质检成绩、得分详情和申诉记录"
    : "查看所有团队及坐席的质检成绩、得分详情和申诉记录";

  /**
   * 过滤和分页数据
   */
  const filteredAndPaginatedData = useMemo(() => {
    let filtered = mockData.filter(item => {
      // 权限过滤
      if (userRole === 'agent' && item.agentId !== '10001') return false; // 假设当前登录坐席是张三
      if (userRole === 'team_leader' && item.teamName !== '客服A组') return false; // 假设当前登录的班组长属于客服A组
      
      // 文本过滤
      if (searchFilters.recordNumber && !item.recordNumber.includes(searchFilters.recordNumber)) return false;
      if (searchFilters.taskName && !item.taskName.includes(searchFilters.taskName)) return false;
      if (searchFilters.agent && !`${item.agentName}${item.agentId}`.includes(searchFilters.agent)) return false;
      if (searchFilters.teamName && !item.teamName.includes(searchFilters.teamName)) return false;
      if (searchFilters.customerPhone && !item.customerPhone.includes(searchFilters.customerPhone)) return false;
      
      // 下拉框过滤
      if (searchFilters.appealStatus && item.appealStatus !== searchFilters.appealStatus) return false;
      if (searchFilters.appealResult && item.appealResult !== searchFilters.appealResult) return false;
      
      // 数字区间过滤
      if (searchFilters.finalScoreMin && item.finalScore < parseInt(searchFilters.finalScoreMin)) return false;
      if (searchFilters.finalScoreMax && item.finalScore > parseInt(searchFilters.finalScoreMax)) return false;
      
      // 时间区间过滤
      if (searchFilters.callStartTime[0] && new Date(item.callStartTime) < new Date(searchFilters.callStartTime[0])) return false;
      if (searchFilters.callStartTime[1] && new Date(item.callStartTime) > new Date(searchFilters.callStartTime[1])) return false;

      // 质检结果过滤
      if (searchFilters.qualityResult && item.qualityResult !== searchFilters.qualityResult) return false;
      return true;
    });

    const total = filtered.length;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const currentData = filtered.slice(startIndex, endIndex);
    const totalPages = Math.ceil(total / pageSize);

    return { data: currentData, total, totalPages };
  }, [searchFilters, currentPage, pageSize, userRole]);

  /**
   * 重置搜索条件
   */
  const handleReset = () => {
    setSearchFilters({
      recordNumber: '',
      taskName: '',
      agent: '',
      teamName: '',
      appealStatus: '',
      appealResult: '',
      finalScoreMin: '',
      finalScoreMax: '',
      callStartTime: ['', ''],
      customerPhone: '',
      qualityResult: '',
    });
    setCurrentPage(1);
  };

  /**
   * 查看详情
   */
  const handleViewDetails = (recordId: string) => {
    navigate(`/final-design/review-workstation/${recordId}?viewMode=performance_check`);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    Object.entries(searchFilters).forEach(([key, value]) => {
      if (key === 'callStartTime') {
        if (value[0] || value[1]) count++;
      } else if (value) {
        count++;
      }
    });
    return count;
  };
  
  const renderAppealInfo = (item: PerformanceRecord) => {
    const statusMap = {
      eligible: { text: '可申诉', color: 'blue' },
      in_progress: { text: '申诉中', color: 'yellow' },
      processed: { text: '已处理', color: 'gray' },
      expired: { text: '已过期', color: 'red' },
    };
    const { text, color } = statusMap[item.appealStatus];
    
    const resultText = item.appealResult === 'approved' 
        ? <span className="text-green-600 font-medium">成功</span> 
        : <span className="text-red-600 font-medium">失败</span>;

    return (
        <div className="space-y-1">
            <span className={`px-2 py-0.5 text-xs font-medium bg-${color}-100 text-${color}-800 rounded-full`}>{text}</span>
            {item.appealStatus === 'eligible' && item.appealDeadline && (
                <p className="text-xs text-gray-500">有效期至: {item.appealDeadline}</p>
            )}
            {item.appealStatus === 'in_progress' && item.appealTime && (
                <p className="text-xs text-gray-500">申请于: {item.appealTime}</p>
            )}
            {item.appealStatus === 'processed' && (
                <div className="text-xs text-gray-500 space-y-0.5">
                    <div className="flex items-center">
                        <span className="w-14">结果:</span>
                        {resultText}
                    </div>
                    {item.appealProcessingTime && <p><span className="w-14 inline-block">处理于:</span> {item.appealProcessingTime}</p>}
                </div>
            )}
        </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50/50">
      {/* 统一页面头部 */}
      <UnifiedPageHeader
        title={pageTitle}
        subtitle={pageSubtitle}
        icon={BarChart3}
        iconColor="text-green-600"
        iconBgColor="bg-green-100"
        badge={{
          text: userRole === 'agent' ? '坐席视角' : userRole === 'team_leader' ? '班组长视角' : '主管视角',
          color: userRole === 'agent' ? 'blue' : userRole === 'team_leader' ? 'yellow' : 'purple'
        }}
      >
        {/* 角色切换器 */}
        <div className="flex items-center space-x-2 p-1 bg-gray-100 rounded-lg w-fit">
          <button 
            onClick={() => setUserRole('agent')}
            className={`px-3 py-1.5 text-sm rounded-md transition-all ${userRole === 'agent' ? 'bg-white text-gray-800 shadow-sm font-medium' : 'bg-transparent text-gray-600 hover:text-gray-800'}`}
          >
            坐席视角
          </button>
          <button 
            onClick={() => setUserRole('team_leader')}
            className={`px-3 py-1.5 text-sm rounded-md transition-all ${userRole === 'team_leader' ? 'bg-white text-gray-800 shadow-sm font-medium' : 'bg-transparent text-gray-600 hover:text-gray-800'}`}
          >
            班组长视角
          </button>
          <button 
            onClick={() => setUserRole('supervisor')}
            className={`px-3 py-1.5 text-sm rounded-md transition-all ${userRole === 'supervisor' ? 'bg-white text-gray-800 shadow-sm font-medium' : 'bg-transparent text-gray-600 hover:text-gray-800'}`}
          >
            质检主管视角
          </button>
        </div>
      </UnifiedPageHeader>

      {/* Main Content */}
      <main className="p-6 md:p-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        >
          {/* 统一查询区域 */}
          <UnifiedSearchFilter
            fields={[
              {
                key: 'recordNumber',
                label: '记录编号',
                type: 'text',
                placeholder: '请输入记录编号'
              },
              {
                key: 'taskName',
                label: '所属任务',
                type: 'text',
                placeholder: '请输入任务名称'
              },
              ...((['team_leader', 'supervisor'].includes(userRole)) ? [{
                key: 'agent',
                label: '坐席',
                type: 'text' as const,
                placeholder: '请输入坐席姓名/工号'
              }] : []),
              ...(userRole === 'supervisor' ? [{
                key: 'teamName',
                label: '所属班组',
                type: 'text' as const,
                placeholder: '请输入班组名称'
              }] : []),
              {
                key: 'customerPhone',
                label: '客户号码',
                type: 'text',
                placeholder: '请输入客户号码'
              },
              {
                key: 'appealStatus',
                label: '申诉状态',
                type: 'select',
                placeholder: '所有申诉状态',
                options: appealStatusOptions
              },
              {
                key: 'appealResult',
                label: '申诉结果',
                type: 'select',
                placeholder: '所有申诉结果',
                options: appealResultOptions
              },
              {
                key: 'finalScore',
                label: '最终得分',
                type: 'numberRange',
                placeholder: '最低分-最高分'
              },
              {
                key: 'callStartTime',
                label: '通话开始时间',
                type: 'datetimeRange',
                placeholder: '开始时间-结束时间'
              },
              {
                key: 'qualityResult',
                label: '质检结果',
                type: 'select',
                placeholder: '所有质检结果',
                options: [
                  { label: '合格', value: '合格' },
                  { label: '不合格', value: '不合格' }
                ]
              }
            ]}
            filters={searchFilters}
            onFiltersChange={(newFilters: Record<string, any>) => setSearchFilters(newFilters as SearchFilters)}
            onSearch={() => setCurrentPage(1)}
            onReset={handleReset}
          />

          {/* 表格区域 */}
          <div className="mt-6">
            <div className="overflow-x-auto">
              <TooltipProvider>
                <table className="w-full text-sm text-left text-gray-600">
                  <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                    <tr>
                      {['序号', '记录编号', '所属任务', 
                        ...(userRole === 'team_leader' || userRole === 'supervisor' ? ['坐席'] : []), 
                        ...(userRole === 'supervisor' ? ['所属班组'] : []), 
                        '客户号码', '通话开始时间', '通话时长', '最终得分', '质检结果', '申诉信息', '操作'].map(header => (
                          <th key={header} scope="col" className="px-4 py-3">{header}</th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {filteredAndPaginatedData.data.map((item, index) => (
                      <tr key={item.id} className="bg-white border-b hover:bg-gray-50">
                        <td className="px-4 py-4 text-gray-700">{(currentPage - 1) * pageSize + index + 1}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-blue-600 font-medium">{item.recordNumber}</td>
                        <td className="px-4 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.taskName}>{item.taskName}</td>
                        {(userRole === 'team_leader' || userRole === 'supervisor') && (
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{`${item.agentName} (${item.agentId})`}</td>
                        )}
                        {userRole === 'supervisor' && (
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.teamName}</td>
                        )}
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.customerPhone}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.callStartTime}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{item.callDuration}</td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm">
                          <div className="flex items-center space-x-2">
                            <span className="font-bold text-blue-700">{item.finalScore}</span>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info className="w-4 h-4 text-gray-400 hover:text-blue-500 cursor-pointer"/>
                              </TooltipTrigger>
                              <TooltipContent className="bg-white text-gray-900 rounded-lg shadow-lg border border-gray-200">
                                <div className="p-3" style={{ minWidth: '200px' }}>
                                  <h4 className="font-semibold text-md mb-2 border-b border-gray-200 pb-2">分数构成</h4>
                                  <div className="text-sm space-y-2 mt-2">
                                    <div className="flex justify-between items-baseline">
                                      <span className="text-gray-600">AI初检得分</span>
                                      <span className="font-bold text-lg">{item.aiScore}</span>
                                    </div>
                                    <div className="flex justify-between items-baseline">
                                      <span className="text-gray-600">人工复核得分</span>
                                      {item.reviewScore !== undefined ? (
                                        <span className="font-bold text-lg">{item.reviewScore}</span>
                                      ) : (
                                        <span className="text-gray-400 text-sm">N/A</span>
                                      )}
                                    </div>
                                    <div className="flex justify-between items-baseline">
                                      <span className="text-gray-600">申诉得分</span>
                                      <span className="font-semibold">{item.appealScore ?? 'N/A'}</span>
                                    </div>
                                  </div>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            item.qualityResult === '合格' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {item.qualityResult}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm">
                          {renderAppealInfo(item)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button 
                            onClick={() => handleViewDetails(item.id)} 
                            className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors"
                            title="查看详情"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </TooltipProvider>
            </div>
            {filteredAndPaginatedData.data.length === 0 && (
              <div className="text-center py-10 text-gray-500">
                <p>未找到匹配的记录。</p>
              </div>
            )}

            {/* 分页 */}
           {filteredAndPaginatedData.data.length > 0 && (
             <UnifiedPagination
               current={1}
               total={filteredAndPaginatedData.data.length}
               pageSize={10}
               onChange={() => {}}
             />
           )}
          </div>
        </motion.div>
      </main>
    </div>
  );
};

export default FinalPersonalPerformancePage;