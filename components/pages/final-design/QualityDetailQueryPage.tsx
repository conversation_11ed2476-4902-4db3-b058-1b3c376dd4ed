import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Download, Eye, AlertTriangle, Search, Info } from 'lucide-react';
import UnifiedPagination from './components/UnifiedPagination';
import { UnifiedSearchFilter } from './components/UnifiedSearchFilter';
import UnifiedPageHeader from './components/UnifiedPageHeader';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './common/tooltip';

/**
 * 质检明细数据接口
 */
interface QualityDetail {
  id: string;
  recordNumber: string; // 记录编号
  taskName: string; // 所属任务
  agentName: string; // 坐席姓名
  agentId: string; // 坐席工号
  teamName: string; // 所属班组
  customerNumber: string; // 客户号码
  callStartTime: string; // 通话开始时间
  callDuration: string; // 通话时长
  finalScore: number; // 最终得分
  qualityResult: '合格' | '不合格'; // 质检结果
  appealStatus: 'none' | 'available' | 'pending' | 'processed' | 'expired'; // 申诉状态
  appealResult?: '成功' | '失败'; // 申诉结果
  appealTime?: string; // 申诉时间
  processTime?: string; // 处理时间
  appealDeadline?: string; // 申诉有效期
  scoreEvolution: {
    ai: number;
    review?: number;
    appeal?: number;
    final: number;
    source: 'AI' | '复核' | '申诉';
  };
}

/**
 * 搜索条件接口
 */
interface SearchFilters {
  recordNumber: string;
  taskName: string;
  agentName: string;
  teamName: string;
  appealStatus: string;
  appealResult: string;
  finalScoreMin: string;
  finalScoreMax: string;
  qualityResult: string;
  callStartTime: [string, string];
  customerNumber: string;
}

/**
 * 用户角色类型
 */
type UserRole = 'agent' | 'teamLeader' | 'qaManager';

/**
 * 选项数据
 */
const agentOptions = [
  { name: '张三', id: '10001', team: 'A组' },
  { name: '李四', id: '10002', team: 'A组' },
  { name: '王五', id: '10003', team: 'B组' },
  { name: '赵六', id: '10004', team: 'B组' },
  { name: '钱七', id: '10005', team: 'C组' },
  { name: '孙八', id: '10006', team: 'C组' },
];

const teamOptions = ['A组', 'B组', 'C组'];
const taskOptions = ['客服质检任务_1', '客服质检任务_2', '营销质检任务_1'];

/**
 * 模拟数据生成
 */
const generateMockData = (userRole: UserRole, currentUser?: { name: string; team: string }): QualityDetail[] => {
  return Array.from({ length: 25 }, (_, index) => {
    const id = (index + 1).toString().padStart(3, '0');
    const agent = agentOptions[Math.floor(Math.random() * agentOptions.length)];
    
    // 根据角色过滤数据
    let filteredAgent = agent;
    if (userRole === 'agent' && currentUser) {
      filteredAgent = { name: currentUser.name, id: '10001', team: currentUser.team };
    } else if (userRole === 'teamLeader' && currentUser) {
      const teamAgents = agentOptions.filter(a => a.team === currentUser.team);
      filteredAgent = teamAgents[Math.floor(Math.random() * teamAgents.length)];
    }
    
    const aiScore = Math.floor(Math.random() * 41) + 60;
    const hasReview = Math.random() > 0.7;
    const reviewScore = hasReview ? Math.min(100, aiScore + Math.floor(Math.random() * 10) - 3) : undefined;
    const hasAppeal = Math.random() > 0.8;
    const appealScore = hasAppeal ? Math.min(100, (reviewScore || aiScore) + Math.floor(Math.random() * 8) - 2) : undefined;
    
    const finalScore = appealScore || reviewScore || aiScore;
    const qualityResult = finalScore >= 80 ? '合格' : '不合格';
    
    // 申诉状态逻辑
    let appealStatus: QualityDetail['appealStatus'] = 'none';
    let appealResult: QualityDetail['appealResult'] | undefined;
    let appealTime: string | undefined;
    let processTime: string | undefined;
    
    if (qualityResult === '不合格' && Math.random() > 0.6) {
      const statusRandom = Math.random();
      if (statusRandom > 0.8) {
        appealStatus = 'available';
      } else if (statusRandom > 0.6) {
        appealStatus = 'pending';
        appealTime = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19);
      } else if (statusRandom > 0.3) {
        appealStatus = 'processed';
        appealResult = Math.random() > 0.5 ? '成功' : '失败';
        appealTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19);
        processTime = new Date(new Date(appealTime).getTime() + Math.random() * 48 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19);
      } else {
        appealStatus = 'expired';
      }
    }
    
    const callStartTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19);
    const durationMinutes = Math.floor(Math.random() * 17) + 3;
    const durationSeconds = Math.floor(Math.random() * 60);
    const callDuration = `${durationMinutes.toString().padStart(2, '0')}:${durationSeconds.toString().padStart(2, '0')}`;
    
    return {
      id: `QD_${id}`,
      recordNumber: `QM250702${id}`,
      taskName: taskOptions[Math.floor(Math.random() * taskOptions.length)],
      agentName: filteredAgent.name,
      agentId: filteredAgent.id,
      teamName: filteredAgent.team,
      customerNumber: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      callStartTime,
      callDuration,
      finalScore,
      qualityResult,
      appealStatus,
      appealResult,
      appealTime,
      processTime,
      appealDeadline: appealStatus === 'available' ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19) : undefined,
      scoreEvolution: {
        ai: aiScore,
        review: reviewScore,
        appeal: appealScore,
        final: finalScore,
        source: appealScore ? '申诉' : (reviewScore ? '复核' : 'AI')
      }
    };
  });
};

/**
 * 质检明细查询页面
 */
export const QualityDetailQueryPage: React.FC = () => {
  const navigate = useNavigate();
  
  // 模拟当前用户角色和信息
  const [currentUserRole] = useState<UserRole>('qaManager'); // 可以是 'agent', 'teamLeader', 'qaManager'
  const [currentUser] = useState({ name: '张三', team: 'A组' });
  
  /**
   * 导出质检明细数据
   */
  const handleExportData = () => {
    const exportData = {
      exportInfo: {
        title: '质检明细数据导出',
        exportTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
        userRole: currentUserRole,
        filters: searchFilters,
        totalRecords: filteredAndPaginatedData.total
      },
      data: filteredAndPaginatedData.allData.map(detail => ({
        记录编号: detail.recordNumber,
        所属任务: detail.taskName,
        ...(currentUserRole !== 'agent' && { 坐席: `${detail.agentName}/${detail.agentId}` }),
        ...(currentUserRole === 'qaManager' && { 所属班组: detail.teamName }),
        客户号码: detail.customerNumber,
        通话开始时间: detail.callStartTime,
        通话时长: detail.callDuration,
        最终得分: detail.finalScore,
        质检结果: detail.qualityResult,
        申诉状态: {
          'none': '无申诉',
          'available': '可申诉',
          'pending': '申诉中',
          'processed': '已处理',
          'expired': '已过期'
        }[detail.appealStatus],
        ...(detail.appealResult && { 申诉结果: detail.appealResult }),
        ...(detail.appealTime && { 申诉时间: detail.appealTime }),
        ...(detail.processTime && { 处理时间: detail.processTime }),
        得分演进: `AI:${detail.scoreEvolution.ai}${detail.scoreEvolution.review ? ` → 复核:${detail.scoreEvolution.review}` : ''}${detail.scoreEvolution.appeal ? ` → 申诉:${detail.scoreEvolution.appeal}` : ''} → 最终:${detail.scoreEvolution.final}`
      }))
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `质检明细数据_${new Date().toISOString().substring(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    recordNumber: '',
    taskName: '',
    agentName: '',
    teamName: '',
    appealStatus: '',
    appealResult: '',
    finalScoreMin: '',
    finalScoreMax: '',
    qualityResult: '',
    callStartTime: ['', ''],
    customerNumber: '',
  });

  // 生成模拟数据
  const mockData = useMemo(() => generateMockData(currentUserRole, currentUser), [currentUserRole, currentUser]);

  /**
   * 过滤和分页数据
   */
  const filteredAndPaginatedData = useMemo(() => {
    let filtered = mockData.filter(detail => {
      // 记录编号过滤
      if (searchFilters.recordNumber && !detail.recordNumber.includes(searchFilters.recordNumber)) return false;
      
      // 任务名称过滤
      if (searchFilters.taskName && !detail.taskName.includes(searchFilters.taskName)) return false;
      
      // 坐席过滤
      if (searchFilters.agentName && !`${detail.agentName} ${detail.agentId}`.includes(searchFilters.agentName)) return false;
      
      // 班组过滤
      if (searchFilters.teamName && !detail.teamName.includes(searchFilters.teamName)) return false;
      
      // 申诉状态过滤
      if (searchFilters.appealStatus && detail.appealStatus !== searchFilters.appealStatus) return false;
      
      // 申诉结果过滤
      if (searchFilters.appealResult && detail.appealResult !== searchFilters.appealResult) return false;
      
      // 最终得分过滤
      if (searchFilters.finalScoreMin && detail.finalScore < parseInt(searchFilters.finalScoreMin)) return false;
      if (searchFilters.finalScoreMax && detail.finalScore > parseInt(searchFilters.finalScoreMax)) return false;
      
      // 质检结果过滤
      if (searchFilters.qualityResult && detail.qualityResult !== searchFilters.qualityResult) return false;
      
      // 通话开始时间过滤
      if (searchFilters.callStartTime[0] && new Date(detail.callStartTime) < new Date(searchFilters.callStartTime[0])) return false;
      if (searchFilters.callStartTime[1] && new Date(detail.callStartTime) > new Date(searchFilters.callStartTime[1])) return false;
      
      // 客户号码过滤
      if (searchFilters.customerNumber && !detail.customerNumber.includes(searchFilters.customerNumber)) return false;
      
      return true;
    });

    const total = filtered.length;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const currentData = filtered.slice(startIndex, endIndex);
    const totalPages = Math.ceil(total / pageSize);

    return { data: currentData, total, totalPages, allData: filtered };
  }, [mockData, searchFilters, currentPage, pageSize]);

  /**
   * 重置搜索条件
   */
  const handleReset = () => {
    setSearchFilters({
      recordNumber: '',
      taskName: '',
      agentName: '',
      teamName: '',
      appealStatus: '',
      appealResult: '',
      finalScoreMin: '',
      finalScoreMax: '',
      qualityResult: '',
      callStartTime: ['', ''],
      customerNumber: '',
    });
    setCurrentPage(1);
  };

  /**
   * 查看详情
   */
  const handleViewDetail = (detailId: string) => {
    navigate(`/final-design/multi-mode-session-detail/${detailId}?viewMode=basic_view`);
  };

  /**
   * 发起申诉
   */
  const handleAppeal = (detailId: string) => {
    navigate(`/final-design/quality-appeal/${detailId}`);
  };

  /**
   * 获取申诉状态显示
   */
  const getAppealStatusDisplay = (detail: QualityDetail) => {
    switch (detail.appealStatus) {
      case 'available':
        return (
          <div className="space-y-1">
            <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
              可申诉
            </span>
            <div className="text-xs text-gray-500">
              有效期至: {detail.appealDeadline?.substring(0, 16)}
            </div>
          </div>
        );
      case 'pending':
        return (
          <div className="space-y-1">
            <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
              申诉中
            </span>
            <div className="text-xs text-gray-500">
              申诉时间: {detail.appealTime?.substring(0, 16)}
            </div>
          </div>
        );
      case 'processed':
        return (
          <div className="space-y-1">
            <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
              detail.appealResult === '成功' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              已处理
            </span>
            <div className="text-xs text-gray-500">
              结果: {detail.appealResult} | {detail.processTime?.substring(0, 16)}
            </div>
          </div>
        );
      case 'expired':
        return (
          <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
            已过期
          </span>
        );
      default:
        return (
          <span className="text-xs text-gray-400">-</span>
        );
    }
  };

  /**
   * 获取分数演进显示
   */
  const getScoreEvolutionDisplay = (detail: QualityDetail) => {
    const { scoreEvolution } = detail;
    
    return (
      <div className="flex items-center space-x-2">
        <span className={`font-bold ${
          detail.finalScore >= 80 ? 'text-green-600' :
          detail.finalScore >= 60 ? 'text-yellow-600' : 'text-red-600'
        }`}>
          {detail.finalScore}
        </span>
        <Tooltip>
          <TooltipTrigger asChild>
            <Info className="w-4 h-4 text-gray-400 hover:text-blue-500 cursor-pointer"/>
          </TooltipTrigger>
          <TooltipContent className="bg-white text-gray-900 rounded-lg shadow-lg border border-gray-200">
            <div className="p-3" style={{ minWidth: '200px' }}>
              <h4 className="font-semibold text-md mb-2 border-b border-gray-200 pb-2">分数构成</h4>
              <div className="text-sm space-y-2 mt-2">
                <div className="flex justify-between items-baseline">
                  <span className="text-gray-600">AI初检得分</span>
                  <span className="font-bold text-lg">{scoreEvolution.ai}</span>
                </div>
                <div className="flex justify-between items-baseline">
                  <span className="text-gray-600">人工复核得分</span>
                  {scoreEvolution.review !== undefined ? (
                    <span className="font-bold text-lg">{scoreEvolution.review}</span>
                  ) : (
                    <span className="text-gray-400 text-sm">N/A</span>
                  )}
                </div>
                <div className="flex justify-between items-baseline">
                  <span className="text-gray-600">申诉得分</span>
                  {scoreEvolution.appeal !== undefined ? (
                    <span className="font-bold text-lg">{scoreEvolution.appeal}</span>
                  ) : (
                    <span className="text-gray-400 text-sm">N/A</span>
                  )}
                </div>
                <div className="flex justify-between items-baseline">
                  <span className="text-gray-600">最终分数来源</span>
                  <span className="font-bold text-sm">{scoreEvolution.source}</span>
                </div>
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </div>
    );
  };

  /**
   * 获取当前已选筛选条件数量
   */
  const getActiveFiltersCount = () => {
    let count = 0;
    Object.entries(searchFilters).forEach(([key, value]) => {
      if (key === 'callStartTime') {
        if (value[0] || value[1]) count++;
      } else if (value) {
        count++;
      }
    });
    return count;
  };

  /**
   * 获取页面标题
   */
  const getPageTitle = () => {
    switch (currentUserRole) {
      case 'agent':
        return '我的质检成绩';
      case 'teamLeader':
        return '团队质检成绩';
      case 'qaManager':
        return '质检明细查询';
      default:
        return '质检明细查询';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50/50">
      <UnifiedPageHeader
        title={getPageTitle()}
        subtitle="查看和管理质检成绩明细信息"
        icon={Search}
        badge={{ text: "明细查询", color: "blue" }}
        actions={[
          {
            label: '导出数据',
            onClick: handleExportData,
            icon: Download,
            variant: 'outline'
          }
        ]}
      />
      <main className="p-6 md:p-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 space-y-6"
        >
          {/* 统一查询区域 */}
          <UnifiedSearchFilter
        fields={[
          {
            key: 'recordNumber',
            label: '记录编号',
            type: 'text',
            placeholder: '请输入记录编号'
          },
          {
            key: 'taskName',
            label: '所属任务',
            type: 'select',
            placeholder: '请选择任务',
            options: taskOptions.map(task => ({ label: task, value: task }))
          },
          ...(currentUserRole !== 'agent' ? [{
            key: 'agentName',
            label: currentUserRole === 'teamLeader' ? '团队坐席' : '坐席',
            type: 'text' as const,
            placeholder: '请输入坐席姓名或工号'
          }] : []),
          ...(currentUserRole === 'qaManager' ? [{
            key: 'teamName',
            label: '所属班组',
            type: 'text' as const,
            placeholder: '请输入班组名称'
          }] : []),
          {
            key: 'appealStatus',
            label: '申诉状态',
            type: 'select',
            placeholder: '请选择申诉状态',
            options: [
              { label: '无申诉', value: 'none' },
              { label: '可申诉', value: 'available' },
              { label: '申诉中', value: 'pending' },
              { label: '已处理', value: 'processed' },
              { label: '已过期', value: 'expired' }
            ]
          },
          {
            key: 'appealResult',
            label: '申诉结果',
            type: 'select',
            placeholder: '全部结果',
            options: [
              { label: '成功', value: '成功' },
              { label: '失败', value: '失败' }
            ]
          },
          {
            key: 'finalScore',
            label: '最终得分',
            type: 'numberRange',
            placeholder: '最低分-最高分'
          },
          {
            key: 'qualityResult',
            label: '质检结果',
            type: 'select',
            placeholder: '全部结果',
            options: [
              { label: '合格', value: '合格' },
              { label: '不合格', value: '不合格' }
            ]
          },
          {
            key: 'callStartTime',
            label: '通话开始时间',
            type: 'datetimeRange',
            placeholder: '开始时间-结束时间'
          },
          {
            key: 'customerNumber',
            label: '客户号码',
            type: 'text',
            placeholder: '请输入客户号码'
          }
        ]}
        filters={searchFilters}
        onFiltersChange={(newFilters: Record<string, any>) => setSearchFilters(newFilters as SearchFilters)}
        onSearch={() => setCurrentPage(1)}
        onReset={handleReset}
          />

          {/* 表格区域 */}
          <div className="overflow-x-auto">
            <TooltipProvider>
            <table className="w-full text-sm text-left text-gray-600">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">记录编号</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属任务</th>
                {currentUserRole !== 'agent' && (
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">坐席</th>
                )}
                {currentUserRole === 'qaManager' && (
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属班组</th>
                )}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户号码</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通话开始时间</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通话时长</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最终得分</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">质检结果</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申诉信息</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAndPaginatedData.data.map((detail, index) => (
                <tr key={detail.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {(currentPage - 1) * pageSize + index + 1}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-medium">
                    {detail.recordNumber}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {detail.taskName}
                  </td>
                  {currentUserRole !== 'agent' && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {`${detail.agentName}/${detail.agentId}`}
                    </td>
                  )}
                  {currentUserRole === 'qaManager' && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {detail.teamName}
                    </td>
                  )}
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {detail.customerNumber}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {detail.callStartTime}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {detail.callDuration}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {getScoreEvolutionDisplay(detail)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      detail.qualityResult === '合格' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {detail.qualityResult}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {getAppealStatusDisplay(detail)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                    <button
                      onClick={() => handleViewDetail(detail.id)}
                      className="inline-flex items-center p-1.5 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-colors"
                      title="查看详情"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    {currentUserRole === 'agent' && detail.appealStatus === 'available' && (
                      <button
                        onClick={() => handleAppeal(detail.id)}
                        className="inline-flex items-center p-1.5 text-sm font-medium text-orange-600 rounded-lg hover:bg-orange-50 hover:text-orange-900 transition-colors"
                        title="发起申诉"
                      >
                        <AlertTriangle className="w-4 h-4" />
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
            </table>
            </TooltipProvider>
          </div>
          
          {/* 分页 */}
          {filteredAndPaginatedData.total > pageSize && (
            <UnifiedPagination
              current={currentPage}
              pageSize={pageSize}
              total={filteredAndPaginatedData.total}
              onChange={setCurrentPage}
            />
          )}
        </motion.div>
      </main>
    </div>
  );
};

export default QualityDetailQueryPage;