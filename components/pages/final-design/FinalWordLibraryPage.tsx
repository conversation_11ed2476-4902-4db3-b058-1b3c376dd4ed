import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Plus, Download, Upload, Edit, Trash2, FileText, User, Calendar, Filter, Settings, PowerOff, Power, X } from 'lucide-react';
import { UnifiedPageHeader } from './components/UnifiedPageHeader';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';

// 词库类型接口
interface WordLibrary {
  id: string;
  name: string;
  description: string;
  wordCount: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'inactive';
}

// 词条接口
interface WordEntry {
  id: string;
  word: string;
  createdAt: string;
  updatedAt: string;
}

// 初始词库数据
const initialWordLibraries: WordLibrary[] = [
  {
    id: '1',
    name: '服务态度正面词库',
    description: '包含表示良好服务态度的词汇和短语',
    wordCount: 156,
    createdBy: '张三',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
    status: 'active',
  },
  {
    id: '2',
    name: '投诉关键词库',
    description: '客户投诉和不满情绪相关词汇',
    wordCount: 89,
    createdBy: '李四',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18',
    status: 'active',
  },
  {
    id: '3',
    name: '业务专业术语',
    description: '行业专业术语和标准用语',
    wordCount: 234,
    createdBy: '王五',
    createdAt: '2024-01-08',
    updatedAt: '2024-01-16',
    status: 'active',
  },
  {
    id: '4',
    name: '敏感词汇库',
    description: '需要特别关注的敏感词汇',
    wordCount: 67,
    createdBy: '赵六',
    createdAt: '2024-01-05',
    updatedAt: '2024-01-12',
    status: 'inactive',
  }
];

// 搜索筛选接口
interface SearchFilters {
  searchTerm: string;
  status: string;
}

// 状态选项
const statusOptions = [
  { value: 'all', label: '全部状态' },
  { value: 'active', label: '启用' },
  { value: 'inactive', label: '禁用' }
];

const getInitialWordEntries = (libraryId: string): WordEntry[] => {
  const baseEntries: Record<string, Omit<WordEntry, 'id' | 'createdAt' | 'updatedAt'>[]> = {
    '1': [ // 服务态度正面词库
      { word: '优质服务' },
      { word: '专业' },
      { word: '耐心' },
      { word: '热情' },
      { word: '及时' },
      { word: '满意' },
      { word: '贴心' },
      { word: '高效' }
    ],
    '2': [ // 投诉关键词库
      { word: '投诉' },
      { word: '不满' },
      { word: '差评' },
      { word: '退款' },
      { word: '态度恶劣' }
    ],
    '3': [ // 业务专业术语
      { word: '保险条款' },
      { word: '理赔' },
      { word: '保费' },
      { word: '承保' }
    ],
    '4': [ // 敏感词汇库
      { word: '欺诈' },
      { word: '违法' },
      { word: '隐瞒' }
    ]
  };
  
  const selectedEntries = baseEntries[libraryId as keyof typeof baseEntries] || [];

  return selectedEntries.map((entry, index) => ({
    ...entry,
    id: `${libraryId}-${index}`,
    createdAt: new Date(Date.now() - (selectedEntries.length - index) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    updatedAt: new Date(Date.now() - (selectedEntries.length - index) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  }));
};

/**
 * 质检词库管理页面
 */
const FinalWordLibraryPage: React.FC = () => {
  const navigate = useNavigate();
  const [wordLibraries, setWordLibraries] = useState<WordLibrary[]>(initialWordLibraries);
  const [filteredLibraries, setFilteredLibraries] = useState<WordLibrary[]>(initialWordLibraries);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    searchTerm: '',
    status: 'all'
  });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showWordsModal, setShowWordsModal] = useState(false);
  const [selectedLibrary, setSelectedLibrary] = useState<WordLibrary | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // States for entries modal
  const [modalEntries, setModalEntries] = useState<WordEntry[]>([]);
  const [filteredModalEntries, setFilteredModalEntries] = useState<WordEntry[]>([]);
  const [entrySearchTerm, setEntrySearchTerm] = useState('');
  const [showCreateEntryModal, setShowCreateEntryModal] = useState(false);
  const [showEditEntryModal, setShowEditEntryModal] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState<WordEntry | null>(null);
  const [entryCurrentPage, setEntryCurrentPage] = useState(1);
  const [entryPageSize] = useState(10);

  const [entryFormData, setEntryFormData] = useState({
    word: '',
  });

  // 新建/编辑词库表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });

  // 筛选字段配置
  const filterFields: FilterField[] = [
    { key: 'searchTerm', label: '词库名称/描述', type: 'text', placeholder: '搜索词库名称或描述...' },
    { key: 'status', label: '状态', type: 'select', options: [
      { value: 'all', label: '全部状态' },
      { value: 'active', label: '启用' },
      { value: 'inactive', label: '禁用' }
    ]}
  ];

  // 筛选和搜索逻辑
  useEffect(() => {
    let filtered = wordLibraries;

    // 搜索筛选
    if (searchFilters.searchTerm) {
      filtered = filtered.filter(library => 
        library.name.toLowerCase().includes(searchFilters.searchTerm.toLowerCase()) ||
        library.description.toLowerCase().includes(searchFilters.searchTerm.toLowerCase())
      );
    }

    // 状态筛选
    if (searchFilters.status !== 'all') {
      filtered = filtered.filter(library => library.status === searchFilters.status);
    }

    setFilteredLibraries(filtered);
    setCurrentPage(1);
  }, [searchFilters, wordLibraries]);

  // 处理筛选条件变化
  const handleFiltersChange = (newFilters: Record<string, any>) => {
    setSearchFilters(newFilters as SearchFilters);
  };

  // 处理查询
  const handleSearch = () => {
    setCurrentPage(1);
  };

  // 处理重置
  const handleReset = () => {
    setSearchFilters({
      searchTerm: '',
      status: 'all'
    });
  };

  // 词条筛选和搜索逻辑
  useEffect(() => {
    let filtered = modalEntries;

    if (entrySearchTerm) {
      filtered = filtered.filter(entry => 
        entry.word.toLowerCase().includes(entrySearchTerm.toLowerCase())
      );
    }

    setFilteredModalEntries(filtered);
    setEntryCurrentPage(1);
  }, [entrySearchTerm, modalEntries]);

  // 分页数据
  const totalPages = Math.ceil(filteredLibraries.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedLibraries = filteredLibraries.slice(startIndex, startIndex + pageSize);

  // 词条分页数据
  const entryTotalPages = Math.ceil(filteredModalEntries.length / entryPageSize);
  const entryStartIndex = (entryCurrentPage - 1) * entryPageSize;
  const paginatedModalEntries = filteredModalEntries.slice(entryStartIndex, entryStartIndex + entryPageSize);

  // 处理状态切换
  const handleStatusToggle = (id: string) => {
    setWordLibraries(prev => prev.map(library => 
      library.id === id 
        ? { ...library, status: library.status === 'active' ? 'inactive' : 'active' }
        : library
    ));
  };

  // 处理删除词库
  const handleDeleteLibrary = (id: string) => {
    if (window.confirm('确定要删除这个词库吗？此操作不可恢复。')) {
      setWordLibraries(prev => prev.filter(library => library.id !== id));
    }
  };

  // 处理新建词库
  const handleCreateLibrary = () => {
    const newLibrary: WordLibrary = {
      id: Date.now().toString(),
      name: formData.name,
      description: formData.description,
      wordCount: 0,
      createdBy: '当前用户',
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
      status: 'active',
    };

    setWordLibraries(prev => [newLibrary, ...prev]);
    setShowCreateModal(false);
    resetForm();
  };

  // 处理编辑词库
  const handleEditLibrary = () => {
    if (!selectedLibrary) return;

    setWordLibraries(prev => prev.map(library => 
      library.id === selectedLibrary.id
        ? {
            ...library,
            name: formData.name,
            description: formData.description,
            updatedAt: new Date().toISOString().split('T')[0]
          }
        : library
    ));
    setShowEditModal(false);
    resetForm();
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
    });
    setSelectedLibrary(null);
  };

  // 打开编辑模态框
  const openEditModal = (library: WordLibrary) => {
    setSelectedLibrary(library);
    setFormData({
      name: library.name,
      description: library.description,
    });
    setShowEditModal(true);
  };

  // 导出词库
  const handleExportLibrary = (library: WordLibrary) => {
    const exportData = {
      libraryInfo: library,
      words: getInitialWordEntries(library.id).map(({word}) => ({word}))
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${library.name}_词库.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 导入词库
  const handleImportLibrary = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.csv,.txt';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        // 这里处理文件导入逻辑
        console.log('导入文件:', file.name);
        alert('词库导入功能开发中...');
      }
    };
    input.click();
  };

  const openWordsModal = (library: WordLibrary) => {
    if (library) {
        setSelectedLibrary(library);
        const entries = getInitialWordEntries(library.id);
        setModalEntries(entries);
        setShowWordsModal(true);
    }
  };

  // ----- 词条管理Handlers -----

  const handleCreateEntry = () => {
    const newEntry: WordEntry = {
      id: Date.now().toString(),
      word: entryFormData.word,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    };

    setModalEntries(prev => [newEntry, ...prev]);
    setShowCreateEntryModal(false);
    resetEntryForm();
  };

  const handleEditEntry = () => {
    if (!selectedEntry) return;

    setModalEntries(prev => prev.map(entry => 
      entry.id === selectedEntry.id
        ? {
            ...entry,
            word: entryFormData.word,
            updatedAt: new Date().toISOString().split('T')[0]
          }
        : entry
    ));
    setShowEditEntryModal(false);
    resetEntryForm();
  };

  const handleDeleteEntry = (id: string) => {
    if (window.confirm('确定要删除这个词条吗？此操作不可恢复。')) {
      setModalEntries(prev => prev.filter(entry => entry.id !== id));
    }
  };

  const resetEntryForm = () => {
    setEntryFormData({
      word: '',
    });
    setSelectedEntry(null);
  };

  const openEditEntryModal = (entry: WordEntry) => {
    setSelectedEntry(entry);
    setEntryFormData({
      word: entry.word,
    });
    setShowEditEntryModal(true);
  };

  const handleExportEntries = () => {
    if (!selectedLibrary) return;
    const exportData = {
      libraryInfo: selectedLibrary,
      entries: modalEntries,
      exportTime: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${selectedLibrary?.name || '词库'}_词条.json`;
    document.body.appendChild(a);
a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImportEntries = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.csv,.txt';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        console.log('导入文件:', file.name);
        alert('词条导入功能开发中...');
      }
    };
    input.click();
  };

  const handleDownloadTemplate = () => {
    const template = {
      instructions: '请按照以下格式填写词条信息',
      format: {
        word: '词条内容',
      },
      examples: [
        { word: '优质服务' },
        { word: '专业' }
      ]
    };
    
    const blob = new Blob([JSON.stringify(template, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '词条导入模板.json';
    document.body.appendChild(a);
a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedPageHeader
        title="质检词库管理"
        subtitle="管理质检词库，支持词库的创建、编辑、导入导出等操作"
        icon={FileText}
        badge={{ text: `共 ${wordLibraries.length} 个词库`, color: "blue" }}
        actions={[
          {
            label: "导入词库",
            icon: Upload,
            variant: "outline",
            onClick: handleImportEntries
          },
          {
            label: "新建词库",
            icon: Plus,
            variant: "primary",
            onClick: () => setShowCreateModal(true)
          }
        ]}
      />
      
      <main className="p-6 md:p-10">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
          {/* 统一查询区域 */}
          <UnifiedSearchFilter
            fields={filterFields}
            filters={searchFilters}
            onFiltersChange={handleFiltersChange}
            onSearch={handleSearch}
            onReset={handleReset}
          />

          {/* 词库列表 */}
           <div className="overflow-x-auto mt-6">
             <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    序号
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    词库信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    词条数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建人
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedLibraries.map((library, index) => (
                  <tr key={library.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {startIndex + index + 1}
                    </td>
                    <td className="px-6 py-4">
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">{library.name}</span>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                              library.status === 'active'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                            {library.status === 'active' ? '已启用' : '已禁用'}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500 mt-1">{library.description}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{library.wordCount} 个词条</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">
                        {library.createdBy}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500">
                        {library.createdAt}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => openEditModal(library)}
                          className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors"
                          title="编辑词库"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleStatusToggle(library.id)}
                          className={`p-1.5 rounded-lg transition-colors mx-1 ${
                            library.status === 'active' 
                              ? 'text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50' 
                              : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                          }`}
                          title={library.status === 'active' ? '禁用词库' : '启用词库'}
                        >
                          {library.status === 'active' ? <PowerOff className="w-4 h-4" /> : <Power className="w-4 h-4" />}
                        </button>
                        
                        <button
                          onClick={() => handleExportLibrary(library)}
                          className="text-green-600 hover:text-green-900 p-1.5 hover:bg-green-50 rounded-lg transition-colors"
                          title="导出词库"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                        
                        <button
                          onClick={() => handleDeleteLibrary(library.id)}
                          className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors"
                          title="删除词库"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => openWordsModal(library)}
                          className="text-blue-600 hover:text-blue-900 p-1.5 hover:bg-blue-50 rounded-lg transition-colors"
                          title="管理词条"
                        >
                          <FileText className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* 分页 */}
          <UnifiedPagination
             current={currentPage}
             pageSize={pageSize}
             total={filteredLibraries.length}
             onChange={(page) => setCurrentPage(page)}
           />
        </div>
      </main>

      {/* 新建词库模态框 */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">新建词库</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">词库名称</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入词库名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入词库描述"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    resetForm();
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleCreateLibrary}
                  disabled={!formData.name.trim()}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  创建
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 编辑词库模态框 */}
      {showEditModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">编辑词库</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">词库名称</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入词库名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入词库描述"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    resetForm();
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleEditLibrary}
                  disabled={!formData.name.trim()}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  保存
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 词条查看模态框 */}
      {showWordsModal && selectedLibrary && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {selectedLibrary.name} - 词条管理
                </h3>
                <button
                  onClick={() => setShowWordsModal(false)}
                  className="p-2 text-gray-400 rounded-full hover:bg-gray-100 hover:text-gray-600 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* 词条管理操作栏 */}
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => setShowCreateEntryModal(true)}
                      className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      新建词条
                    </button>
                    <button
                      onClick={handleImportEntries}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      导入词条
                    </button>
                    <button
                      onClick={handleExportEntries}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      导出词条
                    </button>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="text"
                        placeholder="搜索词条..."
                        value={entrySearchTerm}
                        onChange={(e) => setEntrySearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 w-64 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <span className="text-sm text-gray-500">
                      {filteredModalEntries.length} 条
                    </span>
                  </div>
                </div>
              </div>

              {/* 词条列表 */}
              <div className="mt-4 border border-gray-200 rounded-lg overflow-hidden">
                <div className="max-h-96 overflow-y-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50 sticky top-0">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">词条</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新时间</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {paginatedModalEntries.map((entry, index) => (
                        <tr key={entry.id}>
                          <td className="px-6 py-4 text-sm text-gray-500">{entryStartIndex + index + 1}</td>
                          <td className="px-6 py-4 text-sm text-gray-900">{entry.word}</td>
                          <td className="px-6 py-4 text-sm text-gray-500">{entry.createdAt}</td>
                          <td className="px-6 py-4 text-sm text-gray-500">{entry.updatedAt}</td>
                          <td className="px-6 py-4 text-right">
                            <div className="flex items-center justify-end space-x-2">
                                <button
                                  onClick={() => openEditEntryModal(entry)}
                                  className="text-indigo-600 hover:text-indigo-900 text-sm"
                                  title="编辑词条"
                                >
                                  <Edit className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleDeleteEntry(entry.id)}
                                  className="text-red-600 hover:text-red-900 text-sm"
                                  title="删除词条"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                 <UnifiedPagination
                   current={entryCurrentPage}
                   pageSize={entryPageSize}
                   total={filteredModalEntries.length}
                   onChange={(page) => setEntryCurrentPage(page)}
                 />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 新建词条模态框 */}
      {showCreateEntryModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-70 overflow-y-auto h-full w-full z-[60]">
          <div className="relative top-40 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">新建词条</h3>
                <button
                  onClick={() => {
                    setShowCreateEntryModal(false);
                    resetEntryForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Trash2 className="w-5 h-5" />
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">词条内容 *</label>
                  <input
                    type="text"
                    value={entryFormData.word}
                    onChange={(e) => setEntryFormData(prev => ({ ...prev, word: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入词条内容"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCreateEntryModal(false);
                    resetEntryForm();
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleCreateEntry}
                  disabled={!entryFormData.word.trim()}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  创建
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 编辑词条模态框 */}
      {showEditEntryModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-70 overflow-y-auto h-full w-full z-[60]">
          <div className="relative top-40 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">编辑词条</h3>
                <button
                  onClick={() => {
                    setShowEditEntryModal(false);
                    resetEntryForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Trash2 className="w-5 h-5" />
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">词条内容 *</label>
                  <input
                    type="text"
                    value={entryFormData.word}
                    onChange={(e) => setEntryFormData(prev => ({ ...prev, word: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入词条内容"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowEditEntryModal(false);
                    resetEntryForm();
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleEditEntry}
                  disabled={!entryFormData.word.trim()}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  保存
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FinalWordLibraryPage;