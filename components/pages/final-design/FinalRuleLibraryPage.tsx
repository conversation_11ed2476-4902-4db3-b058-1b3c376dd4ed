import React, { useState, useMemo, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

import { Plus, Search, Edit, Trash2, FileText, Code, BrainCircuit, Microscope, SlidersHorizontal, ChevronDown, Sparkles, PowerOff, Power, Settings, Siren } from 'lucide-react';
import { CreateRuleForm } from './FinalCreateRuleForm';
import { CreateRuleProForm } from './FinalCreateRuleProForm';
import { UnifiedSearchFilter, FilterField } from './components/UnifiedSearchFilter';
import UnifiedPagination from './components/UnifiedPagination';
import UnifiedPageHeader from './components/UnifiedPageHeader';

interface Condition {
    id: string;
    type: string;
}

interface Rule {
    id: string;
    name: string;
    status: 'enabled' | 'disabled';
    detectionLogic: 'AND' | 'OR';
    conditions: Condition[];
    creator: string;
    lastModified: string;
    description: string;
    importance: string;
    category: string;
    ruleType: string;
    isRealtimeAlert?: boolean;
}

const initialRulesData: Rule[] = [
  { id: 'RULE-001', name: '客户不满与合规词检测', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r1c1', type: '关键词检查'}, {id: 'r1c2', type: '正则表达式检查'}], creator: '王明', lastModified: '2024-05-20 10:30', description: '识别客户不满关键词，并排除特定合规术语。', importance: '严重违规', category: '合规风险', ruleType: '服务规范', isRealtimeAlert: true },
  { id: 'RULE-002', name: '标准开场白检测', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r2c1', type: '大模型检查'}], creator: '李静', lastModified: '2024-05-19 14:00', description: '检测坐席开场白是否符合"您好，很高兴为您服务"的标准句式。', importance: '轻度违规', category: '服务规范', ruleType: '服务规范', isRealtimeAlert: false },
  { id: 'RULE-003', name: '合规声明-录音告知', status: 'disabled', detectionLogic: 'AND', conditions: [{id: 'r3c1', type: '正则表达式检查'}], creator: '王明', lastModified: '2024-05-18 09:00', description: '验证坐席是否在通话开始时进行了录音告知。', importance: '中度违规', category: '合规风险', ruleType: '合规风险', isRealtimeAlert: false },
  { id: 'RULE-004', name: '客户情绪-激动模型', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r4c1', type: '大模型检查'}, {id: 'r4c2', type: '能量检测'}], creator: '管理员', lastModified: '2024-05-17 11:45', description: '使用AI模型结合能量检测判断客户是否存在激动情绪。', importance: '中度违规', category: '客户体验', ruleType: '客户体验', isRealtimeAlert: true },
  { id: 'RULE-005', name: '身份证号码格式校验', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r5c1', type: '正则表达式检查'}], creator: '张三', lastModified: '2024-05-16 16:20', description: '通过正则表达式验证身份证号码格式是否正确。', importance: '严重违规', category: '信息安全', ruleType: '信息安全', isRealtimeAlert: false },
  { id: 'RULE-006', name: '客服辱骂检测模型', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r6c1', type: '大模型检查'}], creator: '管理员', lastModified: '2024-05-15 13:00', description: '监控客服在对话中是否存在不文明用语。', importance: '严重违规', category: '服务红线', ruleType: '服务红线', isRealtimeAlert: true },
  { id: 'RULE-007', name: '上下文重复询问', status: 'disabled', detectionLogic: 'AND', conditions: [{id: 'r7c1', type: '大模型检查'}], creator: '李静', lastModified: '2024-05-14 18:00', description: '检测坐席是否在短时间内重复询问客户相同的问题。', importance: '轻度违规', category: '服务效率', ruleType: '服务效率', isRealtimeAlert: false },
  { id: 'RULE-008', name: '静音与抢话综合检测', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r8c1', type: '通话静音检查'}, {id: 'r8c2', type: '抢话/打断'}], creator: '王明', lastModified: '2024-05-13 10:10', description: '检测单次静音时长是否超过30秒，以及是否存在抢话。', importance: '中度违规', category: '服务质量', ruleType: '服务质量', isRealtimeAlert: true },
  { id: 'RULE-009', name: '信息实体-手机号与邮箱', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r9c1', type: '信息实体检查'}, {id: 'r9c2', type: '正则表达式检查'}], creator: '张三', lastModified: '2024-05-12 11:55', description: '提取对话中客户提及的手机号码和邮箱地址。', importance: '轻度违规', category: '信息提取', ruleType: '信息提取', isRealtimeAlert: false },
  { id: 'RULE-010', name: '大模型质检-金融产品推荐', status: 'enabled', detectionLogic: 'AND', conditions: [{id: 'r10c1', type: '大模型检查'}, {id: 'r10c2', type: '关键词检查'}], creator: '王明', lastModified: '2024-05-21 09:15', description: '使用大模型判断坐席的金融产品推荐是否合规、适当，并校验是否提及风险。', importance: '严重违规', category: '销售合规', ruleType: '销售合规', isRealtimeAlert: true },
];

const ALL_RULE_TYPES = [
    '关键词检查',
    '正则表达式检查',
    '通话静音检查',
    '语速检查',
    '抢话/打断',
    '录音时长检测',
    '非正常挂机',
    '非正常接听',
    '语句数检测',
    '角色判断',
    '能量检测',
    '大模型检查',
];

const ruleTypeIcons = {
    '关键词检查': <FileText className="w-4 h-4" />,
    '正则表达式检查': <Code className="w-4 h-4" />,
    '大模型检查': <BrainCircuit className="w-4 h-4" />,
    '通话静音检查': <Microscope className="w-4 h-4" />,
    '语速检查': <Microscope className="w-4 h-4" />,
    '抢话/打断': <Microscope className="w-4 h-4" />,
    '能量检测': <Microscope className="w-4 h-4" />,
    '角色判断': <Microscope className="w-4 h-4" />,
    '非正常挂机': <Microscope className="w-4 h-4" />,
    '非正常接听': <Microscope className="w-4 h-4" />,
    '录音时长检测': <Microscope className="w-4 h-4" />,
    '语句数检测': <Microscope className="w-4 h-4" />,
};

const ruleTypeColors = {
    '关键词检查': 'text-blue-600 bg-blue-100',
    '正则表达式检查': 'text-green-600 bg-green-100',
    '大模型检查': 'text-purple-600 bg-purple-100',
    '通话静音检查': 'text-teal-600 bg-teal-100',
    '语速检查': 'text-teal-600 bg-teal-100',
    '抢话/打断': 'text-teal-600 bg-teal-100',
    '能量检测': 'text-teal-600 bg-teal-100',
    '角色判断': 'text-teal-600 bg-teal-100',
    '非正常挂机': 'text-teal-600 bg-teal-100',
    '非正常接听': 'text-teal-600 bg-teal-100',
    '录音时长检测': 'text-teal-600 bg-teal-100',
    '语句数检测': 'text-teal-600 bg-teal-100',
}

const importanceColors: { [key: string]: string } = {
    '轻度违规': 'bg-blue-100 text-blue-800',
    '中度违规': 'bg-yellow-100 text-yellow-800',
    '严重违规': 'bg-red-100 text-red-800',
};

const PAGE_SIZE = 8;

const ALL_IMPORTANCE_LEVELS = ['轻度违规', '中度违规', '严重违规'];
const ALL_CATEGORIES = ['合规风险', '服务规范', '服务质量', '服务效率', '服务红线', '信息安全', '客户体验', '信息提取', '销售合规'];

const Switch = ({ checked, onChange }: { checked: boolean, onChange: () => void }) => (
    <button
        type="button"
        onClick={onChange}
        className={`${
            checked ? 'bg-green-500' : 'bg-gray-300'
        } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
        role="switch"
        aria-checked={checked}
    >
        <span
            aria-hidden="true"
            className={`${
                checked ? 'translate-x-5' : 'translate-x-0'
            } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
        />
    </button>
);

/**
 * 质检规则库页面
 * 提供质检规则的创建、查看、筛选、搜索和管理功能
 */
interface SearchFilters {
    ruleName: string;
    status: string;
    importance: string;
    category: string;
    ruleType: string;
    isRealtimeAlert: string;
}

export const FinalRuleLibraryPage: React.FC = () => {
    const [rules, setRules] = useState<Rule[]>(initialRulesData);
    const [searchFilters, setSearchFilters] = useState<SearchFilters>({
        ruleName: '',
        status: '',
        importance: '',
        category: '',
        ruleType: '',
        isRealtimeAlert: ''
    });
    const [currentPage, setCurrentPage] = useState(1);
    const [isCreateModalOpen, setCreateModalOpen] = useState(false);
    const [isCreateProModalOpen, setIsCreateProModalOpen] = useState(false);
    
    // 筛选字段配置
    const filterFields: FilterField[] = [
        { key: 'ruleName', label: '规则名称', type: 'text', placeholder: '请输入规则名称' },
        { key: 'status', label: '状态', type: 'select', options: [
            { value: 'enabled', label: '已启用' },
            { value: 'disabled', label: '已禁用' }
        ]},
        { key: 'isRealtimeAlert', label: '实时预警', type: 'select', options: [
            { value: 'true', label: '已开启' },
            { value: 'false', label: '已关闭' }
        ]},
        { key: 'importance', label: '重要程度', type: 'select', options: [
            { value: '轻度违规', label: '轻度违规' },
            { value: '中度违规', label: '中度违规' },
            { value: '严重违规', label: '严重违规' }
        ]},
        { key: 'category', label: '规则类型', type: 'select', options: [
            { value: '合规风险', label: '合规风险' },
            { value: '服务规范', label: '服务规范' },
            { value: '服务质量', label: '服务质量' },
            { value: '服务效率', label: '服务效率' },
            { value: '服务红线', label: '服务红线' },
            { value: '信息安全', label: '信息安全' },
            { value: '客户体验', label: '客户体验' },
            { value: '信息提取', label: '信息提取' },
            { value: '销售合规', label: '销售合规' }
        ]},
        { key: 'ruleType', label: '规则算子', type: 'select', options: ALL_RULE_TYPES.map(type => ({ value: type, label: type }))}
    ];

    // 处理筛选条件变化
    const handleFiltersChange = (newFilters: Record<string, any>) => {
        setSearchFilters(newFilters as SearchFilters);
    };

    // 处理查询
    const handleSearch = () => {
        setCurrentPage(1);
    };

    // 处理重置
    const handleReset = () => {
        setSearchFilters({
            ruleName: '',
            status: '',
            importance: '',
            category: '',
            ruleType: '',
            isRealtimeAlert: ''
        });
    };
    
    const filteredRules = useMemo(() => {
        return rules.filter(rule => {
            const matchesRuleName = !searchFilters.ruleName || 
                rule.name.toLowerCase().includes(searchFilters.ruleName.toLowerCase());
            const matchesStatus = !searchFilters.status || rule.status === searchFilters.status;
            const matchesImportance = !searchFilters.importance || rule.importance === searchFilters.importance;
            const matchesCategory = !searchFilters.category || rule.category === searchFilters.category;
            const matchesRuleType = !searchFilters.ruleType || 
                rule.conditions.some(c => c.type === searchFilters.ruleType);
            const matchesRealtimeAlert = !searchFilters.isRealtimeAlert || String(!!rule.isRealtimeAlert) === searchFilters.isRealtimeAlert;
            
            return matchesRuleName && matchesStatus && matchesImportance && matchesCategory && matchesRuleType && matchesRealtimeAlert;
        });
    }, [rules, searchFilters]);

    const totalPages = Math.ceil(filteredRules.length / PAGE_SIZE);
    const paginatedRules = filteredRules.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

    const handlePageChange = (page: number) => {
        if (page > 0 && page <= totalPages) {
            setCurrentPage(page);
        }
    };
    
    const handleStatusToggle = (ruleId: string) => {
        setRules(currentRules =>
            currentRules.map(rule =>
                rule.id === ruleId
                    ? { ...rule, status: rule.status === 'enabled' ? 'disabled' : 'enabled' }
                    : rule
            )
        );
    };

    const handleAddRule = (newRuleData: {
        name: string;
        description: string;
        importance: string;
        category: string;
        detectionLogic: 'ALL' | 'ANY' | 'NONE' | 'CUSTOM';
        conditions: Condition[];
        ruleType: string;
    }) => {
        const newRule: Rule = {
            id: `RULE-${String(rules.length + 1).padStart(3, '0')}`,
            status: 'enabled',
            creator: '当前用户',
            lastModified: new Date().toLocaleString(),
            ...newRuleData,
            detectionLogic: newRuleData.detectionLogic === 'ALL' ? 'AND' : 'OR',
        };
        setRules(prevRules => [newRule, ...prevRules]);
        setCreateModalOpen(false);
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <UnifiedPageHeader
                title="质检规则管理"
                subtitle="在这里创建和管理所有独立的质检规则，它们是构成质检方案的基础。"
                icon={Settings}
                badge={{ text: "规则管理", color: "purple" }}
                actions={[
                    {
                        label: "创建规则",
                        icon: Plus,
                        onClick: () => setCreateModalOpen(true),
                        variant: "primary"
                    }
                ]}
            />
            <main className="p-6 md:p-10">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    {/* 统一查询区域 */}
                    <UnifiedSearchFilter
                        fields={filterFields}
                        filters={searchFilters}
                        onFiltersChange={handleFiltersChange}
                        onSearch={handleSearch}
                        onReset={handleReset}
                    />

                    {/* Rules Table */}
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm text-left text-gray-600">
                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-4 py-3">序号</th>
                                    <th scope="col" className="px-6 py-3">规则名称</th>
                                    <th scope="col" className="px-6 py-3">规则类型</th>
                                    <th scope="col" className="px-6 py-3">规则算子</th>
                                    <th scope="col" className="px-6 py-3">创建人</th>
                                    <th scope="col" className="px-6 py-3">最后修改时间</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedRules.map((rule, index) => (
                                    <tr key={rule.id} className="bg-white border-b hover:bg-gray-50">
                                        <td className="px-4 py-4 text-gray-700">
                                            {(currentPage - 1) * PAGE_SIZE + index + 1}
                                        </td>
                                        <td className="px-6 py-4 align-top">
                                            <div className="flex items-center">
                                                {rule.isRealtimeAlert && (
                                                    <span title="已开启实时预警">
                                                        <Siren className="w-4 h-4 text-orange-500 mr-2 flex-shrink-0" />
                                                    </span>
                                                )}
                                                <span className="font-semibold text-gray-900">{rule.name}</span>
                                                <span className={`ml-3 text-xs font-medium px-2 py-0.5 rounded-full ${importanceColors[rule.importance]}`}>
                                                    {rule.importance}
                                                </span>
                                            </div>
                                            <p className={`mt-1 text-xs ${rule.status === 'enabled' ? 'text-gray-500' : 'text-gray-400'}`}>
                                                {rule.description}
                                            </p>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{rule.ruleType}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="flex items-center flex-wrap gap-2 max-w-xs">
                                                {[...new Set(rule.conditions.map(c => c.type))].map(t => (
                                                    <span key={t} className={`flex items-center gap-1.5 px-2 py-1 text-xs font-medium rounded-full ${ruleTypeColors[t as keyof typeof ruleTypeColors]}`}>
                                                        {ruleTypeIcons[t as keyof typeof ruleTypeIcons]}
                                                        {t}
                                                    </span>
                                                ))}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{rule.creator}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{rule.lastModified}</span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button
                                                onClick={() => handleStatusToggle(rule.id)}
                                                className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors"
                                                title="编辑"
                                            >
                                                <Edit className="w-4 h-4" />
                                            </button>
                                            <button
                                                onClick={() => handleStatusToggle(rule.id)}
                                                className={`p-1.5 rounded-lg transition-colors mx-1 ${
                                                    rule.status === 'enabled' 
                                                        ? 'text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50' 
                                                        : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                                                }`}
                                                title={rule.status === 'enabled' ? '禁用' : '启用'}
                                            >
                                                {rule.status === 'enabled' ? <PowerOff className="w-4 h-4" /> : <Power className="w-4 h-4" />}
                                            </button>
                                            <button
                                                onClick={() => handleStatusToggle(rule.id)}
                                                className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors"
                                                title="删除"
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    {filteredRules.length === 0 && (
                        <div className="text-center py-10 text-gray-500">
                            <p>未找到匹配的规则。</p>
                        </div>
                    )}
                    
                    {/* 分页 */}
                    {filteredRules.length > 0 && (
                        <UnifiedPagination
                        current={1}
                        total={filteredRules.length}
                        pageSize={10}
                        onChange={() => {}}
                        />
                    )}
                </motion.div>
            </main>
            
            {isCreateModalOpen && (
                <CreateRuleForm 
                    onClose={() => setCreateModalOpen(false)}
                    onSubmit={handleAddRule as any}
                    allRuleTypes={ALL_RULE_TYPES}
                />
            )}
            {isCreateProModalOpen && (
                <CreateRuleProForm 
                    onClose={() => setIsCreateProModalOpen(false)}
                    onSubmit={handleAddRule as any}
                />
            )}
        </div>
    );
};

export default FinalRuleLibraryPage;
