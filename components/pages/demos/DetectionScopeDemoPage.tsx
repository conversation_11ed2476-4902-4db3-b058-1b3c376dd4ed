import React, { useState, useMemo } from 'react';
import { HelpCircle } from 'lucide-react';

// --- 数据结构和初始数据 ---

interface Sentence {
  id: number;
  role: '客服' | '客户';
  text: string;
}

const mockConversation: Sentence[] = [
  { id: 1, role: '客户', text: '你好，我想咨询一下信用卡的事情。' },
  { id: 2, role: '客服', text: '您好，请问有什么可以帮您的？' },
  { id: 3, role: '客户', text: '我上周申请的白金卡，现在什么进度了？' },
  { id: 4, role: '客服', text: '好的，请您提供一下您的申请编号或者身份证号，我为您查询。' },
  { id: 5, role: '客户', text: '我的身份证是 320101...，你查查看。' }, // 默认的前置条件命中点
  { id: 6, role: '客服', text: '正在为您查询，请稍候... 好的女士，您的卡片已经审核通过了。' },
  { id: 7, role: '客户', text: '太好了！那大概什么时候能寄到呢？' },
  { id: 8, role: '客服', text: '一般是三个工作日内会通过快递寄出，请您注意查收。' },
  { id: 9, role: '客户', text: '好的，谢谢你。' },
  { id: 10, role: '客服', text: '不客气，祝您生活愉快。' },
];

type DetectionRole = '客服' | '客户' | '全员';
type DetectionScope = '全文' | '前置条件命中位置之前' | '前置条件命中位置前后' | '前置条件命中位置之后';
type SentenceStatus = 'normal' | 'prerequisite' | 'scoped' | 'final';

// --- 引导案例数据 ---
interface Preset {
  name: string;
  description: string;
  settings: {
    prerequisiteIndex: number | null;
    detectionRole: DetectionRole;
    detectionScope: DetectionScope;
    rangeStart: string;
    rangeEnd: string;
  };
}

const presets: Preset[] = [
    { name: '全文-前3句', description: '无前置条件时，选中全文的前3句', settings: { prerequisiteIndex: null, detectionRole: '全员', detectionScope: '全文', rangeStart: '1', rangeEnd: '3' } },
    { name: '全文-后3句', description: '无前置条件时，选中全文的后3句', settings: { prerequisiteIndex: null, detectionRole: '全员', detectionScope: '全文', rangeStart: '-3', rangeEnd: '-1' } },
    { name: '命中前-最后2句', description: '选中前置条件命中位置之前，客户所说的最后2句话', settings: { prerequisiteIndex: 4, detectionRole: '客户', detectionScope: '前置条件命中位置之前', rangeStart: '-2', rangeEnd: '-1' } },
    { name: '命中后-全部', description: '选中前置条件命中位置之后，客服所说的所有话', settings: { prerequisiteIndex: 4, detectionRole: '客服', detectionScope: '前置条件命中位置之后', rangeStart: '1', rangeEnd: '-1' } },
    { name: '命中前后-各2句', description: '选中命中点前后，全员所说的各2句话', settings: { prerequisiteIndex: 4, detectionRole: '全员', detectionScope: '前置条件命中位置前后', rangeStart: '-2', rangeEnd: '2' } },
    { name: '命中前后-仅当句', description: '仅选中命中点当前这一句话', settings: { prerequisiteIndex: 4, detectionRole: '客户', detectionScope: '前置条件命中位置前后', rangeStart: '0', rangeEnd: '0' } },
];

// --- 新增：时间轴可视化组件 ---
const TimelineVisualization: React.FC<{
  sentences: Sentence[];
  processedSentences: (Sentence & { status: SentenceStatus })[];
  prerequisiteIndex: number | null;
  rangeStart: string;
  rangeEnd: string;
}> = ({ sentences, processedSentences, prerequisiteIndex, rangeStart, rangeEnd }) => {
  const SVG_WIDTH = 800;
  const SVG_HEIGHT = 150;
  const Y_TIMELINE = SVG_HEIGHT / 2;
  const TICK_HEIGHT = 10;

  const tickSpacing = SVG_WIDTH / (sentences.length + 1);

  const getStatusColor = (status: SentenceStatus) => {
    switch (status) {
      case 'final': return '#3b82f6'; // blue-500
      case 'prerequisite': return '#f97316'; // orange-500
      case 'scoped': return '#9ca3af'; // gray-400
      default: return '#d1d5db'; // gray-300
    }
  };
  
  const finalIndices = processedSentences
    .map((s, i) => (s.status === 'final' ? i : -1))
    .filter(i => i !== -1);
  
  const firstFinalIndex = finalIndices.length > 0 ? Math.min(...finalIndices) : -1;
  const lastFinalIndex = finalIndices.length > 0 ? Math.max(...finalIndices) : -1;

  return (
    <svg width="100%" viewBox={`0 0 ${SVG_WIDTH} ${SVG_HEIGHT}`}>
      {/* Main timeline */}
      <line x1={0} y1={Y_TIMELINE} x2={SVG_WIDTH} y2={Y_TIMELINE} stroke="#9ca3af" strokeWidth="2" />

      {/* Ticks and Labels */}
      {sentences.map((sentence, index) => {
        const x = tickSpacing * (index + 1);
        const status = processedSentences[index]?.status || 'normal';
        const color = getStatusColor(status);
        return (
          <g key={sentence.id}>
            <line x1={x} y1={Y_TIMELINE - TICK_HEIGHT / 2} x2={x} y2={Y_TIMELINE + TICK_HEIGHT / 2} stroke={color} strokeWidth="3" />
            <text x={x} y={Y_TIMELINE + TICK_HEIGHT + 15} textAnchor="middle" fontSize="12" fill="#4b5563">
              {sentence.id}
            </text>
          </g>
        );
      })}

      {/* Prerequisite Marker */}
      {prerequisiteIndex !== null && (
        <g transform={`translate(${tickSpacing * (prerequisiteIndex + 1)}, ${Y_TIMELINE})`}>
          <path d="M 0 -25 L 5 -30 L -5 -30 Z" fill="#f97316" />
          <line x1="0" y1="-25" x2="0" y2="0" stroke="#f97316" strokeWidth="2" />
          <text x="0" y="-35" textAnchor="middle" fontSize="12" fill="#f97316" fontWeight="bold">命中点</text>
        </g>
      )}

      {/* Dynamic Range Bracket */}
      {firstFinalIndex !== -1 && (
         <g>
            <path 
              d={`
                M ${tickSpacing * (firstFinalIndex + 1)}, ${Y_TIMELINE - 20} 
                L ${tickSpacing * (firstFinalIndex + 1)}, ${Y_TIMELINE - 30} 
                L ${tickSpacing * (lastFinalIndex + 1)}, ${Y_TIMELINE - 30} 
                L ${tickSpacing * (lastFinalIndex + 1)}, ${Y_TIMELINE - 20}
              `}
              stroke="#3b82f6"
              strokeWidth="2"
              fill="none"
            />
            <text x={(tickSpacing * (firstFinalIndex + 1) + tickSpacing * (lastFinalIndex + 1)) / 2} y={Y_TIMELINE - 38} textAnchor="middle" fontSize="14" fill="#3b82f6" fontWeight="bold">
              范围: [ {rangeStart} , {rangeEnd} ]
            </text>
         </g>
      )}
    </svg>
  );
};

// --- 主组件 ---
export const DetectionScopeDemoPage: React.FC = () => {
  // --- 状态管理 ---
  const [prerequisiteIndex, setPrerequisiteIndex] = useState<number | null>(4); // 数组索引，第5句的索引是4
  const [detectionRole, setDetectionRole] = useState<DetectionRole>('全员');
  const [detectionScope, setDetectionScope] = useState<DetectionScope>('前置条件命中位置前后');
  const [rangeStart, setRangeStart] = useState<string>('-2');
  const [rangeEnd, setRangeEnd] = useState<string>('2');

  // --- 案例应用 ---
  const applyPreset = (preset: Preset) => {
    setPrerequisiteIndex(preset.settings.prerequisiteIndex);
    setDetectionRole(preset.settings.detectionRole);
    setDetectionScope(preset.settings.detectionScope);
    setRangeStart(preset.settings.rangeStart);
    setRangeEnd(preset.settings.rangeEnd);
  };

  // --- 核心计算逻辑 ---
  const processedSentences = useMemo(() => {
    const startNum = parseInt(rangeStart, 10);
    const endNum = parseInt(rangeEnd, 10);

    if (isNaN(startNum) || isNaN(endNum)) {
      return mockConversation.map(s => ({ ...s, status: 'normal' as SentenceStatus }));
    }

    const roleFilteredSentences = mockConversation
      .map((s, i) => ({ ...s, originalIndex: i }))
      .filter(s => detectionRole === '全员' || s.role === detectionRole);

    let paragraph: typeof roleFilteredSentences = [];
    const finalSelectedIds = new Set<number>();

    if (prerequisiteIndex === null || detectionScope === '全文') {
      paragraph = roleFilteredSentences;
      const startIndex = startNum > 0 ? startNum - 1 : paragraph.length + startNum;
      const endIndex = endNum > 0 ? endNum - 1 : paragraph.length + endNum;
      const finalStart = Math.min(startIndex, endIndex);
      const finalEnd = Math.max(startIndex, endIndex);

      if (finalStart >= 0 && finalEnd < paragraph.length) {
        for (let i = finalStart; i <= finalEnd; i++) {
          finalSelectedIds.add(paragraph[i].id);
        }
      }
    } else {
      switch (detectionScope) {
        case '前置条件命中位置之前':
          paragraph = roleFilteredSentences.filter(s => s.originalIndex < prerequisiteIndex);
          const startIndex = startNum > 0 ? startNum - 1 : paragraph.length + startNum;
          const endIndex = endNum > 0 ? endNum - 1 : paragraph.length + endNum;
          const finalStart = Math.min(startIndex, endIndex);
          const finalEnd = Math.max(startIndex, endIndex);
          if (finalStart >= 0 && finalEnd < paragraph.length) {
            for (let i = finalStart; i <= finalEnd; i++) {
              finalSelectedIds.add(paragraph[i].id);
            }
          }
          break;

        case '前置条件命中位置之后':
          paragraph = roleFilteredSentences.filter(s => s.originalIndex > prerequisiteIndex);
          const startIndexAfter = startNum > 0 ? startNum - 1 : paragraph.length + startNum;
          const endIndexAfter = endNum > 0 ? endNum - 1 : paragraph.length + endNum;
          const finalStartAfter = Math.min(startIndexAfter, endIndexAfter);
          const finalEndAfter = Math.max(startIndexAfter, endIndexAfter);
          if (finalStartAfter >= 0 && finalEndAfter < paragraph.length) {
            for (let i = finalStartAfter; i <= finalEndAfter; i++) {
              finalSelectedIds.add(paragraph[i].id);
            }
          }
          break;

        case '前置条件命中位置前后':
          const hitInRoleFilteredIndex = roleFilteredSentences.findIndex(s => s.originalIndex === prerequisiteIndex);
          if (hitInRoleFilteredIndex !== -1) {
            const beforeSentences = roleFilteredSentences.slice(0, hitInRoleFilteredIndex).reverse();
            const afterSentences = roleFilteredSentences.slice(hitInRoleFilteredIndex + 1);

            const range1 = Math.min(startNum, endNum);
            const range2 = Math.max(startNum, endNum);

            for (let i = range1; i <= range2; i++) {
              if (i < 0) {
                const index = Math.abs(i) - 1;
                if (index < beforeSentences.length) finalSelectedIds.add(beforeSentences[index].id);
              } else if (i > 0) {
                const index = i - 1;
                if (index < afterSentences.length) finalSelectedIds.add(afterSentences[index].id);
              } else {
                if (detectionRole === '全员' || roleFilteredSentences[hitInRoleFilteredIndex].role === detectionRole) {
                  finalSelectedIds.add(roleFilteredSentences[hitInRoleFilteredIndex].id);
                }
              }
            }
          }
          break;
      }
    }

    return mockConversation.map((sentence, index) => {
      let status: SentenceStatus = 'normal';
      let inScope = false;

      if (prerequisiteIndex !== null && detectionScope !== '全文') {
          if (detectionScope === '前置条件命中位置之前') {
             inScope = index < prerequisiteIndex;
          } else if (detectionScope === '前置条件命中位置之后') {
             inScope = index > prerequisiteIndex;
          } else { // 前后
             inScope = true;
          }
      } else { // 全文
          inScope = true;
      }
      
      if (!inScope) {
          status = 'normal';
      } else if (finalSelectedIds.has(sentence.id)) {
        status = 'final';
      } else if (prerequisiteIndex !== null && index === prerequisiteIndex) {
        status = 'prerequisite';
      } else {
        status = 'scoped';
      }

      return { ...sentence, status };
    });
  }, [prerequisiteIndex, detectionRole, detectionScope, rangeStart, rangeEnd]);

  // --- 优化后的实时解释 ---
  const explanation = useMemo(() => {
    let baseText = ``;
    if (prerequisiteIndex === null) {
      baseText = `在【无前置条件】的情况下，将检测【${detectionRole}】所说的【全文】。`;
    } else {
      baseText = `当前【前置条件命中第 ${prerequisiteIndex + 1} 句】。将检测【${detectionRole}】所说的【${detectionScope}】范围。`;
    }

    let logicText = '';
    const start = rangeStart;
    const end = rangeEnd;
    
    if (detectionScope === '前置条件命中位置前后') {
      logicText = `在该模式下，负数(-N)代表命中前的第N句，正数(+N)代表命中后的第N句，0代表命中句本身。范围 [${start}, ${end}] 即为最终选中范围。`;
    } else {
      logicText = `在该段落内，正数(N)代表顺数第N句，负数(-N)代表倒数第N句。范围 [${start}, ${end}] 即为最终选中范围。`;
    }
    
    return `${baseText} ${logicText}`;
  }, [prerequisiteIndex, detectionRole, detectionScope, rangeStart, rangeEnd]);

  // --- 渲染 ---
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="bg-white border-b border-gray-200 mb-6 -mx-6 -mt-6">
        <div className="max-w-full mx-auto px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-900">检测范围说明</h1>
          <p className="text-gray-600 mt-1">通过实时交互，深入理解正数、负数和零在不同检测范围下的具体含义。</p>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        <div className="lg:w-[40%] bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-12 self-start">
          <h2 className="text-xl font-semibold text-gray-800 border-b pb-3">控制面板</h2>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">① 前置条件命中句</label>
            <select
              value={prerequisiteIndex === null ? 'null' : prerequisiteIndex}
              onChange={e => {
                const newIndex = e.target.value === 'null' ? null : Number(e.target.value);
                setPrerequisiteIndex(newIndex);
                if (newIndex === null) {
                  setDetectionScope('全文');
                }
              }}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="null">无前置条件</option>
              {mockConversation.map((s, i) => (
                <option key={s.id} value={i}>第{s.id}句: "{s.text.substring(0, 10)}..."</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">② 检测角色</label>
            <select
              value={detectionRole}
              onChange={e => setDetectionRole(e.target.value as DetectionRole)}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option>全员</option>
              <option>客服</option>
              <option>客户</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">③ 检测段落</label>
            <select
              value={detectionScope}
              onChange={e => setDetectionScope(e.target.value as DetectionScope)}
              disabled={prerequisiteIndex === null}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md disabled:bg-gray-100 disabled:cursor-not-allowed"
            >
              <option disabled={prerequisiteIndex !== null}>全文</option>
              <option>前置条件命中位置之前</option>
              <option>前置条件命中位置前后</option>
              <option>前置条件命中位置之后</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">④ 数字范围 (支持正/负/零)</label>
            <div className="flex items-center gap-2">
              <input type="text" value={rangeStart} onChange={e => setRangeStart(e.target.value.trim())} className="w-full rounded-md border-gray-300 shadow-sm sm:text-sm text-center" />
              <span className="text-gray-500">到</span>
              <input type="text" value={rangeEnd} onChange={e => setRangeEnd(e.target.value.trim())} className="w-full rounded-md border-gray-300 shadow-sm sm:text-sm text-center" />
            </div>
          </div>
          <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
            <h3 className="font-semibold text-indigo-900 mb-2 flex items-center"><HelpCircle className="w-4 h-4 mr-2"/> 规则解释</h3>
            <p className="text-indigo-800 text-sm">{explanation}</p>
          </div>
        </div>

        <div className="lg:w-[60%] space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-800 border-b pb-3 mb-4">学习助手</h2>
              <div className="space-y-4">
                  <div>
                      <h3 className="text-lg font-medium text-gray-700 mb-3">引导案例 (点击加载)</h3>
                      <div className="flex flex-wrap gap-2">
                          {presets.map(p => (
                              <button key={p.name} onClick={() => applyPreset(p)} title={p.description} className="px-3 py-1.5 bg-gray-100 text-gray-800 rounded-md text-sm hover:bg-gray-200 transition-colors">
                                  {p.name}
                              </button>
                          ))}
                      </div>
                  </div>
              </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-800 border-b pb-3 mb-4">可视化结果</h2>
            <div>
              <h3 className="text-lg font-medium text-gray-700 mb-2">交互式时间轴</h3>
              <TimelineVisualization
                sentences={mockConversation}
                processedSentences={processedSentences}
                prerequisiteIndex={prerequisiteIndex}
                rangeStart={rangeStart}
                rangeEnd={rangeEnd}
              />
            </div>
            <div className="border-t pt-4 mt-4">
               <h3 className="text-lg font-medium text-gray-700 mb-3">对话内容详情</h3>
               <div className="space-y-3 mt-4 max-h-80 overflow-y-auto pr-2">
                {processedSentences.map(s => (
                  <div key={s.id} className={`p-3 rounded-md transition-all duration-300 border-l-4 ${
                    s.status === 'final' ? 'bg-blue-100 border-blue-500 shadow' :
                    s.status === 'prerequisite' ? 'bg-orange-100 border-orange-500' :
                    s.status === 'scoped' ? 'bg-gray-50 border-gray-300' :
                    'bg-gray-50 border-transparent opacity-40'
                  }`}>
                    <span className={`font-bold ${s.role === '客服' ? 'text-green-700' : 'text-purple-700'}`}>{s.role} (第{s.id}句):</span>
                    <span className="ml-2 text-gray-800">{s.text}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="border-t pt-4 mt-4">
              <h3 className="text-lg font-medium text-gray-700 mb-3">图例说明</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-x-4 gap-y-2 text-sm">
                  <div className="flex items-center"><div className="w-4 h-4 rounded-sm bg-blue-500 mr-2"></div>最终选中句</div>
                  <div className="flex items-center"><div className="w-4 h-4 rounded-sm bg-orange-500 mr-2"></div>前置条件命中句</div>
                  <div className="flex items-center"><div className="w-4 h-4 rounded-sm bg-gray-400 mr-2"></div>检测段落内句子</div>
                  <div className="flex items-center"><div className="w-4 h-4 rounded-sm bg-gray-300 mr-2"></div>段落外句子</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 