import React from 'react';
import { RegexCheckConfig } from './RegexCheckConfig';

interface RegexCheckTestResult {
  matched: boolean;
  matchedText: string[];
  matchedSentences: string[];
  excludedMatches: string[];
  details: string;
}

interface RegexCheckTesterProps {
  config: RegexCheckConfig;
  testText: string;
  onTestTextChange: (text: string) => void;
  testResult: RegexCheckTestResult | null;
  onRunTest: () => void;
  onClearResult: () => void;
}

/**
 * 正则表达式检查实时测试组件
 * 提供测试功能和结果展示
 */
const RegexCheckTester: React.FC<RegexCheckTesterProps> = ({
  config,
  testText,
  onTestTextChange,
  testResult,
  onRunTest,
  onClearResult
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          🧪 实时测试
        </h2>
        <div className="flex items-center space-x-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            即时验证
          </span>
          <button
            onClick={() => {
              onTestTextChange('客服：您好，我的联系方式是13812345678。\n客户：好的，我的手机号是15987654321。\n客服：请问您的身份证号是多少？\n客户：我的身份证号是110101199001011234。');
              onClearResult();
            }}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            重置示例
          </button>
        </div>
      </div>
      
      <div className="space-y-6">
        {/* 测试文本输入区 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">
              📝 测试文本
            </label>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>行数: {testText.split('\n').filter(line => line.trim()).length}</span>
              <span>•</span>
              <span>字符: {testText.length}</span>
            </div>
          </div>
          <div className="relative">
            <textarea
              id="test-text"
              rows={10}
              className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
              value={testText}
              onChange={(e) => onTestTextChange(e.target.value)}
              placeholder="请输入需要测试的对话文本，每行一句话...&#10;&#10;示例：&#10;客服：您好，我的联系方式是13812345678&#10;客户：好的，我的手机号是15987654321&#10;客服：请问您的身份证号是多少？"
            />
            {testText.trim() && (
              <button
                onClick={() => {
                  onTestTextChange('');
                  onClearResult();
                }}
                className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="清空文本"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          <div className="text-xs text-gray-500 flex items-center space-x-4">
            <span>💡 提示：每行代表一句对话，使用"客服："或"客户："开头</span>
          </div>
        </div>

        {/* 快速测试按钮 */}
        <div className="flex space-x-3">
          <button
            onClick={onRunTest}
            disabled={!testText.trim() || !config.matchRegex.trim()}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <span className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>执行测试</span>
            </span>
          </button>
          {testResult && (
            <button
              onClick={onClearResult}
              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              清除结果
            </button>
          )}
        </div>

        {/* 配置验证提示 */}
        {(!config.matchRegex.trim()) && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.664-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span className="text-sm text-yellow-800">请先配置命中正则表达式</span>
            </div>
          </div>
        )}
      </div>
      
      {/* 测试结果 */}
      {testResult && (
        <div className="mt-8 space-y-6">
          {/* 结果总览 */}
          <div className="border-t border-gray-200 pt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <span>📊</span>
                <span>测试结果</span>
              </h3>
              <button
                onClick={onClearResult}
                className="text-sm text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* 主要结果卡片 */}
            <div className={`relative overflow-hidden rounded-xl border-2 ${
              testResult.matched 
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
            }`}>
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                    testResult.matched ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    <span className={`text-2xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                      {testResult.matched ? '✓' : '✗'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className={`text-lg font-bold ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>
                      {testResult.matched ? '规则命中' : '规则未命中'}
                    </div>
                    <div className={`text-sm mt-1 ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                      {testResult.details}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      testResult.matched 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {testResult.matched ? '✅ 通过' : '❌ 未通过'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 详细结果 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 匹配内容 */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-blue-500">🎯</span>
                <h4 className="font-semibold text-gray-800">匹配内容</h4>
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {testResult.matchedText.length} 项
                </span>
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {testResult.matchedText.length > 0 ? (
                  testResult.matchedText.map((match, i) => (
                    <div key={i} className="flex items-center justify-between py-1">
                      <code className="text-sm bg-blue-50 text-blue-800 px-2 py-1 rounded border">
                        {match}
                      </code>
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                        ✓ 命中
                      </span>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500 italic">无匹配内容</div>
                )}
              </div>
            </div>

            {/* 排除内容 */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-purple-500">🚫</span>
                <h4 className="font-semibold text-gray-800">排除内容</h4>
                <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                  {testResult.excludedMatches.length} 项
                </span>
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {testResult.excludedMatches.length > 0 ? (
                  testResult.excludedMatches.map((match, i) => (
                    <div key={i} className="flex items-center justify-between py-1">
                      <code className="text-sm bg-purple-50 text-purple-800 px-2 py-1 rounded border">
                        {match}
                      </code>
                      <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                        ✗ 排除
                      </span>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500 italic">无排除内容</div>
                )}
              </div>
            </div>
          </div>

          {/* 命中句子详情 */}
          {testResult.matchedSentences.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-orange-500">💬</span>
                <h4 className="font-semibold text-gray-800">命中句子</h4>
                <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {testResult.matchedSentences.length} 条
                </span>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {testResult.matchedSentences.map((sentence, i) => (
                  <div key={i} className="p-3 bg-orange-50 border border-orange-100 rounded-md">
                    <div className="text-sm text-gray-800 font-medium">
                      {sentence}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 配置摘要 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-gray-500">⚙️</span>
              <h4 className="font-semibold text-gray-800">当前配置</h4>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">检测角色:</span>
                  <span className="font-medium">{
                    config.detectionRole === 'agent' ? '客服' : 
                    config.detectionRole === 'customer' ? '客户' : '所有角色'
                  }</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">检测范围:</span>
                  <span className="font-medium">{
                    config.detectionScope === 'full' ? '全文' : `第${config.rangeStart}-${config.rangeEnd}句`
                  }</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">命中规则:</span>
                  <code className="font-mono text-xs bg-white px-1 rounded border max-w-32 truncate" title={config.matchRegex}>
                    {config.matchRegex || '未设置'}
                  </code>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">排除规则:</span>
                  <code className="font-mono text-xs bg-white px-1 rounded border max-w-32 truncate" title={config.excludeRegex}>
                    {config.excludeRegex || '无'}
                  </code>
                </div>
                {config.singleSentence && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">单句生效:</span>
                    <span className="font-medium text-green-600">✓ 启用</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">匹配标志:</span>
                  <span className="font-medium text-blue-600">gim</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RegexCheckTester; 