import React from 'react';
import { RegexCheckConfig } from './RegexCheckConfig';

interface RegexCheckCase {
  name: string;
  description: string;
  config: RegexCheckConfig;
  testText: string;
  expectedResult: string;
}

interface RegexCheckCasesProps {
  onLoadCase: (config: RegexCheckConfig, testText: string) => void;
}

/**
 * 正则表达式检查案例库组件
 * 提供预设案例供用户学习和测试
 */
const RegexCheckCases: React.FC<RegexCheckCasesProps> = ({ onLoadCase }) => {
  // 预设案例
  const cases: RegexCheckCase[] = [
    {
      name: '手机号检测（排除13开头）',
      description: '检测手机号码，但排除13开头的号码',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        matchRegex: '1[3|4|5|7|8][0-9]{9}',
        excludeRegex: '13[0-9]{9}',
        singleSentence: false
      },
      testText: '客服：您好，我的联系方式是13812345678。\n客户：好的，我的手机号是15987654321。\n客服：还有一个号码是18712345678。',
      expectedResult: '应该命中（检测到15987654321和18712345678，排除13812345678）'
    },
    {
      name: '身份证号格式验证',
      description: '检测18位身份证号码格式',
      config: {
        detectionRole: 'customer' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        matchRegex: '[0-9]{17}[0-9Xx]',
        excludeRegex: '',
        singleSentence: false
      },
      testText: '客服：请提供您的身份证号码。\n客户：我的身份证号是110101199001011234。\n客户：之前的是110101199001011x。\n客服：好的，已记录。',
      expectedResult: '应该命中（客户提供了有效的身份证号格式）'
    },
    {
      name: '邮箱地址识别',
      description: '检测有效的邮箱地址格式',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        matchRegex: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}',
        excludeRegex: '',
        singleSentence: false
      },
      testText: '客服：请提供您的邮箱地址。\n客户：我的邮箱是zhang<EMAIL>。\n客服：还可以发送到******************。',
      expectedResult: '应该命中（检测到有效的邮箱地址格式）'
    },
    {
      name: '单句话内生效演示',
      description: '在单句话内检测车牌号和驾驶证号',
      config: {
        detectionRole: 'customer' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        matchRegex: '(车牌号|驾驶证号|发动机号)',
        excludeRegex: '',
        singleSentence: true
      },
      testText: '客服：请问您需要查询什么信息？\n客户：我想查询车牌号，还有驾驶证号的相关信息。\n客户：车牌号是京A12345。驾驶证号是110101199001011234。',
      expectedResult: '应该命中（单句话内包含车牌号或驾驶证号关键词）'
    },
    {
      name: '指定范围银行卡号检测',
      description: '在前2句中检测银行卡号',
      config: {
        detectionRole: 'customer' as const,
        detectionScope: 'range' as const,
        rangeStart: 1,
        rangeEnd: 2,
        matchRegex: '[0-9]{16,19}',
        excludeRegex: '',
        singleSentence: false
      },
      testText: '客户：我的银行卡号是6222024000001234567。\n客户：开户行是工商银行。\n客户：另一张卡号是6227001234567890123。',
      expectedResult: '应该命中（前2句中检测到银行卡号）'
    },
    {
      name: '中文姓名提取',
      description: '提取2-4个字的中文姓名，排除常见词汇',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        matchRegex: '[\\u4e00-\\u9fa5]{2,4}',
        excludeRegex: '(客服|先生|女士|您好|谢谢|不好意思|对不起)',
        singleSentence: false
      },
      testText: '客服：您好，我是王小明。\n客户：你好，我叫李华。\n客服：张三先生之前有联系过我们。',
      expectedResult: '应该命中（提取到王小明、李华、张三等姓名，排除客服、先生等）'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-4">
        <span className="text-2xl">📂</span>
        <h2 className="text-xl font-semibold text-gray-900">
          案例库
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          {cases.length} 个案例
        </span>
      </div>
      
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200 hover:border-blue-300 transition-colors">
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h4 className="font-semibold text-gray-800">{c.name}</h4>
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    案例 {index + 1}
                  </span>
                </div>
                <p className="text-xs text-gray-600 mb-2">{c.description}</p>
                <p className="text-xs text-emerald-600 font-medium">预期结果：{c.expectedResult}</p>
              </div>
              <button 
                onClick={() => onLoadCase(c.config, c.testText)}
                className="ml-4 text-sm bg-blue-500 text-white px-3 py-1.5 rounded-md hover:bg-blue-600 transition-colors font-medium"
              >
                加载案例
              </button>
            </div>
            
            {/* 案例配置预览 */}
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-500">检测角色:</span>
                    <span className="font-medium">{
                      c.config.detectionRole === 'agent' ? '客服' : 
                      c.config.detectionRole === 'customer' ? '客户' : '所有角色'
                    }</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">检测范围:</span>
                    <span className="font-medium">{
                      c.config.detectionScope === 'full' ? '全文' : `第${c.config.rangeStart}-${c.config.rangeEnd}句`
                    }</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-500">命中规则:</span>
                    <code className="font-mono text-xs bg-white px-1 rounded border max-w-24 truncate" title={c.config.matchRegex}>
                      {c.config.matchRegex}
                    </code>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">排除规则:</span>
                    <code className="font-mono text-xs bg-white px-1 rounded border max-w-24 truncate" title={c.config.excludeRegex}>
                      {c.config.excludeRegex || '无'}
                    </code>
                  </div>
                </div>
              </div>
              {c.config.singleSentence && (
                <div className="mt-2 flex items-center space-x-1">
                  <span className="text-xs text-green-600">✓</span>
                  <span className="text-xs text-green-600 font-medium">单句话内生效</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-sm">💡</span>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-blue-900 mb-2">使用说明</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 点击"加载案例"将自动填充配置和测试文本</li>
              <li>• 建议先运行案例测试，理解配置和结果的对应关系</li>
              <li>• 可以基于案例修改配置，探索不同的匹配效果</li>
              <li>• 每个案例都包含详细的配置预览和预期结果说明</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegexCheckCases;