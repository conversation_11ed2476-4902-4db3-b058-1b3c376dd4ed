import React from 'react';

/**
 * 正则表达式检查配置接口
 */
export interface RegexCheckConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  matchRegex: string;
  excludeRegex: string;
  singleSentence: boolean;
}

interface RegexCheckConfigProps {
  config: RegexCheckConfig;
  onConfigChange: (config: RegexCheckConfig) => void;
}

/**
 * 正则表达式检查配置演示组件
 * 提供完整的配置界面
 */
const RegexCheckConfigComponent: React.FC<RegexCheckConfigProps> = ({
  config,
  onConfigChange
}) => {
  const updateConfig = (updates: Partial<RegexCheckConfig>) => {
    onConfigChange({ ...config, ...updates });
  };

  // 验证正则表达式
  const validateRegex = (pattern: string): boolean => {
    if (!pattern.trim()) return true; // 空字符串视为有效
    try {
      new RegExp(pattern, 'gim');
      return true;
    } catch {
      return false;
    }
  };

  const isMatchRegexValid = validateRegex(config.matchRegex);
  const isExcludeRegexValid = validateRegex(config.excludeRegex);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">
          配置演示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>
      
      {/* 配置规则说明 */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-lg">📋</span>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 mb-3">配置规则总结</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🎯 检测角色</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>客服</strong>：仅检测"客服："开头的对话</li>
                    <li>• <strong>客户</strong>：仅检测"客户："开头的对话</li>
                    <li>• <strong>所有角色</strong>：检测全部对话内容</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">📍 检测范围</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>全文</strong>：检测所有符合角色条件的句子</li>
                    <li>• <strong>指定范围</strong>：只检测第X~Y句之间的内容</li>
                  </ul>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🔍 正则规则</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>命中规则</strong>：必填，定义要匹配的模式</li>
                    <li>• <strong>排除规则</strong>：可选，过滤不希望匹配的内容</li>
                    <li>• <strong>匹配标志</strong>：不区分大小写、多行匹配</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">⚡ 扩展功能</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>单句话内生效</strong>：按标点符号分割后匹配</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionRole}
            onChange={(e) => updateConfig({ detectionRole: e.target.value as any })}
          >
            <option value="agent">客服</option>
            <option value="customer">客户</option>
            <option value="all">所有角色</option>
          </select>
        </div>

        {/* 前置条件 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">前置条件</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="none">无</option>
          </select>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionScope}
            onChange={(e) => updateConfig({ detectionScope: e.target.value as any })}
          >
            <option value="full">全文</option>
            <option value="range">指定范围</option>
          </select>
        </div>

        {/* 指定范围配置 */}
        {config.detectionScope === 'range' && (
          <div className="flex items-center space-x-4">
            <label className="w-20 text-sm font-medium text-gray-700 text-right">范围</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">第</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeStart}
                onChange={(e) => updateConfig({ rangeStart: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">~</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeEnd}
                onChange={(e) => updateConfig({ rangeEnd: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">句</span>
              <span 
                className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                title="指定检测的句子范围，例如第1~3句表示只检测前3句对话"
              >
                ?
              </span>
            </div>
          </div>
        )}

        {/* 命中规则 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">
            命中
            <span className="text-red-500 ml-1">*</span>
          </label>
          <div className="flex-1">
            <div className="flex">
              <div className="flex-1 relative">
                <textarea
                  rows={3}
                  className={`w-full px-3 py-2 text-sm border rounded-l-md focus:outline-none focus:ring-2 transition-colors ${
                    isMatchRegexValid 
                      ? 'border-gray-300 focus:ring-blue-500 focus:border-blue-500' 
                      : 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                  }`}
                  placeholder="输入正则表达式，例如：1[3|4|5|7|8][0-9]{9}"
                  value={config.matchRegex}
                  onChange={(e) => updateConfig({ matchRegex: e.target.value })}
                />
                {!isMatchRegexValid && (
                  <div className="absolute -bottom-6 left-0 text-xs text-red-500">
                    正则表达式语法错误
                  </div>
                )}
              </div>
              <button 
                className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white text-sm rounded-r-md hover:from-purple-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200 flex items-center justify-center"
                title="AI 优化正则表达式"
              >
                <span className="text-lg">🤖</span>
              </button>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              💡 使用JavaScript正则表达式语法，默认启用不区分大小写(i)、全局匹配(g)、多行模式(m)
            </div>
          </div>
        </div>

        {/* 排除规则 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">排除</label>
          <div className="flex-1">
            <div className="flex">
              <div className="flex-1 relative">
                <textarea
                  rows={3}
                  className={`w-full px-3 py-2 text-sm border rounded-l-md focus:outline-none focus:ring-2 transition-colors ${
                    isExcludeRegexValid 
                      ? 'border-gray-300 focus:ring-blue-500 focus:border-blue-500' 
                      : 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                  }`}
                  placeholder="可选：输入要排除的正则表达式，例如：13[0-9]{9}"
                  value={config.excludeRegex}
                  onChange={(e) => updateConfig({ excludeRegex: e.target.value })}
                />
                {!isExcludeRegexValid && (
                  <div className="absolute -bottom-6 left-0 text-xs text-red-500">
                    正则表达式语法错误
                  </div>
                )}
              </div>
              <button 
                className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white text-sm rounded-r-md hover:from-purple-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200 flex items-center justify-center"
                title="AI 优化排除规则"
              >
                <span className="text-lg">🤖</span>
              </button>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              💡 在命中规则的基础上，过滤掉不希望匹配的内容（留空表示不排除）
            </div>
          </div>
        </div>

        {/* 扩展功能 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">扩展功能</label>
          <div className="flex flex-col space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="mr-2 text-blue-600 focus:ring-blue-500 rounded"
                checked={config.singleSentence}
                onChange={(e) => updateConfig({ singleSentence: e.target.checked })}
              />
              <span className="text-sm text-gray-700">单句话内生效</span>
              <span 
                className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                                  title="将句子按标点符号（逗号、句号等）拆分为小段，在每个小段中分别进行正则匹配。例如：'你好，这里是xxx，向您推荐产品' 会拆分为 '你好'、'这里是xxx'、'向您推荐产品' 三段分别匹配。"
              >
                ?
              </span>
            </label>
          </div>
        </div>

        {/* 正则表达式帮助 */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-center space-x-2 mb-3">
            <span className="text-gray-600">📚</span>
            <h4 className="font-semibold text-gray-800">常用正则表达式</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">手机号码:</span>
                <code className="bg-white px-2 py-1 rounded border text-gray-800">1[3-9][0-9]{'{9}'}</code>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">身份证号:</span>
                <code className="bg-white px-2 py-1 rounded border text-gray-800">[0-9]{'{17}'}[0-9X]</code>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">邮箱地址:</span>
                <code className="bg-white px-2 py-1 rounded border text-gray-800">[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{'{2,}'}</code>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">银行卡号:</span>
                <code className="bg-white px-2 py-1 rounded border text-gray-800">[0-9]{'{16,19}'}</code>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">中文姓名:</span>
                <code className="bg-white px-2 py-1 rounded border text-gray-800">[\u4e00-\u9fa5]{'{2,4}'}</code>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">车牌号:</span>
                <code className="bg-white px-2 py-1 rounded border text-gray-800">[\u4e00-\u9fa5][A-Z][A-Z0-9]{'{5}'}</code>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegexCheckConfigComponent; 