import React from 'react';
import type { SentenceCountConfigType } from '../SentenceCountCheckPage';
import { Tooltip } from '../../../common/Tooltip';

/**
 * @typedef {object} SentenceCountConfigProps
 * @property {SentenceCountConfigType} config
 * @property {React.Dispatch<React.SetStateAction<SentenceCountConfigType>>} setConfig
 */
interface SentenceCountConfigProps {
  config: SentenceCountConfigType;
  setConfig: React.Dispatch<React.SetStateAction<SentenceCountConfigType>>;
}

/**
 * 对话语句数检测配置组件
 * @param {SentenceCountConfigProps} props
 * @returns {React.ReactElement}
 */
const SentenceCountConfig: React.FC<SentenceCountConfigProps> = ({ config, setConfig }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">
          配置演示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>

      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-24 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionRole}
            onChange={(e) => setConfig({ ...config, detectionRole: e.target.value as any })}
          >
            <option value="all">所有角色</option>
            <option value="agent">客服</option>
            <option value="customer">客户</option>
          </select>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-24 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionScope}
            onChange={(e) => setConfig({ ...config, detectionScope: e.target.value as any })}
          >
            <option value="full">全文</option>
            <option value="range">指定范围</option>
          </select>
        </div>

        {/* 指定范围配置 */}
        {config.detectionScope === 'range' && (
          <div className="flex items-center space-x-4">
            <label className="w-24 text-sm font-medium text-gray-700 text-right">范围</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">第</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeStart}
                onChange={(e) => setConfig({ ...config, rangeStart: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">~</span>
              <input
                type="number"
                min={config.rangeStart}
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeEnd}
                onChange={(e) => setConfig({ ...config, rangeEnd: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">句</span>
              <Tooltip text="指定检测的句子范围，例如第1~3句表示只检测前3句对话。">
                <span className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50">?</span>
              </Tooltip>
            </div>
          </div>
        )}

        {/* 检查逻辑 */}
        <div className="flex items-center space-x-4">
          <label className="w-24 text-sm font-medium text-gray-700 text-right">检查逻辑</label>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-800">对话语句数</span>
            <select
              className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={config.operator}
              onChange={(e) => setConfig({ ...config, operator: e.target.value as any })}
            >
              <option value="greater">大于</option>
              <option value="less">小于</option>
              <option value="equal">等于</option>
            </select>
            <input
              type="number"
              min="0"
              className="w-24 px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={config.count}
              onChange={(e) => setConfig({ ...config, count: parseInt(e.target.value) || 0 })}
            />
            <span className="text-sm text-gray-700">句</span>
            <Tooltip text="设置用于比较的语句数量阈值。">
              <span className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50">?</span>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SentenceCountConfig; 