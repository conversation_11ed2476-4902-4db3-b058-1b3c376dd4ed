import React from 'react';
import type { SentenceCountConfigType } from '../SentenceCountCheckPage';

/**
 * @typedef {object} SentenceCountCasesProps
 * @property {React.Dispatch<React.SetStateAction<SentenceCountConfigType>>} setConfig
 * @property {React.Dispatch<React.SetStateAction<string>>} setTestText
 */
interface SentenceCountCasesProps {
  setConfig: React.Dispatch<React.SetStateAction<SentenceCountConfigType>>;
  setTestText: React.Dispatch<React.SetStateAction<string>>;
}

/**
 * 对话语句数检测案例组件
 * @param {SentenceCountCasesProps} props
 * @returns {React.ReactElement}
 */
const SentenceCountCases: React.FC<SentenceCountCasesProps> = ({ setConfig, setTestText }) => {
  const cases = [
    {
      name: '客服未说话检测',
      description: '检测客服是否在对话中一句话未说。',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        operator: 'equal' as const,
        count: 0,
      },
      testText: '客户：喂？有人吗？\n客户：怎么没人说话啊？',
      expectedResult: '应该命中（客服语句数等于0）',
    },
    {
      name: '客户交流过少检测',
      description: '检测客户的发言是否少于3句，可能为无效沟通。',
      config: {
        detectionRole: 'customer' as const,
        detectionScope: 'full' as const,
        operator: 'less' as const,
        count: 3,
      },
      testText: '客服：您好，请问有什么可以帮您的？\n客户：没事了。\n客服：好的，再见。',
      expectedResult: '应该命中（客户语句数小于3）',
    },
    {
      name: '对话轮次过多检测',
      description: '检测总对话是否超过20句，可能为冗长通话。',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        operator: 'greater' as const,
        count: 20,
      },
      testText: Array.from({ length: 22 }, (_, i) => `${i % 2 === 0 ? '客服' : '客户'}：这是测试对话第${i + 1}句。`).join('\n'),
      expectedResult: '应该命中（总语句数大于20）',
    },
  ];

  const loadCase = (caseData: typeof cases[0]) => {
    setConfig(prevConfig => ({
        ...prevConfig,
        ...caseData.config,
        rangeStart: 1, // Reset range on case load
        rangeEnd: 5,
    }));
    setTestText(caseData.testText);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        📂 案例库
      </h2>
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500 mt-1">{c.description}</p>
                <p className="text-xs text-blue-500 mt-1">预期结果：{c.expectedResult}</p>
              </div>
              <button
                onClick={() => loadCase(c)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SentenceCountCases; 