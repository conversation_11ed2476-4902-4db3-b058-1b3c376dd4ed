import React from 'react';

/**
 * 对话语句数检测使用提示组件
 * @returns {React.ReactElement}
 */
const SentenceCountTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          使用提示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        {/* 基础操作提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⌨️</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">基础操作</h4>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                <li><strong>检测角色</strong>: 明确要统计哪一方的发言。</li>
                <li><strong>检测范围</strong>: "全文"适用于大多数场景，"指定范围"可用于分析特定阶段。</li>
                <li><strong>检查逻辑</strong>: 这是规则的核心，请仔细设置比较符和句数值。</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 最佳实践 */}
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">最佳实践</h4>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                <li><strong>初筛利器</strong>: 使用"小于1句"快速过滤掉无效或未接通的通话。</li>
                <li><strong>组合使用</strong>: 与"录音时长"算子结合，可以更精准地定位低效沟通。</li>
                <li><strong>分析开场</strong>: 使用"指定范围"为"第1-3句"来检查开场白是否过于简单或复杂。</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SentenceCountTips; 