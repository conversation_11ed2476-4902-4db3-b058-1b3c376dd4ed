import React, { useState } from 'react';
import { PageHeader } from '../../PageHeader';
import DurationCheckConcepts from './durationCheck/DurationCheckConcepts';
import DurationCheckConfig from './durationCheck/DurationCheckConfig';
import DurationCheckTester from './durationCheck/DurationCheckTester';
import DurationCheckCases from './durationCheck/DurationCheckCases';
import DurationCheckTips from './durationCheck/DurationCheckTips';

/**
 * 解析时间戳字符串 [mm:ss]
 * @param line 对话行
 * @returns {number | null} 秒数或null
 */
const parseTimestamp = (line: string): number | null => {
  const match = line.match(/\[(\d{2}):(\d{2})\]/);
  if (match) {
    const minutes = parseInt(match[1], 10);
    const seconds = parseInt(match[2], 10);
    return minutes * 60 + seconds;
  }
  return null;
};

/**
 * 录音时长检测页面
 * @constructor
 */
const DurationCheckPage: React.FC = () => {
  const [config, setConfig] = useState({
    operator: '>' as '>' | '<' | '>=' | '<=' | '=',
    duration: 600,
  });

  const [testText, setTestText] = useState(
    '[00:05] 客户：你好。\n[10:01] 客服：好的，我们已经处理完成。'
  );

  const [testResult, setTestResult] = useState<{
    matched: boolean;
    totalDuration: number | null;
    details: string;
  } | null>(null);

  const runTest = () => {
    const lines = testText.split('\n').filter(s => s.trim());
    if (lines.length === 0) {
      setTestResult({
        matched: false,
        totalDuration: null,
        details: '未检测到任何对话内容。'
      });
      return;
    }

    const timestamps = lines.map(parseTimestamp).filter(ts => ts !== null) as number[];
    if (timestamps.length === 0) {
      setTestResult({
        matched: false,
        totalDuration: null,
        details: '错误：未在对话中找到任何有效的时间戳 [mm:ss]。'
      });
      return;
    }

    const totalDuration = Math.max(...timestamps);

    let timeMatched = false;
    switch (config.operator) {
      case '>':
        timeMatched = totalDuration > config.duration;
        break;
      case '<':
        timeMatched = totalDuration < config.duration;
        break;
      case '>=':
        timeMatched = totalDuration >= config.duration;
        break;
      case '<=':
        timeMatched = totalDuration <= config.duration;
        break;
      case '=':
        timeMatched = totalDuration === config.duration;
        break;
    }

    setTestResult({
      matched: timeMatched,
      totalDuration,
      details: `规则${timeMatched ? '命中' : '未命中'}。检测到总时长为 ${totalDuration} 秒，${timeMatched ? '满足' : '不满足'}条件 ( ${config.operator} ${config.duration} 秒 )。`
    });
  };

  const loadCase = (caseConfig: any, caseText: string) => {
    setConfig(caseConfig);
    setTestText(caseText);
    setTestResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="🎙️ 录音时长检测"
        description="用于检测录音文件的时长是否大于或小于设定值，检测整通对话的时长。"
        badge="语音检查"
        tag="基础入门"
      />

      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          <div className="w-[60%] space-y-6">
            <DurationCheckConcepts />
            <DurationCheckConfig config={config} setConfig={setConfig} />
            <DurationCheckTester
              testText={testText}
              setTestText={setTestText}
              testResult={testResult}
              setTestResult={setTestResult}
              runTest={runTest}
            />
          </div>

          <div className="w-[40%] space-y-6">
            <DurationCheckCases loadCase={loadCase} />
            <DurationCheckTips />
          </div>
        </div>
        
        <div className="max-w-full mx-auto px-6 pb-6 mt-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <span className="text-2xl">💡</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-blue-900 mb-2">
                  学习建议
                </h3>
                <div className="text-blue-800 space-y-2 text-sm">
                  <p>• <strong>从案例开始</strong>：加载预设案例，观察不同时长和比较符如何影响结果。</p>
                  <p>• <strong>理解时长计算</strong>：系统会将文本中最后一个出现的时间戳作为对话总时长，与配置进行比较。</p>
                  <p>• <strong>注意单位</strong>：配置中的时长单位是"秒"，请根据需要进行换算（例如10分钟 = 600秒）。</p>
                  <p>• <strong>实际应用</strong>：此算子非常适合作为规则的"前置条件"，用于快速过滤掉时长不符合基本要求的通话录音。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DurationCheckPage; 