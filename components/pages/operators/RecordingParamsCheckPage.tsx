import React, { useState } from 'react';
import { RecordingParamsCheckConcepts } from './recordingParamsCheck/RecordingParamsCheckConcepts';
import { RecordingParamsCheckConfig } from './recordingParamsCheck/RecordingParamsCheckConfig';
import { RecordingParamsCheckCases } from './recordingParamsCheck/RecordingParamsCheckCases';
import { RecordingParamsCheckTips } from './recordingParamsCheck/RecordingParamsCheckTips';
import { RecordingParamsCheckTester, TestResult } from './recordingParamsCheck/RecordingParamsCheckTester';

/**
 * 随录参数检查页面
 * @constructor
 */
export const RecordingParamsCheckPage: React.FC = () => {
  const [config, setConfig] = useState({
    conditions: [
      {
        id: 'cond1',
        type: 'system' as 'system' | 'custom',
        name: '业务线',
        operator: '=' as '=' | '!=' | 'contains' | 'not_contains',
        values: ['华东'],
      },
    ],
  });

  const [testParams, setTestParams] = useState<object>({ "业务线": "华东" });
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  
  const loadCase = (caseConfig: any, caseTestParams: any) => {
    setConfig(caseConfig);
    setTestParams(caseTestParams);
    setTestResult(null);
  };

  const runTest = () => {
    let matchedAll = true;
    const matchedConditions: string[] = [];

    for (const condition of config.conditions) {
      const paramValue = (testParams as any)[condition.name];
      let conditionMet = false;

      if (paramValue !== undefined) {
          switch (condition.operator) {
            case '=':
              conditionMet = condition.values.includes(String(paramValue));
              break;
            case '!=':
              conditionMet = !condition.values.includes(String(paramValue));
              break;
            case 'contains':
              conditionMet = condition.values.some(v => String(paramValue).includes(v));
              break;
            case 'not_contains':
              conditionMet = !condition.values.some(v => String(paramValue).includes(v));
              break;
          }
      }
      
      if(conditionMet) {
        matchedConditions.push(condition.id);
      } else {
        matchedAll = false;
      }
    }

    setTestResult({
        matched: matchedAll,
        matchedConditions,
        details: matchedAll ? '所有条件均已满足' : '部分条件未满足'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-900">
            ⚙️ 随录参数检查
          </h1>
          <p className="text-gray-600 mt-1">
            根据通话的随录参数（如业务线、技能组等）进行质检
          </p>
        </div>
      </div>

      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          <div className="w-[60%] space-y-6">
            <RecordingParamsCheckConcepts />
            <RecordingParamsCheckConfig config={config} setConfig={setConfig} />
            <RecordingParamsCheckTester 
                config={config}
                testParams={testParams}
                setTestParams={setTestParams}
                testResult={testResult}
                setTestResult={setTestResult}
                runTest={runTest}
            />
          </div>
          <div className="w-[40%] space-y-6">
            <RecordingParamsCheckCases loadCase={loadCase} />
            <RecordingParamsCheckTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>熟悉字段类型</strong>：理解"系统常规字段"和"更多自定义字段"的区别，并根据需要选择。</p>
                <p>• <strong>掌握操作符</strong>：练习使用不同的比较操作符（=, ≠, 包含, 不包含）来满足各种业务场景。</p>
                <p>• <strong>实践JSON测试</strong>：在实时测试中，尝试使用嵌套的JSON对象作为自定义参数，模拟真实数据结构。</p>
                <p>• <strong>组合应用</strong>：尝试将"随录参数检查"作为前置条件，与其他算子（如"关键词检查"）结合，创建更强大的复合质检规则。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 