import React from 'react';
import { motion } from 'framer-motion';
import { BookCopy, ShieldCheck, ShoppingCart } from 'lucide-react';
import { LargeModelConfig } from './LargeModelCheckConfig';

interface LargeModelCheckCasesProps {
  onLoadCase: (config: LargeModelConfig, sampleText: string) => void;
}

const cases: { name: string; icon: React.ReactElement; description: string; config: LargeModelConfig; sampleText: string; }[] = [
  {
    name: '服务规范检查',
    icon: <ShieldCheck className="w-6 h-6 text-green-500" />,
    description: '检查客服在对话中是否遵守了基本的文明用语、开场白和结束语规范。',
    config: {
      scenarioName: '通用客服场景',
      backgroundKnowledge: '客服人员在与客户沟通时，应保持礼貌和专业。开场需要有问候语，结束时需要有结束语。',
      dimensions: [
        { id: 1, name: '使用文明用语', definition: '客服在整个对话过程中，使用了"您好"、"请问"、"谢谢"等礼貌用语。' },
        { id: 2, name: '标准开场白', definition: '客服在对话开始时，主动问好并表明身份，例如"您好，xx客服很高兴为您服务"。' },
        { id: 3, name: '标准结束语', definition: '客服在对话结束时，主动询问客户是否还有其他问题，并表示感谢，例如"请问还有什么可以帮您吗？再见。"。' },
      ],
    },
    sampleText: `客户：你好，我想咨询一下信用卡的年费政策。
客服：您好！很高兴为您服务。我们的信用卡首年是免年费的，当年刷卡消费满6次就可以免除次年的年费。
客户：好的，我明白了，谢谢你。
客服：不客气，请问还有什么可以帮您吗？再见。`
  },
  {
    name: '金融产品销售合规',
    icon: <BookCopy className="w-6 h-6 text-blue-500" />,
    description: '检查金融产品销售过程中，是否按规定向客户揭示了产品风险。',
    config: {
      scenarioName: '理财产品电话销售',
      backgroundKnowledge: '根据合规要求，销售任何非保本理财产品时，必须向客户明确说明产品存在本金亏损的风险，并提及"投资有风险，入市需谨慎"等类似话语。',
      dimensions: [
        { id: 1, name: '风险充分揭示', definition: '客服或销售人员在介绍产品时，明确提到了产品可能存在亏损、风险、不保本等关键信息。' },
        { id: 2, name: '确认客户理解', definition: '在揭示风险后，销售人员主动询问客户是否已经理解相关风险点。' },
      ],
    },
    sampleText: `客户：你好，我想了解一下你们那个"稳健增利"理财产品。
销售：您好！很高兴为您介绍。"稳健增利"是我们行的一款明星产品，预期收益率很不错。不过我需要提醒您，这款产品不属于保本类型，虽然风险等级不高，但理论上存在本金亏损的可能性。投资有风险，入市需谨慎，您能理解吗？
客户：哦，好的，我明白了，风险我了解。`
  },
  {
    name: '电商退货流程处理',
    icon: <ShoppingCart className="w-6 h-6 text-orange-500" />,
    description: '检查客服在处理客户退货请求时，是否清晰说明了退货步骤和注意事项。',
    config: {
      scenarioName: '电商平台客户咨询退货',
      backgroundKnowledge: '平台标准退货流程为：客户APP申请 -> 填写退货原因 -> 邮寄商品 -> 仓库收货验货 -> 完成退款。运费由保险公司承担。',
      dimensions: [
        { id: 1, name: '清晰说明退货步骤', definition: '客服准确告知了客户需要通过App申请，并邮寄回商品的完整流程。' },
        { id: 2, name: '告知运费政策', definition: '客服向客户说明了退货运费的处理方式（例如由运费险承担或客户自理）。' },
        { id: 3, name: '安抚客户情绪', definition: '当客户因为商品问题表达不满时，客服首先进行了安抚，而不是直接进入流程说明。' },
      ],
    },
    sampleText: `客户：我上周买的那个耳机有质量问题，我想退货。
客服：您好，很抱歉给您带来了不好的体验。您可以在咱们的App里找到对应订单，点击申请售后，选择退货就可以了。把商品按里面的地址寄回，我们仓库收到验货后就会马上为您办理退款。退货的运费会由运费险承担，您先垫付一下，后续会补给您。
客户：好的，知道了。`
  },
];


const LargeModelCheckCases: React.FC<LargeModelCheckCasesProps> = ({ onLoadCase }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      className="p-6 bg-white rounded-lg border border-gray-200"
    >
      <h3 className="text-xl font-semibold text-gray-800 mb-4">案例库</h3>
      <div className="space-y-4">
        {cases.map((caseItem, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
            className="p-4 bg-gray-50 rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
          >
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center mb-2">
                  <div className="flex-shrink-0 mr-3">
                    {caseItem.icon}
                  </div>
                  <h4 className="text-md font-bold text-gray-900">{caseItem.name}</h4>
                </div>
                <p className="text-sm text-gray-600 ml-9">{caseItem.description}</p>
              </div>
              <button
                onClick={() => onLoadCase(caseItem.config, caseItem.sampleText)}
                className="ml-4 flex-shrink-0 px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                加载此案例
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default LargeModelCheckCases;
