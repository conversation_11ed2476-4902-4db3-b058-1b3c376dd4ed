import React from 'react';
import { motion } from 'framer-motion';
import { Sparkles, HelpCircle, Check, X } from 'lucide-react';

const tips = [
  {
    icon: <Sparkles className="w-5 h-5 text-yellow-500" />,
    title: '让"维度定义"更具体',
    good: {
      label: '推荐',
      example: '客服在对话开场需要与客户核对姓名或手机号。',
    },
    bad: {
      label: '不推荐',
      example: '客服需要确认客户信息。',
    },
    explanation: '具体"的定义能让模型更精确地理解您的意图，避免模糊和歧义。'
  },
  {
    icon: <HelpCircle className="w-5 h-5 text-blue-500" />,
    title: '善用"背景知识"',
    good: {
      label: '推荐',
      example: '背景知识：本公司规定，所有投诉电话必须在24小时内由专人回访。',
    },
    bad: {
      label: '不推荐',
      example: '背景知识留空。',
    },
    explanation: '背景知识"能为模型提供重要的上下文，帮助它理解特定业务领域的专有术语和规则。'
  },
  {
    icon: <Sparkles className="w-5 h-5 text-yellow-500" />,
    title: '维度拆分要单一',
    good: {
      label: '推荐',
      example: '维度1: 主动问好; 维度2: 核实身份',
    },
    bad: {
      label: '不推荐',
      example: '维度1: 主动问好并核实身份',
    },
    explanation: '每个维度只检查一个独立的点，能让测试结果更清晰，定位问题更准确。'
  },
];


const LargeModelCheckTips: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
      className="p-6 bg-white rounded-lg border border-gray-200"
    >
      <h3 className="text-xl font-semibold text-gray-800 mb-4">使用提示</h3>
      <div className="space-y-4">
        {tips.map((tip, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 + 0.4 }}
            className="p-4 bg-gray-50 rounded-lg border border-gray-200"
          >
            <div className="flex items-center font-bold text-gray-800 mb-2">
              {tip.icon}
              <span className="ml-2">{tip.title}</span>
            </div>
            <div className="text-sm space-y-2">
              <div className="flex items-start">
                <div className="flex-shrink-0 w-16 text-green-600 font-semibold flex items-center">
                  <Check className="w-4 h-4 mr-1" />
                  {tip.good.label}
                </div>
                <div className="text-gray-700">{tip.good.example}</div>
              </div>
              <div className="flex items-start">
                <div className="flex-shrink-0 w-16 text-red-600 font-semibold flex items-center">
                  <X className="w-4 h-4 mr-1" />
                  {tip.bad.label}
                </div>
                <div className="text-gray-500">{tip.bad.example}</div>
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2 pt-2 border-t border-gray-200">{tip.explanation}</p>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default LargeModelCheckTips;
