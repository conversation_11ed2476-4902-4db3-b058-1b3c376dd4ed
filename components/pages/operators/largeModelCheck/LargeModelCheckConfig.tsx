import React from 'react';
import { motion } from 'framer-motion';
import { Plus<PERSON>ircle, Trash2, FileText, BrainCircuit, HelpCircle } from 'lucide-react';
import { Tooltip } from '../../../common/Tooltip';

export interface Dimension {
  id: number;
  name: string;
  definition: string;
}

export interface LargeModelConfig {
  scenarioName: string;
  backgroundKnowledge: string;
  dimensions: Dimension[];
}

interface LargeModelCheckConfigProps {
  config: LargeModelConfig;
  onConfigChange: (newConfig: LargeModelConfig) => void;
}

const LargeModelCheckConfig: React.FC<LargeModelCheckConfigProps> = ({ config, onConfigChange }) => {
  const handleAddDimension = () => {
    const newDimension: Dimension = {
      id: Date.now(),
      name: '',
      definition: '',
    };
    onConfigChange({ ...config, dimensions: [...config.dimensions, newDimension] });
  };

  const handleRemoveDimension = (id: number) => {
    onConfigChange({ ...config, dimensions: config.dimensions.filter(d => d.id !== id) });
  };

  const handleDimensionChange = (id: number, field: 'name' | 'definition', value: string) => {
    const updatedDimensions = config.dimensions.map(d =>
      d.id === id ? { ...d, [field]: value } : d
    );
    onConfigChange({ ...config, dimensions: updatedDimensions });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
      className="space-y-8"
    >
      {/* General Info */}
      <div className="p-6 bg-white rounded-lg border border-gray-200">
        <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <FileText className="w-6 h-6 mr-2 text-blue-500" />
          基础信息
        </h3>
        <div className="space-y-4">
          <div>
            <label htmlFor="scenarioName" className="block text-sm font-medium text-gray-700 mb-1">
              场景名称
              <Tooltip text="服务质检的对话场景描述，例如：手机销售场景。不超过100字。">
                <HelpCircle className="w-4 h-4 inline-block ml-1 text-gray-400 cursor-pointer" />
              </Tooltip>
            </label>
            <input
              type="text"
              id="scenarioName"
              value={config.scenarioName}
              onChange={(e) => onConfigChange({ ...config, scenarioName: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              placeholder="例如：银行信用卡办理业务"
              maxLength={100}
            />
          </div>
          <div>
            <label htmlFor="backgroundKnowledge" className="block text-sm font-medium text-gray-700 mb-1">
              背景知识
              <Tooltip text="提供增强模型场景理解的背景知识，可以提升模型识别效果。例如：某产品必须说明的特定风险点。">
                <HelpCircle className="w-4 h-4 inline-block ml-1 text-gray-400 cursor-pointer" />
              </Tooltip>
            </label>
            <textarea
              id="backgroundKnowledge"
              value={config.backgroundKnowledge}
              onChange={(e) => onConfigChange({ ...config, backgroundKnowledge: e.target.value })}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              placeholder="例如：办理信用卡时，必须告知用户年费政策及免年费条件..."
            />
          </div>
        </div>
      </div>

      {/* Dimensions */}
      <div className="p-6 bg-white rounded-lg border border-gray-200">
        <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <BrainCircuit className="w-6 h-6 mr-2 text-purple-500" />
          检测维度配置
        </h3>
        <div className="space-y-4">
          {config.dimensions.map((dim, index) => (
            <motion.div
              key={dim.id}
              layout
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
              className="p-4 border border-gray-200 rounded-lg bg-gray-50"
            >
              <div className="flex justify-between items-center mb-2">
                <span className="text-md font-semibold text-gray-700">维度 {index + 1}</span>
                <button
                  onClick={() => handleRemoveDimension(dim.id)}
                  className="p-1 text-gray-400 hover:text-red-500 rounded-full hover:bg-red-100 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
              <div className="space-y-2">
                <input
                  type="text"
                  value={dim.name}
                  onChange={(e) => handleDimensionChange(dim.id, 'name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="维度名称，例如：主动确认客户身份"
                  maxLength={60}
                />
                <textarea
                  value={dim.definition}
                  onChange={(e) => handleDimensionChange(dim.id, 'definition', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="维度定义，即该维度是否命中的具体描述，例如：客服在对话开场需要与客户核对姓名或手机号。"
                  maxLength={400}
                />
              </div>
            </motion.div>
          ))}
        </div>
        <button
          onClick={handleAddDimension}
          className="mt-4 flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlusCircle className="w-5 h-5 mr-2" />
          添加检测维度
        </button>
      </div>
    </motion.div>
  );
};

export default LargeModelCheckConfig; 