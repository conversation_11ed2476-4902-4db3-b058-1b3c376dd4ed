import React from 'react';
import { EnergyCheckConfigState } from './EnergyCheckConfig';

export interface Sentence {
  role: 'agent' | 'customer' | 'unknown';
  content: string;
  energy: number;
}

export interface EnergyCheckResult {
  matched: boolean;
  details: string;
  matchedSentences: Sentence[];
  calculatedValue?: number;
}

interface EnergyCheckTesterProps {
  config: EnergyCheckConfigState;
  testText: string;
  setTestText: (text: string) => void;
  runTest: () => void;
  testResult: EnergyCheckResult | null;
  setTestResult: (result: EnergyCheckResult | null) => void;
}

/**
 * 能量检测实时测试组件
 * @param props
 * @constructor
 */
const EnergyCheckTester: React.FC<EnergyCheckTesterProps> = (props) => {
  const { testText, setTestText, runTest, testResult, setTestResult } = props;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">🧪 实时测试</h2>
        <button
          onClick={() => {
            setTestText(
              '[energy:3] 客户：喂？\n[energy:5] 客服：您好，这里是xx客服中心。\n[energy:8] 客户：我的天呐！你们这个产品怎么回事！\n[energy:9] 客户：太气人了！完全不能用！\n[energy:6] 客服：您别着急，慢慢说，我会尽力帮您解决。\n[energy:4] 客户：好吧，那我说一下情况...'
            );
            setTestResult(null);
          }}
          className="text-sm text-gray-500 hover:text-gray-700 underline"
        >
          重置示例
        </button>
      </div>

      <div className="space-y-6">
        <div className="space-y-3">
          <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">
            📝 测试文本 (格式: [energy:能量值] 角色：内容)
          </label>
          <textarea
            id="test-text"
            rows={10}
            className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 resize-none"
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            placeholder="请输入带有能量值的测试文本..."
          />
        </div>

        <div className="flex space-x-3">
          <button
            onClick={runTest}
            disabled={!testText.trim()}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 transition-all duration-200"
          >
            执行测试
          </button>
          {testResult && (
            <button
              onClick={() => setTestResult(null)}
              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
            >
              清除结果
            </button>
          )}
        </div>

        {testResult && (
          <div className="mt-8 space-y-6">
            <div
              className={`relative overflow-hidden rounded-xl border-2 p-6 ${
                testResult.matched
                  ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'
                  : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
              }`}
            >
              <div className="flex items-center space-x-4">
                <div
                  className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                    testResult.matched ? 'bg-green-100' : 'bg-red-100'
                  }`}
                >
                  <span className={`text-2xl ${testResult.matched ? 'text-green-600' : 'text-red-600'}`}>
                    {testResult.matched ? '✓' : '✗'}
                  </span>
                </div>
                <div className="flex-1">
                  <div className={`text-lg font-bold ${testResult.matched ? 'text-green-800' : 'text-red-800'}`}>
                    {testResult.matched ? '规则命中' : '规则未命中'}
                  </div>
                  <div className={`text-sm mt-1 ${testResult.matched ? 'text-green-700' : 'text-red-700'}`}>
                    {testResult.details}
                  </div>
                </div>
              </div>
            </div>

            {testResult.matchedSentences.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="font-semibold text-gray-800 mb-3">命中句子 ({testResult.matchedSentences.length}条)</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {testResult.matchedSentences.map((sentence, i) => (
                    <div key={i} className="p-3 bg-orange-50 border border-orange-100 rounded-md">
                      <div className="text-sm text-gray-800 font-medium">
                        <span className="font-bold text-orange-600">[能量: {sentence.energy}]</span> {sentence.content}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EnergyCheckTester; 