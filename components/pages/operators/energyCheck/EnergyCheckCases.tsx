import React from 'react';
import { EnergyCheckConfigState } from './EnergyCheckConfig';

interface Case {
  name: string;
  description: string;
  config: Partial<EnergyCheckConfigState>;
  testText: string;
  expectedResult: string;
}

interface EnergyCheckCasesProps {
  loadCase: (config: Partial<EnergyCheckConfigState>, text: string) => void;
}

/**
 * 能量检测案例库组件
 * @param loadCase
 * @constructor
 */
const EnergyCheckCases: React.FC<EnergyCheckCasesProps> = ({ loadCase }) => {
  const cases: Case[] = [
    {
      name: '客户情绪激动检测',
      description: '检测客户的语音能量是否过高（大于等于8）',
      config: {
        detectionRole: 'customer',
        detectionScope: 'full',
        checkMethod: 'range',
        rangeOperator: 'gte',
        rangeValue: 8,
      },
      testText:
        '[energy:3] 客户：喂？\n[energy:5] 客服：您好，很高兴为您服务。\n[energy:8] 客户：你们这个产品怎么回事！\n[energy:9] 客户：简直太气人了！',
      expectedResult: '应该命中（检测到客户能量为8和9的两句话）',
    },
    {
      name: '客服语气突然增强',
      description: '检测客服相邻两句话的能量波动是否过大（差值大于等于3）',
      config: {
        detectionRole: 'agent',
        detectionScope: 'full',
        checkMethod: 'fluctuation',
        fluctuationValue: 3,
      },
      testText:
        '[energy:4] 客服：您好，请问有什么可以帮您？\n[energy:5] 客户：我听不清！\n[energy:8] 客服：好的！我明白了！请您稍等！\n[energy:5] 客服：已经为您处理。',
      expectedResult: '应该命中（第二句客服的话能量8，比第一句能量4，差值超过3）',
    },
    {
      name: '通话情绪波动范围检测',
      description: '检测整通电话中所有角色的最大能量与最小能量差值（跨度大于等于5）',
      config: {
        detectionRole: 'all',
        detectionScope: 'full',
        checkMethod: 'span',
        spanValue: 5,
      },
      testText:
        '[energy:3] 客户：喂，你好。\n[energy:4] 客服：您好。\n[energy:9] 客户：我非常不满意！\n[energy:4] 客服：请您息怒。',
      expectedResult: '应该命中（最大能量9，最小能量3，跨度为6 >= 5）',
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">📂 案例库</h2>
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500 mt-1">{c.description}</p>
                <p className="text-xs text-blue-500 mt-1">预期结果：{c.expectedResult}</p>
              </div>
              <button
                onClick={() => loadCase(c.config, c.testText)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors flex-shrink-0"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EnergyCheckCases; 