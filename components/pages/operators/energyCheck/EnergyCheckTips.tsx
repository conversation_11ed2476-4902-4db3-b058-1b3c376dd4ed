import React from 'react';

/**
 * 能量检测使用提示组件
 * @constructor
 */
const EnergyCheckTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">使用提示</h2>
      </div>

      <div className="space-y-4">
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <h4 className="font-semibold text-gray-800 mb-2">数据格式说明</h4>
          <p className="text-xs text-gray-600">
            测试文本需要遵循特定格式：<code>[energy:能量值] 角色：对话内容</code>。
            能量值范围为1-10的整数。例如：<code>[energy:8] 客户：太气人了！</code>
          </p>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
          <h4 className="font-semibold text-gray-800 mb-2">配置技巧</h4>
          <ul className="list-disc list-inside text-xs text-gray-600 space-y-1">
            <li>
              <strong>能量范围检测</strong>：适用于识别极端情绪，如喊叫（高能量值）或耳语（低能量值）。
            </li>
            <li>
              <strong>相邻句能量波动</strong>：适合捕捉情绪的突然变化，例如从平静突然转为激动。
            </li>
            <li>
              <strong>最大能量跨度</strong>：用于评估整通对话的情绪起伏程度，跨度越大，说明情绪波动越剧烈。
            </li>
          </ul>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <h4 className="font-semibold text-gray-800 mb-2">业务场景建议</h4>
          <ul className="list-disc list-inside text-xs text-gray-600 space-y-1">
            <li>
              <strong>服务质量监控</strong>：使用"能量范围检测"监控客服是否存在语气过重（能量值过高）的情况。
            </li>
            <li>
              <strong>客户流失预警</strong>：通过"相邻句能量波动"和"最大能量跨度"识别出情绪极不稳定的客户，并进行标记。
            </li>
            <li>
              <strong>录音有效性检查</strong>：使用"能量范围检测"筛查出能量值持续过低的通话，可能为无效录音。
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default EnergyCheckTips; 