import React from 'react';
import { Tooltip } from '../../../common/Tooltip';

export type EnergyCheckConfigState = {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  checkMethod: 'range' | 'fluctuation' | 'span';
  rangeOperator: 'gt' | 'lt' | 'gte' | 'lte' | 'eq';
  rangeValue: number;
  fluctuationValue: number;
  spanValue: number;
};

interface EnergyCheckConfigProps {
  config: EnergyCheckConfigState;
  setConfig: React.Dispatch<React.SetStateAction<EnergyCheckConfigState>>;
}

/**
 * 能量检测配置组件
 * @param config
 * @param setConfig
 * @constructor
 */
const EnergyCheckConfig: React.FC<EnergyCheckConfigProps> = ({ config, setConfig }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">配置演示</h2>
      </div>

      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-24 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={config.detectionRole}
            onChange={(e) => setConfig({ ...config, detectionRole: e.target.value as any })}
          >
            <option value="agent">客服</option>
            <option value="customer">客户</option>
            <option value="all">所有角色</option>
          </select>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-24 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={config.detectionScope}
            onChange={(e) => setConfig({ ...config, detectionScope: e.target.value as any })}
          >
            <option value="full">全文</option>
            <option value="range">指定范围</option>
          </select>
        </div>

        {/* 指定范围配置 */}
        {config.detectionScope === 'range' && (
          <div className="flex items-center space-x-4">
            <label className="w-24 text-sm font-medium text-gray-700 text-right">范围</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">第</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={config.rangeStart}
                onChange={(e) => setConfig({ ...config, rangeStart: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">~</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={config.rangeEnd}
                onChange={(e) => setConfig({ ...config, rangeEnd: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">句</span>
            </div>
          </div>
        )}

        {/* 说明 */}
        <div className="flex items-center space-x-4">
          <label className="w-24 text-sm font-medium text-gray-700 text-right"></label>
          <p className="text-xs text-gray-500 bg-gray-100 p-2 rounded-md flex items-center">
            录音文件转写成文本后，每句话会有一个语音能量等级（1-10），该算子用于分析情绪波动。
            <Tooltip text="该检测类型仅适用于音频质检">
              <span className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50">
                ?
              </span>
            </Tooltip>
          </p>
        </div>

        {/* 检查方式 */}
        <div className="flex items-start space-x-4">
          <label className="w-24 text-sm font-medium text-gray-700 text-right pt-2">检查方式</label>
          <div className="flex-1">
            <select
              className="w-full max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={config.checkMethod}
              onChange={(e) => setConfig({ ...config, checkMethod: e.target.value as any })}
            >
              <option value="range">能量范围检测</option>
              <option value="fluctuation">相邻句能量波动</option>
              <option value="span">最大能量跨度</option>
            </select>
          </div>
        </div>

        {/* 检查逻辑 */}
        <div className="flex items-start space-x-4">
          <label className="w-24 text-sm font-medium text-gray-700 text-right pt-2">检查逻辑</label>
          <div className="flex-1 space-y-2">
            {config.checkMethod === 'range' && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-800">语音能量等级</span>
                <select
                  className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={config.rangeOperator}
                  onChange={(e) => setConfig({ ...config, rangeOperator: e.target.value as any })}
                >
                  <option value="gt">大于</option>
                  <option value="lt">小于</option>
                  <option value="gte">大于等于</option>
                  <option value="lte">小于等于</option>
                  <option value="eq">等于</option>
                </select>
                <input
                  type="number"
                  min="1"
                  max="10"
                  className="w-24 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={config.rangeValue}
                  onChange={(e) => setConfig({ ...config, rangeValue: parseInt(e.target.value) || 1 })}
                />
                <span className="text-xs text-gray-500">(可输入范围1-10)</span>
              </div>
            )}
            {config.checkMethod === 'fluctuation' && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-800">能量差大于或等于</span>
                <input
                  type="number"
                  min="1"
                  max="9"
                  className="w-24 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={config.fluctuationValue}
                  onChange={(e) => setConfig({ ...config, fluctuationValue: parseInt(e.target.value) || 1 })}
                />
                <span className="text-xs text-gray-500">(可输入范围1-9)</span>
              </div>
            )}
            {config.checkMethod === 'span' && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-800">能量差大于或等于</span>
                <input
                  type="number"
                  min="1"
                  max="9"
                  className="w-24 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={config.spanValue}
                  onChange={(e) => setConfig({ ...config, spanValue: parseInt(e.target.value) || 1 })}
                />
                <span className="text-xs text-gray-500">(可输入范围1-9)</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnergyCheckConfig;
