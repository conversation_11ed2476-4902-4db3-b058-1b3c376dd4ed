import React, { useState } from 'react';
import CallSilenceConcepts from './callSilenceCheck/CallSilenceConcepts';
import CallSilenceConfigComponent from './callSilenceCheck/CallSilenceConfig';
import CallSilenceTester from './callSilenceCheck/CallSilenceTester';
import CallSilenceCases from './callSilenceCheck/CallSilenceCases';
import CallSilenceTips from './callSilenceCheck/CallSilenceTips';

interface CallSilenceConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  silenceLogic: 'different' | 'any' | 'same';
  timeComparison: 'greater' | 'less' | 'between';
  timeValue: number;
  timeValueEnd: number;
}

interface SilenceSegment {
  startTime: number;
  endTime: number;
  duration: number;
  previousRole: string;
  nextRole: string;
  previousSentence: string;
  nextSentence: string;
}

interface TestResult {
  matched: boolean;
  silenceSegments: SilenceSegment[];
  matchedSegments: SilenceSegment[];
  details: string;
}

interface ParsedSentence {
  startTime: number;
  endTime: number;
  role: string;
  content: string;
  originalText: string;
}

/**
 * 通话静音检查详细演示页面
 * 提供通话静音检查算子的完整学习体验
 */
const CallSilenceCheckPage: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState<CallSilenceConfig>({
    detectionRole: 'agent',
    detectionScope: 'full',
    rangeStart: 1,
    rangeEnd: 5,
    silenceLogic: 'different',
    timeComparison: 'greater',
    timeValue: 3,
    timeValueEnd: 5
  });

  // 测试文本
  const [testText, setTestText] = useState(
    '[00:05-00:12] 客服：您好，欢迎来电咨询\n[00:13-00:18] 客户：我想了解产品价格\n[00:22-00:30] 客服：好的，我来为您详细介绍\n[00:31-00:35] 客户：明白了，谢谢'
  );

  // 测试结果
  const [testResult, setTestResult] = useState<TestResult | null>(null);

  // 解析时间字符串
  const parseTime = (timeStr: string): number => {
    if (timeStr.includes(':')) {
      const [minutes, seconds] = timeStr.split(':').map(Number);
      return minutes * 60 + seconds;
    }
    return Number(timeStr);
  };

  // 解析测试文本
  const parseTestText = (text: string): ParsedSentence[] => {
    const lines = text.split('\n').filter(line => line.trim());
    const sentences: ParsedSentence[] = [];

    for (const line of lines) {
      // 匹配格式: [开始时间-结束时间] 角色：内容
      const match = line.match(/^\[(.+?)-(.+?)\]\s*([^：:]+)[：:]\s*(.+)$/);
      if (match) {
        const [, startTimeStr, endTimeStr, role, content] = match;
        sentences.push({
          startTime: parseTime(startTimeStr),
          endTime: parseTime(endTimeStr),
          role: role.trim(),
          content: content.trim(),
          originalText: line
        });
      }
    }

    return sentences.sort((a, b) => a.startTime - b.startTime);
  };

  // 执行测试
  const runTest = () => {
    const sentences = parseTestText(testText);
    if (sentences.length < 2) {
      setTestResult({
        matched: false,
        silenceSegments: [],
        matchedSegments: [],
        details: '需要至少2句对话才能检测静音'
      });
      return;
    }

    // 根据检测角色和范围过滤句子
    let targetSentences = sentences;
    
    if (config.detectionScope === 'range') {
      targetSentences = sentences.slice(config.rangeStart - 1, config.rangeEnd);
    }

    // 计算静音段
    const silenceSegments: SilenceSegment[] = [];
    
    for (let i = 0; i < targetSentences.length - 1; i++) {
      const current = targetSentences[i];
      const next = targetSentences[i + 1];
      
      // 检查角色条件
      let shouldCheck = false;
      
      if (config.silenceLogic === 'different') {
        // 不同角色之间：前后句角色不同，且后句角色符合检测角色
        if (current.role !== next.role) {
          if (config.detectionRole === 'all' || 
              next.role === (config.detectionRole === 'agent' ? '客服' : '客户')) {
            shouldCheck = true;
          }
        }
      } else if (config.silenceLogic === 'any') {
        // 不区分角色：检测所有静音段
        if (config.detectionRole === 'all' ||
            next.role === (config.detectionRole === 'agent' ? '客服' : '客户')) {
          shouldCheck = true;
        }
      } else if (config.silenceLogic === 'same') {
        // 相同角色：前后句角色相同，且角色符合检测角色
        if (current.role === next.role) {
          if (config.detectionRole === 'all' ||
              next.role === (config.detectionRole === 'agent' ? '客服' : '客户')) {
            shouldCheck = true;
          }
        }
      }

      if (shouldCheck) {
        const silenceDuration = next.startTime - current.endTime;
        if (silenceDuration > 0) {
          silenceSegments.push({
            startTime: current.endTime,
            endTime: next.startTime,
            duration: silenceDuration,
            previousRole: current.role,
            nextRole: next.role,
            previousSentence: current.originalText,
            nextSentence: next.originalText
          });
        }
      }
    }

    // 根据时间条件筛选匹配的静音段
    const matchedSegments = silenceSegments.filter(segment => {
      switch (config.timeComparison) {
        case 'greater':
          return segment.duration > config.timeValue;
        case 'less':
          return segment.duration < config.timeValue;
        case 'between':
          return segment.duration >= config.timeValue && segment.duration <= config.timeValueEnd;
        default:
          return false;
      }
    });

    const matched = matchedSegments.length > 0;
    
    let details = '';
    if (matched) {
      details = `检测到 ${matchedSegments.length} 个符合条件的静音段`;
      if (config.timeComparison === 'between') {
        details += `（时长在 ${config.timeValue}-${config.timeValueEnd} 秒之间）`;
      } else {
        details += `（时长${config.timeComparison === 'greater' ? '大于' : '小于'} ${config.timeValue} 秒）`;
      }
    } else {
      details = `未检测到符合条件的静音段（共分析 ${silenceSegments.length} 个静音段）`;
    }

    setTestResult({
      matched,
      silenceSegments,
      matchedSegments,
      details
    });
  };

  // 加载案例
  const loadCase = (caseConfig: CallSilenceConfig, caseText: string) => {
    setConfig(caseConfig);
    setTestText(caseText);
    setTestResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                🔇 通话静音检查
              </h1>
              <p className="text-gray-600 mt-1">
                检测通话过程中是否出现异常静音，支持多种检测模式和时间条件
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                🎧 语音检查
              </span>
              <span className="text-sm text-gray-500">
                算子类型：语音检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            {/* 核心概念 */}
            <CallSilenceConcepts />

            {/* 配置演示 */}
            <CallSilenceConfigComponent config={config} setConfig={setConfig} />

            {/* 实时测试 */}
            <CallSilenceTester
              config={config}
              testText={testText}
              setTestText={setTestText}
              testResult={testResult}
              onTest={runTest}
              onClearResult={() => setTestResult(null)}
            />
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            {/* 案例库 */}
            <CallSilenceCases onLoadCase={loadCase} />
            
            {/* 使用提示 */}
            <CallSilenceTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>理解检测逻辑</strong>：掌握不同角色间、不区分角色、相同角色的区别</p>
                <p>• <strong>合理设置阈值</strong>：根据业务场景调整时间阈值，避免误报</p>
                <p>• <strong>分析实际场景</strong>：结合真实通话数据，优化检测参数</p>
                <p>• <strong>综合质检</strong>：与其他算子配合使用，提升质检效果</p>
                <p>• <strong>持续监控</strong>：定期分析检测结果，调整优化策略</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallSilenceCheckPage; 