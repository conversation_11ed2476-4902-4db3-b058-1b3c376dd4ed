import React, { useState } from 'react';
import RegexCheckConcepts from './regexCheck/RegexCheckConcepts';
import RegexCheckConfigComponent, { RegexCheckConfig } from './regexCheck/RegexCheckConfig';
import RegexCheckTester from './regexCheck/RegexCheckTester';
import RegexCheckTips from './regexCheck/RegexCheckTips';
import RegexCheckCases from './regexCheck/RegexCheckCases';

/**
 * 正则表达式检查测试结果接口
 */
interface RegexCheckTestResult {
  matched: boolean;
  matchedText: string[];
  matchedSentences: string[];
  excludedMatches: string[];
  details: string;
}

/**
 * 正则表达式检查详细演示页面
 * 提供正则表达式检查算子的完整学习体验
 */
const RegexCheckPage: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState<RegexCheckConfig>({
    detectionRole: 'all' as const,
    detectionScope: 'full' as const,
    rangeStart: 1,
    rangeEnd: 5,
    matchRegex: '1[3|4|5|7|8][0-9]{9}',
    excludeRegex: '13[0-9]{9}',
    singleSentence: false
  });

  // 测试文本
  const [testText, setTestText] = useState(
    '客服：您好，我的联系方式是13812345678。\n客户：好的，我的手机号是15987654321。\n客服：请问您的身份证号是多少？\n客户：我的身份证号是110101199001011234。'
  );

  // 测试结果
  const [testResult, setTestResult] = useState<RegexCheckTestResult | null>(null);

  // 执行测试
  const runTest = () => {
    const sentences = testText.split('\n').filter(s => s.trim());
    let targetSentences: string[] = [];

    // 根据检测角色筛选句子
    if (config.detectionRole === 'agent') {
      targetSentences = sentences.filter(s => s.startsWith('客服：'));
    } else if (config.detectionRole === 'customer') {
      targetSentences = sentences.filter(s => s.startsWith('客户：'));
    } else {
      targetSentences = sentences;
    }

    // 根据检测范围筛选
    if (config.detectionScope === 'range') {
      targetSentences = targetSentences.slice(config.rangeStart - 1, config.rangeEnd);
    }

    if (!config.matchRegex.trim()) {
      setTestResult({
        matched: false,
        matchedText: [],
        matchedSentences: [],
        excludedMatches: [],
        details: '请先配置命中正则表达式'
      });
      return;
    }

    // 验证正则表达式
    let matchRegex: RegExp;
    let excludeRegex: RegExp | null = null;

    try {
      matchRegex = new RegExp(config.matchRegex, 'gim');
    } catch (error) {
      setTestResult({
        matched: false,
        matchedText: [],
        matchedSentences: [],
        excludedMatches: [],
        details: '命中正则表达式语法错误'
      });
      return;
    }

    if (config.excludeRegex.trim()) {
      try {
        excludeRegex = new RegExp(config.excludeRegex, 'gim');
      } catch (error) {
        setTestResult({
          matched: false,
          matchedText: [],
          matchedSentences: [],
          excludedMatches: [],
          details: '排除正则表达式语法错误'
        });
        return;
      }
    }

    const matchedText: string[] = [];
    const matchedSentences: string[] = [];
    const excludedMatches: string[] = [];

    // 正则表达式匹配逻辑
    if (config.singleSentence) {
      // 单句话内生效：按标点分割后匹配
      targetSentences.forEach(sentence => {
        // 使用更完整的中英文标点符号进行分割
        const subSentences = sentence.split(/[，。！？；：,.!?;:""'']/);
        subSentences.forEach(subSentence => {
          const trimmedSub = subSentence.trim();
          if (trimmedSub) {
            // 为避免全局匹配的lastIndex问题，创建新的RegExp对象
            const newMatchRegex = new RegExp(config.matchRegex, 'gim');
            const matches = trimmedSub.match(newMatchRegex);
            if (matches) {
              matches.forEach(match => {
                // 检查是否需要排除
                if (excludeRegex) {
                  const newExcludeRegex = new RegExp(config.excludeRegex, 'gim');
                  if (newExcludeRegex.test(match)) {
                    excludedMatches.push(match);
                  } else {
                    matchedText.push(match);
                    if (!matchedSentences.includes(sentence)) {
                      matchedSentences.push(sentence);
                    }
                  }
                } else {
                  matchedText.push(match);
                  if (!matchedSentences.includes(sentence)) {
                    matchedSentences.push(sentence);
                  }
                }
              });
            }
          }
        });
      });
    } else {
      // 整句检测
      targetSentences.forEach(sentence => {
        // 为避免全局匹配的lastIndex问题，创建新的RegExp对象
        const newMatchRegex = new RegExp(config.matchRegex, 'gim');
        const matches = sentence.match(newMatchRegex);
        if (matches) {
          matches.forEach(match => {
            // 检查是否需要排除
            if (excludeRegex) {
              const newExcludeRegex = new RegExp(config.excludeRegex, 'gim');
              if (newExcludeRegex.test(match)) {
                excludedMatches.push(match);
              } else {
                matchedText.push(match);
                if (!matchedSentences.includes(sentence)) {
                  matchedSentences.push(sentence);
                }
              }
            } else {
              matchedText.push(match);
              if (!matchedSentences.includes(sentence)) {
                matchedSentences.push(sentence);
              }
            }
          });
        }
      });
    }

    // 判断是否命中
    const matched = matchedText.length > 0;

    let details = '';
    if (matched) {
      details = `规则已命中 - 找到 ${matchedText.length} 个匹配项`;
      if (excludedMatches.length > 0) {
        details += `，排除 ${excludedMatches.length} 个项目`;
      }
    } else {
      details = '规则未命中 - 未找到匹配的内容';
      if (excludedMatches.length > 0) {
        details += `（${excludedMatches.length} 个项目被排除）`;
      }
    }

    setTestResult({
      matched,
      matchedText,
      matchedSentences,
      excludedMatches,
      details
    });
  };

  // 加载案例
  const loadCase = (caseConfig: RegexCheckConfig, caseText: string) => {
    setConfig(caseConfig);
    setTestText(caseText);
    setTestResult(null);
  };

  // 清除测试结果
  const clearResult = () => {
    setTestResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                🔍 正则表达式检查
              </h1>
              <p className="text-gray-600 mt-1">
                检测文本内容是否符合正则表达式配置的规则内容，支持复杂模式匹配和排除规则
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                🎯 进阶应用
              </span>
              <span className="text-sm text-gray-500">
                算子类型：文字检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            {/* 核心概念 */}
            <RegexCheckConcepts />

            {/* 配置演示 */}
            <RegexCheckConfigComponent 
              config={config}
              onConfigChange={setConfig}
            />

            {/* 实时测试 */}
            <RegexCheckTester
              config={config}
              testText={testText}
              onTestTextChange={setTestText}
              testResult={testResult}
              onRunTest={runTest}
              onClearResult={clearResult}
            />
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            {/* 案例库 */}
            <RegexCheckCases onLoadCase={loadCase} />
            
            {/* 使用提示 */}
            <RegexCheckTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>从简单开始</strong>：先使用基础的字符匹配，逐步学习元字符和量词</p>
                <p>• <strong>理解匹配逻辑</strong>：掌握命中规则和排除规则的组合使用</p>
                <p>• <strong>学习常用模式</strong>：熟练掌握手机号、身份证号等常见格式的正则表达式</p>
                <p>• <strong>善用测试工具</strong>：通过案例库和实时测试验证正则表达式的正确性</p>
                <p>• <strong>优化性能</strong>：避免过于复杂的正则表达式，保持良好的执行效率</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegexCheckPage; 