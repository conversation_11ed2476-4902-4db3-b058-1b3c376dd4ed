import React, { useState } from 'react';
import { PageHeader } from '../../PageHeader';
import { CustomerModelConcepts } from './customerModel/CustomerModelConcepts';
import { CustomerModelConfig } from './customerModel/CustomerModelConfig';
import { CustomerModelTester } from './customerModel/CustomerModelTester';
import { CustomerModelCases } from './customerModel/CustomerModelCases';
import { CustomerModelTips } from './customerModel/CustomerModelTips';

export const detectionTypes = [
  '投诉', '升级问题', '质疑服务', '制造舆情', 
  '表扬', '辱骂', '愤怒'
] as const;

export type DetectionType = typeof detectionTypes[number];

/**
 * @typedef {object} CustomerModelConfigType
 * @property {'customer'} detectionRole
 * @property {'full'} detectionScope
 * @property {DetectionType | ''} detectionType
 */
export interface CustomerModelConfigType {
  detectionRole: 'customer';
  detectionScope: 'full';
  detectionType: DetectionType | '';
}

/**
 * @typedef {object} TestResultType
 * @property {boolean} matched
 * @property {DetectionType | null} matchedType
 * @property {string} details
 */
export interface TestResultType {
  matched: boolean;
  matchedType: DetectionType | null;
  details: string;
}

/**
 * 客户模型检测页面
 * @returns {React.ReactElement}
 */
export const CustomerModelPage: React.FC = () => {
  const [config, setConfig] = useState<CustomerModelConfigType>({
    detectionRole: 'customer',
    detectionScope: 'full',
    detectionType: '',
  });

  const [testText, setTestText] = useState(
    '客户：我对你们的服务非常不满意，我要投诉！\n客服：您好，请问发生了什么事？'
  );

  const [testResult, setTestResult] = useState<TestResultType | null>(null);

  const pageInfo = {
    title: '🤖 客户模型检测',
    description: '检测客户常见的异常情绪或意图，由系统内置的算法模型进行分析。',
    tag: '模型检查',
    badge: '高级功能',
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader {...pageInfo} />

      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          <div className="w-[60%] space-y-6">
            <CustomerModelConcepts />
            <CustomerModelConfig config={config} setConfig={setConfig} />
            <CustomerModelTester
              config={config}
              testText={testText}
              setTestText={setTestText}
              testResult={testResult}
              setTestResult={setTestResult}
            />
          </div>

          <div className="w-[40%] space-y-6">
            <CustomerModelCases setConfig={setConfig} setTestText={setTestText} />
            <CustomerModelTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2 text-sm">
                <p>• <strong>熟悉情绪分类</strong>：了解"投诉"、"表扬"、"愤怒"等每种客户情绪的典型表达方式。</p>
                <p>• <strong>利用案例库</strong>：加载不同案例，观察模型如何区分"升级问题"和"制造舆情"等相似意图。</p>
                <p>• <strong>结合实际应用</strong>：将此算子作为高风险对话的触发器，用于后续的告警或人工干预流程。</p>
                <p>• <strong>思考正向激励</strong>：探索如何利用"表扬"模型的数据，自动发现优秀服务案例，用于客服的绩效考核和正向激励。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 