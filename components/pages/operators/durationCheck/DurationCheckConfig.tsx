import React from 'react';

/**
 * 录音时长检测配置接口
 */
interface DurationCheckConfigProps {
  config: {
    operator: '>' | '<' | '>=' | '<=' | '=';
    duration: number;
  };
  setConfig: React.Dispatch<React.SetStateAction<any>>;
}

/**
 * 录音时长检测配置组件
 * @param config 当前配置
 * @param setConfig 更新配置的函数
 * @constructor
 */
const DurationCheckConfig: React.FC<DurationCheckConfigProps> = ({ config, setConfig }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">配置演示</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检查逻辑</label>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">整通对话时长</span>
            <select
              className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={config.operator}
              onChange={(e) => setConfig({ ...config, operator: e.target.value as any })}
            >
              <option value=">">大于</option>
              <option value="<">小于</option>
              <option value=">=">大于等于</option>
              <option value="<=">小于等于</option>
              <option value="=">等于</option>
            </select>
            <input
              type="number"
              min="0"
              className="w-24 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={config.duration}
              onChange={(e) => setConfig({ ...config, duration: parseInt(e.target.value) || 0 })}
            />
            <span className="text-sm text-gray-700">秒</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DurationCheckConfig; 