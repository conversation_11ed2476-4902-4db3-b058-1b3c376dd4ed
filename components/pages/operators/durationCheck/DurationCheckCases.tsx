import React from 'react';

/**
 * 录音时长检测案例接口
 */
interface DurationCheckCasesProps {
  loadCase: (config: any, testText: string) => void;
}

/**
 * 录音时长检测预设案例组件
 * @constructor
 */
const DurationCheckCases: React.FC<DurationCheckCasesProps> = ({ loadCase }) => {
  const cases = [
    {
      name: '检测超长通话',
      description: '检测通话时长是否超过10分钟（600秒）',
      config: {
        operator: '>' as const,
        duration: 600,
      },
      testText: '[00:05] 客户：你好。\n[10:01] 客服：好的，我们已经处理完成。',
      expectedResult: '应该命中'
    },
    {
      name: '过滤无效短通话',
      description: '检测通话时长是否小于30秒',
      config: {
        operator: '<' as const,
        duration: 30,
      },
      testText: '[00:03] 客服：您好。\n[00:15] 客户：打错了，不好意思。',
      expectedResult: '应该命中'
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">📂 案例库</h2>
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500 mt-1">{c.description}</p>
                <p className="text-xs text-blue-500 mt-1">预期结果：{c.expectedResult}</p>
              </div>
              <button
                onClick={() => loadCase(c.config, c.testText)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 flex-shrink-0 ml-4"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DurationCheckCases; 