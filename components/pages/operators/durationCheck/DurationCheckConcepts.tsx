import React from 'react';

/**
 * 录音时长检测核心概念组件
 * @constructor
 */
const DurationCheckConcepts: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">核心概念</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          基础知识
        </span>
      </div>

      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">🎯</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">功能介绍</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                通过分析对话文本中的时间戳 <code>[mm:ss]</code>，来检测通话录音的总时长，或特定角色（客服/客户）的发言时长是否满足设定的条件。这是评估通话效率和完整性的基础算子。
              </p>
            </div>
          </div>
        </div>

        {/* 应用场景 */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-lg">🏢</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">应用场景</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">过滤无效的短通话</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">识别超长通话以便复盘</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">监控通话时长是否达标</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">分析不同业务的平均通话时长</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 优势特点 */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-lg">⭐</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">优势特点</h3>
              <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
                <li><strong>简单高效</strong>：配置直观，判断迅速，是通话初筛的利器。</li>
                <li><strong>范围灵活</strong>：支持设置时长上限、下限，或两者结合的区间。</li>
                <li><strong>适用性广</strong>：可用于外呼、呼入等多种业务场景的通话筛选。</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 检测原理 */}
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-lg">🔧</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">检测原理</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                算子直接获取录音文件的元数据（Metadata）中的总时长信息，然后将其与配置中设定的时长范围（大于、小于、介于）进行比较。如果实际时长落在设定的异常区间内，则规则命中。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DurationCheckConcepts;
