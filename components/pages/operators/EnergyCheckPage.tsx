import React, { useState } from 'react';
import { UnifiedPageHeader } from '../final-design/components/UnifiedPageHeader';
import { Zap } from 'lucide-react';
import EnergyCheckConcepts from './energyCheck/EnergyCheckConcepts';
import EnergyCheckConfig, { EnergyCheckConfigState } from './energyCheck/EnergyCheckConfig';
import EnergyCheckTester, { EnergyCheckResult, Sentence } from './energyCheck/EnergyCheckTester';
import EnergyCheckCases from './energyCheck/EnergyCheckCases';
import EnergyCheckTips from './energyCheck/EnergyCheckTips';

/**
 * 解析带有能量信息的单行文本
 * @param line - eg: "[energy:5] 客服：你好"
 */
const parseLine = (line: string): Sentence | null => {
  const match = line.match(/\[energy:(\d+)]\s*(客服|客户)：(.*)/);
  if (!match) return null;

  return {
    energy: parseInt(match[1], 10),
    role: match[2] === '客服' ? 'agent' : 'customer',
    content: match[3].trim(),
  };
};

/**
 * 能量检测页面
 * @constructor
 */
const EnergyCheckPage: React.FC = () => {
  const [config, setConfig] = useState<EnergyCheckConfigState>({
    detectionRole: 'customer',
    detectionScope: 'full',
    rangeStart: 1,
    rangeEnd: 3,
    checkMethod: 'range',
    rangeOperator: 'gte',
    rangeValue: 8,
    fluctuationValue: 3,
    spanValue: 5,
  });

  const [testText, setTestText] = useState(
    '[energy:3] 客户：喂？\n[energy:5] 客服：您好，这里是xx客服中心。\n[energy:8] 客户：我的天呐！你们这个产品怎么回事！\n[energy:9] 客户：太气人了！完全不能用！\n[energy:6] 客服：您别着急，慢慢说，我会尽力帮您解决。\n[energy:4] 客户：好吧，那我说一下情况...'
  );

  const [testResult, setTestResult] = useState<EnergyCheckResult | null>(null);

  const runTest = () => {
    const allSentences = testText.split('\n').map(parseLine).filter((s): s is Sentence => s !== null);

    let targetSentences = allSentences;
    if (config.detectionRole !== 'all') {
      targetSentences = allSentences.filter(s => s.role === config.detectionRole);
    }

    if (config.detectionScope === 'range') {
      targetSentences = targetSentences.slice(config.rangeStart - 1, config.rangeEnd);
    }
    
    if(targetSentences.length === 0){
        setTestResult({ matched: false, details: '在检测范围和角色下未找到任何句子。', matchedSentences: [] });
        return;
    }

    let matched = false;
    let details = '';
    const matchedSentences: Sentence[] = [];
    let calculatedValue: number | undefined = undefined;

    switch (config.checkMethod) {
      case 'range': {
        const operatorMap = { gt: '>', lt: '<', gte: '>=', lte: '<=', eq: '==' };
        targetSentences.forEach(s => {
          // eslint-disable-next-line no-eval
          const condition = eval(`${s.energy} ${operatorMap[config.rangeOperator]} ${config.rangeValue}`);
          if (condition) {
            matchedSentences.push(s);
          }
        });
        matched = matchedSentences.length > 0;
        details = `检测到 ${matchedSentences.length} 句能量等级 ${operatorMap[config.rangeOperator]} ${config.rangeValue} 的句子。`;
        break;
      }
      case 'fluctuation': {
        for (let i = 1; i < targetSentences.length; i++) {
          const diff = Math.abs(targetSentences[i].energy - targetSentences[i - 1].energy);
          if (diff >= config.fluctuationValue) {
            // 将导致波动的两句话都加入
            if(!matchedSentences.includes(targetSentences[i-1])) matchedSentences.push(targetSentences[i-1]);
            if(!matchedSentences.includes(targetSentences[i])) matchedSentences.push(targetSentences[i]);
          }
        }
        matched = matchedSentences.length > 0;
        details = `检测到 ${matchedSentences.length > 0 ? matchedSentences.length / 2 : 0} 处相邻句能量波动 >= ${config.fluctuationValue}。`;
        break;
      }
      case 'span': {
        if (targetSentences.length > 1) {
            const energies = targetSentences.map(s => s.energy);
            const maxEnergy = Math.max(...energies);
            const minEnergy = Math.min(...energies);
            calculatedValue = maxEnergy - minEnergy;
            if (calculatedValue >= config.spanValue) {
              matched = true;
              // 找到能量最高和最低的句子
              const maxSentence = targetSentences.find(s => s.energy === maxEnergy);
              const minSentence = targetSentences.find(s => s.energy === minEnergy);
              if(maxSentence) matchedSentences.push(maxSentence);
              if(minSentence && minSentence !== maxSentence) matchedSentences.push(minSentence);
            }
        }
        details = `最大能量跨度为 ${calculatedValue || 0} (需要 >= ${config.spanValue})。`;
        break;
      }
    }
    
    setTestResult({ matched, details, matchedSentences, calculatedValue });
  };
  
  const loadCase = (caseConfig: Partial<EnergyCheckConfigState>, caseText: string) => {
    setConfig(prev => ({ ...prev, ...caseConfig }));
    setTestText(caseText);
    setTestResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedPageHeader
        title="能量检测"
        subtitle="通过分析语音能量等级，判断通话过程中的情绪波动。"
        icon={Zap}
        badge={{ text: "高级", color: "yellow" }}
      />
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          <div className="w-[60%] space-y-6">
            <EnergyCheckConcepts />
            <EnergyCheckConfig config={config} setConfig={setConfig} />
            <EnergyCheckTester 
              config={config}
              testText={testText}
              setTestText={setTestText}
              runTest={runTest}
              testResult={testResult}
              setTestResult={setTestResult}
            />
          </div>
          <div className="w-[40%] space-y-6">
            <EnergyCheckCases loadCase={loadCase} />
            <EnergyCheckTips />
          </div>
        </div>
        {/* 学习建议 */}
        <div className="mt-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">💡 学习建议</h3>
                <div className="text-blue-800 space-y-2 text-sm">
                    <p>• <strong>理解数据格式</strong>：首先掌握 <code>[energy:N] 角色: 内容</code> 的格式，这是正确测试的基础。</p>
                    <p>• <strong>逐一测试检测方式</strong>：分别加载三种检测方式（范围、波动、跨度）的案例，理解其不同应用场景。</p>
                    <p>• <strong>调整参数看变化</strong>：在案例基础上，尝试修改能量值、波动阈值等参数，观察测试结果的变化，加深理解。</p>
                    <p>• <strong>结合实际场景</strong>：思考在你的业务中，什么样的能量指标是有价值的，并尝试创建自己的质检规则。</p>
                </div>
            </div>
        </div>
      </div>
    </div>
  );
};

export default EnergyCheckPage;
