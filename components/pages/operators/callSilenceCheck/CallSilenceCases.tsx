import React from 'react';

interface CallSilenceConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  silenceLogic: 'different' | 'any' | 'same';
  timeComparison: 'greater' | 'less' | 'between';
  timeValue: number;
  timeValueEnd: number;
}

interface CallSilenceCasesProps {
  onLoadCase: (config: CallSilenceConfig, testText: string) => void;
}

/**
 * 通话静音检查案例库组件
 * 提供预设的测试案例
 */
const CallSilenceCases: React.FC<CallSilenceCasesProps> = ({ onLoadCase }) => {
  // 预设案例
  const cases = [
    {
      name: '客服响应延迟检测',
      description: '检测客服是否在客户说话后出现超过3秒的响应延迟',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        silenceLogic: 'different' as const,
        timeComparison: 'greater' as const,
        timeValue: 3,
        timeValueEnd: 0
      },
      testText: '[00:05-00:12] 客户：你好，我想咨询一下产品信息\n[00:17-00:25] 客服：您好，请稍等，我来为您查询\n[00:28-00:35] 客户：好的，谢谢\n[00:36-00:45] 客服：经过查询，我们有以下几种产品',
      expectedResult: '应该命中（客户说话后，客服响应延迟了5秒）'
    },
    {
      name: '通话质量检测',
      description: '检测整个通话过程中是否有超过2秒的异常静音',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        silenceLogic: 'any' as const,
        timeComparison: 'greater' as const,
        timeValue: 2,
        timeValueEnd: 0
      },
      testText: '[00:05-00:10] 客服：您好，欢迎来电咨询\n[00:11-00:18] 客户：我想了解你们的服务\n[00:22-00:30] 客服：好的，我来为您详细介绍\n[00:31-00:35] 客户：谢谢',
      expectedResult: '应该命中（第二句和第三句之间有4秒静音）'
    },
    {
      name: '客服专业度评估',
      description: '检测客服同一角色连续说话时是否有过长停顿',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        silenceLogic: 'same' as const,
        timeComparison: 'greater' as const,
        timeValue: 2,
        timeValueEnd: 0
      },
      testText: '[00:05-00:12] 客服：您好，欢迎来电\n[00:16-00:25] 客服：请问您需要什么帮助\n[00:26-00:30] 客户：我想咨询产品\n[00:31-00:40] 客服：好的，我来介绍',
      expectedResult: '应该命中（客服连续说话之间有4秒停顿）'
    },
    {
      name: '快速响应检测',
      description: '检测客服响应时间是否在合理范围内（1-3秒）',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'range' as const,
        rangeStart: 1,
        rangeEnd: 3,
        silenceLogic: 'different' as const,
        timeComparison: 'between' as const,
        timeValue: 1,
        timeValueEnd: 3
      },
      testText: '[00:05-00:10] 客户：请问有优惠活动吗\n[00:12-00:20] 客服：有的，目前我们有折扣活动\n[00:21-00:25] 客户：具体是什么折扣\n[00:27-00:35] 客服：新客户可享受8折优惠',
      expectedResult: '应该命中（客服响应时间都在1-3秒范围内）'
    },
    {
      name: '系统故障识别',
      description: '检测是否存在异常长时间的静音（可能是系统问题）',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        silenceLogic: 'any' as const,
        timeComparison: 'greater' as const,
        timeValue: 10,
        timeValueEnd: 0
      },
      testText: '[00:05-00:10] 客服：您好，请问有什么可以帮您\n[00:11-00:15] 客户：我想查询订单状态\n[00:30-00:38] 客服：好的，请您稍等，系统正在查询\n[00:39-00:42] 客户：好的',
      expectedResult: '应该命中（中间有15秒的异常长静音）'
    },
    {
      name: '正常通话流程',
      description: '检测正常通话中不应触发静音警告',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        silenceLogic: 'any' as const,
        timeComparison: 'greater' as const,
        timeValue: 5,
        timeValueEnd: 0
      },
      testText: '[00:05-00:12] 客服：您好，欢迎来电咨询\n[00:13-00:18] 客户：我想了解产品价格\n[00:19-00:28] 客服：好的，我们的产品价格是\n[00:29-00:32] 客户：明白了，谢谢',
      expectedResult: '不应该命中（所有静音都在正常范围内）'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        📂 案例库
      </h2>
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div className="flex-1 pr-4">
                <h4 className="font-semibold text-gray-800 mb-2">{c.name}</h4>
                <p className="text-xs text-gray-600 mb-2">{c.description}</p>
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xs text-blue-600 font-medium">配置：</span>
                  <span className="text-xs text-gray-500">
                    {(() => {
                      const detectionRole = c.config.detectionRole as 'agent' | 'customer' | 'all';
                      const silenceLogic = c.config.silenceLogic as 'different' | 'any' | 'same';
                      const timeComparison = c.config.timeComparison as 'greater' | 'less' | 'between';
                      
                      const role = detectionRole === 'agent' ? '客服' : detectionRole === 'customer' ? '客户' : '所有角色';
                      const logic = silenceLogic === 'different' ? '不同角色间' : silenceLogic === 'any' ? '不区分角色' : '相同角色';
                      const comparison = timeComparison === 'greater' ? '大于' : timeComparison === 'less' ? '小于' : '区间';
                      const timeDisplay = timeComparison === 'between' ? ` ${c.config.timeValue}-${c.config.timeValueEnd}秒` : ` ${c.config.timeValue}秒`;
                      return `${role} | ${logic} | ${comparison}${timeDisplay}`;
                    })()}
                  </span>
                </div>
                <p className="text-xs text-green-600 font-medium">预期结果：{c.expectedResult}</p>
              </div>
              <button 
                onClick={() => onLoadCase(c.config, c.testText)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors flex-shrink-0"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CallSilenceCases; 