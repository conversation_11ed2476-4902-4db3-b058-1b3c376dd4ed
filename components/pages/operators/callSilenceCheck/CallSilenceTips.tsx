import React from 'react';

/**
 * 通话静音检查使用提示组件
 * 提供使用技巧和最佳实践建议
 */
const CallSilenceTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          使用提示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        {/* 时间格式提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⏰</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">时间格式</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">标准格式</span>
                    <span className="text-xs text-gray-500 block">[00:05-00:12] 客服：内容（分:秒格式）</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">简化格式</span>
                    <span className="text-xs text-gray-500 block">[5-12] 客服：内容（纯秒数格式）</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 检测逻辑说明 */}
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-sm">🔧</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">检测逻辑详解</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">不同角色之间</span>
                    <span className="text-xs text-gray-500 block">检测角色切换时的响应延迟，如客户问话后客服回应的时间</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">不区分角色</span>
                    <span className="text-xs text-gray-500 block">检测任意位置的静音，用于整体通话质量评估</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">相同角色</span>
                    <span className="text-xs text-gray-500 block">检测同一人连续说话的停顿，评估表达流畅度</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 配置建议 */}
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">配置建议</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">响应速度检测</span>
                    <span className="text-xs text-gray-500 block">设置客服检测，不同角色间，大于3秒</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">通话质量监控</span>
                    <span className="text-xs text-gray-500 block">设置所有角色，不区分角色，大于5秒</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 应用场景 */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-sm">🚀</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">典型应用场景</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded text-xs">客服质检</span>
                    <span className="text-sm text-gray-700">响应速度监控</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded text-xs">通话质量</span>
                    <span className="text-sm text-gray-700">异常静音检测</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded text-xs">系统监控</span>
                    <span className="text-sm text-gray-700">技术故障识别</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded text-xs">培训评估</span>
                    <span className="text-sm text-gray-700">专业度分析</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 注意事项 */}
        <div className="bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg p-4 border border-amber-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
              <span className="text-amber-600 text-sm">⚠️</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">注意事项</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">时间精度</span>
                    <span className="text-xs text-gray-500 block">建议使用0.1秒精度，避免过于严格的检测</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">阈值设置</span>
                    <span className="text-xs text-gray-500 block">根据业务场景调整时间阈值，避免误报</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">数据质量</span>
                    <span className="text-xs text-gray-500 block">确保时间戳准确，否则会影响检测结果</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 快速开始 */}
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
              <span className="text-indigo-600 text-sm">🎓</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">快速开始</h4>
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤1</span>
                <span>选择案例</span>
                <span>→</span>
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤2</span>
                <span>执行测试</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallSilenceTips; 