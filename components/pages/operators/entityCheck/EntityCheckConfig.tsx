import React from 'react';

/**
 * 实体条件接口
 */
export interface EntityCondition {
  /** 实体类型 */
  entityType: 'date' | 'time' | 'province' | 'city' | 'district' | 'idCard' | 'personName' | 'email' | '';
  /** 逻辑运算符 */
  operator: 'equals' | 'notEquals' | 'contains' | 'notContains' | '';
  /** 比较字段（随路参数字段名） */
  expectedValue: string;
  /** 字段类型 */
  fieldType: 'system' | 'custom' | '';
}

/**
 * 信息实体检查配置接口
 */
export interface EntityCheckConfig {
  /** 检测角色 */
  detectionRole: 'agent' | 'customer' | 'all';
  /** 检测范围 */
  detectionScope: 'full' | 'range';
  /** 范围开始 */
  rangeStart: number;
  /** 范围结束 */
  rangeEnd: number;
  /** 实体条件列表 */
  entityConditions: EntityCondition[];
}

/**
 * 实体类型选项
 */
const ENTITY_TYPES = [
  { value: 'date', label: '日期', description: '今天、2018年3月4号、周三、2018-03-05等' },
  { value: 'time', label: '时间', description: '3点、3点一刻、18点等' },
  { value: 'province', label: '省份', description: '北京、山东等省级行政区' },
  { value: 'city', label: '城市', description: '北京、济南等地级行政区' },
  { value: 'district', label: '区县', description: '朝阳区等县级行政区' },
  { value: 'idCard', label: '身份证号码', description: '身份证号码' },
  { value: 'personName', label: '人名', description: '人名实体，比如张三、李四等' },
  { value: 'email', label: '邮箱', description: '邮箱地址' }
] as const;

/**
 * 逻辑运算符选项
 */
const OPERATORS = [
  { value: 'equals', label: '=', description: '完全匹配' },
  { value: 'notEquals', label: '!=', description: '不匹配' },
  { value: 'contains', label: '包含', description: '部分匹配' },
  { value: 'notContains', label: '不包含', description: '不存在部分匹配' }
] as const;

interface Props {
  config: EntityCheckConfig;
  onChange: (config: EntityCheckConfig) => void;
}

/**
 * 信息实体检查配置组件
 */
const EntityCheckConfig: React.FC<Props> = ({ config, onChange }) => {
  /**
   * 更新实体条件（只有一个固定条件）
   */
  const updateEntityCondition = (updates: Partial<EntityCondition>) => {
    const currentCondition = config.entityConditions[0] || {
      entityType: '' as any,
      operator: '' as any,
      expectedValue: '',
      fieldType: 'system'
    };
    onChange({
      ...config,
      entityConditions: [{ ...currentCondition, ...updates }]
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">配置演示</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>
      
      {/* 配置规则说明 */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-lg">📋</span>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 mb-3">配置规则总结</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🎯 检测角色</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>客服</strong>：仅检测"客服："开头的对话</li>
                    <li>• <strong>客户</strong>：仅检测"客户："开头的对话</li>
                    <li>• <strong>所有角色</strong>：检测全部对话内容</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">📍 检测范围</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>全文</strong>：检测所有符合角色条件的句子</li>
                    <li>• <strong>指定范围</strong>：只检测第X~Y句之间的内容</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🏷️ 实体识别</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• 在对话中自动识别指定类型的实体</li>
                    <li>• 支持8种实体：日期、时间、人名、地址等</li>
                  </ul>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">📋 字段选择</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>系统常规字段</strong>：客服姓名、技能组等预设字段</li>
                    <li>• <strong>自定义字段</strong>：业务特有的随路参数</li>
                    <li>• <strong>必须选择</strong>：不能留空，作为比较的基准值</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">⚡ 比较验证</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>=</strong>：实体值与字段值完全一致</li>
                    <li>• <strong>!=</strong>：实体值与字段值不一致</li>
                    <li>• <strong>包含</strong>：实体值包含字段值</li>
                    <li>• <strong>不包含</strong>：实体值不包含字段值</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionRole}
            onChange={(e) => onChange({ ...config, detectionRole: e.target.value as any })}
          >
            <option value="agent">客服</option>
            <option value="customer">客户</option>
            <option value="all">所有角色</option>
          </select>
        </div>

        {/* 前置条件 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">前置条件</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="none">无</option>
          </select>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionScope}
            onChange={(e) => onChange({ ...config, detectionScope: e.target.value as any })}
          >
            <option value="full">全文</option>
            <option value="range">指定范围</option>
          </select>
        </div>

        {/* 指定范围配置 */}
        {config.detectionScope === 'range' && (
          <div className="flex items-center space-x-4">
            <label className="w-20 text-sm font-medium text-gray-700 text-right">范围</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">第</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeStart}
                onChange={(e) => onChange({ ...config, rangeStart: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">~</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeEnd}
                onChange={(e) => onChange({ ...config, rangeEnd: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">句</span>
              <span 
                className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                title="指定检测的句子范围，例如第1~3句表示只检测前3句对话"
              >
                ?
              </span>
            </div>
          </div>
        )}

        {/* 实体选择 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">实体选择</label>
          <div className="flex items-center space-x-3 flex-1">
            {/* 实体类型选择 */}
            <select
              className="w-32 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={config.entityConditions[0]?.entityType || ''}
              onChange={(e) => updateEntityCondition({ entityType: e.target.value as any })}
            >
              <option value="">请选择实体</option>
              {ENTITY_TYPES.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>

            {/* 逻辑运算符 */}
            <select
              className="w-24 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={config.entityConditions[0]?.operator || ''}
              onChange={(e) => updateEntityCondition({ operator: e.target.value as any })}
            >
              <option value="">请选择运算符</option>
              {OPERATORS.map(op => (
                <option key={op.value} value={op.value}>{op.label}</option>
              ))}
            </select>

            {/* 字段类型选择 */}
            <select
              className="w-40 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={config.entityConditions[0]?.fieldType || ''}
              onChange={(e) => updateEntityCondition({ fieldType: e.target.value as any, expectedValue: '' })}
            >
              <option value="">请选择字段类型</option>
              <option value="system">系统常规字段</option>
              <option value="custom">更多自定义字段</option>
            </select>

            {/* 字段选择区域 */}
            {config.entityConditions[0]?.fieldType === 'system' ? (
              <select
                className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.entityConditions[0]?.expectedValue || ''}
                onChange={(e) => updateEntityCondition({ expectedValue: e.target.value })}
              >
                <option value="">请选择...</option>
                <option value="客服姓名">客服姓名</option>
                <option value="客服ID">客服ID</option>
                <option value="技能组名称">技能组名称</option>
                <option value="呼叫类型">呼叫类型</option>
                <option value="业务线">业务线</option>
                <option value="自定义数据1">自定义数据1</option>
                <option value="自定义数据2">自定义数据2</option>
                <option value="自定义数据3">自定义数据3</option>
              </select>
            ) : (
              <input
                type="text"
                placeholder="请输入期望值"
                className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.entityConditions[0]?.expectedValue || ''}
                onChange={(e) => updateEntityCondition({ expectedValue: e.target.value })}
              />
            )}
          </div>
        </div>

        {/* 实体类型说明 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right"></label>
          <div className="flex-1">
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <h5 className="text-sm font-medium text-blue-900 mb-2">💡 支持的实体类型</h5>
              <div className="grid grid-cols-1 gap-2 text-xs">
                {ENTITY_TYPES.map(type => (
                  <div key={type.value} className="flex items-start space-x-2">
                    <span className="font-medium text-blue-800 min-w-[60px]">{type.label}：</span>
                    <span className="text-blue-700">{type.description}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EntityCheckConfig; 