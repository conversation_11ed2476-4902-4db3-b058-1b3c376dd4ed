import React, { useState } from 'react';
import type { EntityCheckConfig } from './EntityCheckConfig';

/**
 * 预设案例接口
 */
interface Case {
  name: string;
  description: string;
  config: EntityCheckConfig;
  testText: string;
  expectedResult: string;
}

interface Props {
  onLoadCase: (config: EntityCheckConfig, testText: string) => void;
}

/**
 * 信息实体检查案例库组件
 */
const EntityCheckCases: React.FC<Props> = ({ onLoadCase }) => {
  const [previewCase, setPreviewCase] = useState<Case | null>(null);

  // 预设案例
  const cases: Case[] = [
    {
      name: '客服身份一致性检查',
      description: '验证客户提到的客服姓名与实际服务客服是否一致',
      config: {
        detectionRole: 'customer',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 5,
        entityConditions: [
          {
            entityType: 'personName',
            operator: 'equals',
            expectedValue: '客服姓名',
            fieldType: 'system'
          }
        ]
      },
      testText: '客户：王经理您好，我想咨询一下业务。\n客服：好的，我是王经理，请问需要什么帮助？\n客户：谢谢王经理。',
      expectedResult: '命中（客户提到的"王经理"与系统记录的客服姓名一致）'
    },
    {
      name: '业务线归属验证',
      description: '检查对话中提到的业务线与通话记录的业务线是否匹配',
      config: {
        detectionRole: 'all',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 5,
        entityConditions: [
          {
            entityType: 'personName',
            operator: 'contains',
            expectedValue: '业务线',
            fieldType: 'system'
          }
        ]
      },
      testText: '客服：您好，这里是信用卡部，我是小李。\n客户：我想办理信用卡业务。\n客服：好的，为您介绍信用卡产品。',
      expectedResult: '命中（对话涉及的业务与系统记录的业务线匹配）'
    },
    {
      name: '客户地址信息核验',
      description: '验证客户提供的城市信息与档案记录是否一致',
      config: {
        detectionRole: 'customer',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 5,
        entityConditions: [
          {
            entityType: 'city',
            operator: 'equals',
            expectedValue: '自定义数据1',
            fieldType: 'system'
          }
        ]
      },
      testText: '客服：请确认您的所在城市。\n客户：我在上海。\n客服：好的，上海的用户，为您优先处理。',
      expectedResult: '命中（客户提到的"上海"与档案中的城市信息一致）'
    },
    {
      name: '预约日期冲突检测',
      description: '检查客户提到的日期与系统预约日期是否有冲突',
      config: {
        detectionRole: 'customer',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 5,
        entityConditions: [
          {
            entityType: 'date',
            operator: 'notEquals',
            expectedValue: '自定义数据2',
            fieldType: 'system'
          }
        ]
      },
      testText: '客服：您的预约是明天吗？\n客户：不对，我预约的是后天。\n客服：我来帮您核实一下。',
      expectedResult: '命中（客户提到的日期与系统预约日期不一致，需要核实）'
    },
    {
      name: '客服技能组匹配验证',
      description: '确认对话内容与客服所属技能组的专业范围一致',
      config: {
        detectionRole: 'all',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 5,
        entityConditions: [
          {
            entityType: 'personName',
            operator: 'contains',
            expectedValue: '技能组名称',
            fieldType: 'system'
          }
        ]
      },
      testText: '客服：我是贷款专员小张。\n客户：我想了解贷款产品。\n客服：为您详细介绍贷款业务。',
      expectedResult: '命中（客服专业与技能组设置匹配）'
    },
    {
      name: '身份证信息安全检查',
      description: '防止客服意外泄露客户身份证号码',
      config: {
        detectionRole: 'agent',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 5,
        entityConditions: [
          {
            entityType: 'idCard',
            operator: 'notContains',
            expectedValue: '自定义数据3',
            fieldType: 'system'
          }
        ]
      },
      testText: '客服：您的身份信息我已经看到了。\n客户：好的。\n客服：信息核实无误，为您继续办理。',
      expectedResult: '命中（客服没有在对话中暴露身份证信息）'
    }
  ];

  /**
   * 加载案例
   */
  const loadCase = (caseItem: Case) => {
    onLoadCase(caseItem.config, caseItem.testText);
    setPreviewCase(null);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">📂</span>
        <h2 className="text-xl font-semibold text-gray-900">案例库</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {cases.length} 个案例
        </span>
      </div>
      
      <div className="space-y-4">
        {cases.map((caseItem, index) => (
          <div key={index} className="group">
            <div className="p-4 rounded-lg bg-gray-50 border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="font-semibold text-gray-800 group-hover:text-blue-800">
                      {caseItem.name}
                    </h4>
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-700">
                      {caseItem.config.entityConditions.length} 个条件
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{caseItem.description}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>
                      角色：{
                        caseItem.config.detectionRole === 'agent' ? '客服' :
                        caseItem.config.detectionRole === 'customer' ? '客户' : '所有角色'
                      }
                    </span>
                    <span>•</span>
                    <span>
                      范围：{caseItem.config.detectionScope === 'full' ? '全文' : '指定范围'}
                    </span>
                    <span>•</span>
                    <span className="text-blue-600">预期：{caseItem.expectedResult}</span>
                  </div>
                  
                  {/* 实体条件预览 */}
                  <div className="mt-3 flex flex-wrap gap-1">
                    {caseItem.config.entityConditions.map((condition, condIndex) => {
                      const entityNames: Record<string, string> = {
                        date: '日期', time: '时间', province: '省份', city: '城市',
                        district: '区县', idCard: '身份证', personName: '人名', email: '邮箱'
                      };
                      const operatorNames: Record<string, string> = {
                        equals: '=', notEquals: '!=', contains: '包含', notContains: '不包含'
                      };
                      
                      return (
                        <span
                          key={condIndex}
                          className="inline-flex items-center px-2 py-1 bg-white border border-gray-200 rounded text-xs text-gray-700"
                        >
                          {entityNames[condition.entityType]} {operatorNames[condition.operator]}
                          {condition.expectedValue && ` "${condition.expectedValue}"`}
                        </span>
                      );
                    })}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => setPreviewCase(previewCase?.name === caseItem.name ? null : caseItem)}
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200"
                    title="预览测试文本"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button 
                    onClick={() => loadCase(caseItem)}
                    className="px-4 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors font-medium"
                  >
                    加载
                  </button>
                </div>
              </div>
            </div>
            
            {/* 预览区域 */}
            {previewCase?.name === caseItem.name && (
              <div className="mt-2 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="font-medium text-blue-900">测试文本预览</h5>
                  <button
                    onClick={() => setPreviewCase(null)}
                    className="text-blue-400 hover:text-blue-600"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="bg-white p-3 rounded border border-blue-200">
                  <div className="text-sm text-gray-800 font-mono leading-relaxed whitespace-pre-line">
                    {previewCase.testText}
                  </div>
                </div>
                <div className="mt-2 text-xs text-blue-700">
                  💡 {previewCase.expectedResult}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 案例说明 */}
      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-lg">💡</span>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-blue-900 mb-2">案例说明</h4>
            <div className="text-sm text-blue-800 space-y-2">
              <p>• <strong>个人信息检查</strong>：适用于需要收集客户身份信息的业务场景</p>
              <p>• <strong>预约时间验证</strong>：确保预约对话包含明确的时间信息</p>
              <p>• <strong>地址信息验证</strong>：检测地址信息的完整性和准确性</p>
              <p>• <strong>联系方式检查</strong>：验证客户提供的联系方式格式</p>
              <p>• <strong>特定值匹配</strong>：检测特定的实体值，如"今天"、"明天"等</p>
              <p>• <strong>隐私保护检查</strong>：确保不泄露客户敏感信息</p>
            </div>
          </div>
        </div>
      </div>

      {/* 自定义提示 */}
      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-sm text-yellow-800 font-medium">
            💡 提示：加载案例后可以修改配置参数来探索不同的检测效果
          </span>
        </div>
      </div>
    </div>
  );
};

export default EntityCheckCases;