import React from 'react';

/**
 * 信息实体检查使用提示组件
 */
const EntityCheckTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">使用提示</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        {/* 基础操作提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⌨️</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">基础操作</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">实体类型选择</span>
                    <span className="text-xs text-gray-500 block">支持8种常见实体：日期、时间、人名、省份、城市、区县、身份证、邮箱</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">字段选择</span>
                    <span className="text-xs text-gray-500 block">必须选择系统常规字段或自定义字段，不能留空</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">比较逻辑</span>
                    <span className="text-xs text-gray-500 block">实体值与随路参数比较：=（一致）、!=（不一致）、包含、不包含</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 高级功能提示 */}
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-sm">🔧</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">高级功能</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">多条件组合</span>
                    <span className="text-xs text-gray-500 block">支持添加多个实体条件，所有条件都满足才算命中</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">自定义字段匹配</span>
                    <span className="text-xs text-gray-500 block">支持系统常规字段和自定义字段两种匹配模式</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">智能实体识别</span>
                    <span className="text-xs text-gray-500 block">自动识别各种格式的实体，如"今天"、"2024-01-01"等</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 最佳实践 */}
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">最佳实践</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">客服身份验证</span>
                    <span className="text-xs text-gray-500 block">检查客户提到的客服姓名是否与实际服务客服一致</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">地址信息核验</span>
                    <span className="text-xs text-gray-500 block">验证对话中的地址信息与客户档案是否匹配</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">敏感信息防护</span>
                    <span className="text-xs text-gray-500 block">使用"不包含"确保客服不会泄露客户身份证等信息</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 参数调优建议 */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-sm">⚡</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">参数调优建议</h4>
              <div className="space-y-3">
                <div className="bg-white rounded-md p-3 border border-orange-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded font-medium">日期时间检测</span>
                  </div>
                  <div className="text-xs text-gray-600 space-y-1">
                    <p>• <strong>预约日期验证</strong>：比较客户提到的日期与系统预约日期</p>
                    <p>• <strong>服务时间核验</strong>：检查通话时间是否符合业务规范</p>
                  </div>
                </div>
                <div className="bg-white rounded-md p-3 border border-orange-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded font-medium">身份一致性验证</span>
                  </div>
                  <div className="text-xs text-gray-600 space-y-1">
                    <p>• <strong>客服身份验证</strong>：确认客户提到的客服姓名与实际一致</p>
                    <p>• <strong>技能组匹配</strong>：验证对话内容与客服专业领域匹配</p>
                  </div>
                </div>
                <div className="bg-white rounded-md p-3 border border-orange-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded font-medium">敏感信息防护</span>
                  </div>
                  <div className="text-xs text-gray-600 space-y-1">
                    <p>• <strong>隐私保护</strong>：使用"不包含"确保不泄露客户身份证</p>
                    <p>• <strong>信息安全</strong>：监控是否意外暴露敏感个人信息</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 常见问题解答 */}
        <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-4 border border-gray-200">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <span className="text-gray-600 text-sm">❓</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">常见问题解答</h4>
              <div className="space-y-3">
                <div className="border-l-4 border-blue-400 pl-3">
                  <p className="text-sm font-medium text-gray-800">Q: 如何提高实体识别的准确率？</p>
                  <p className="text-xs text-gray-600 mt-1">A: 确保测试文本格式规范，使用标准的实体表达方式，避免过于口语化的表达。</p>
                </div>
                <div className="border-l-4 border-green-400 pl-3">
                  <p className="text-sm font-medium text-gray-800">Q: 为什么某些实体没有被识别？</p>
                  <p className="text-xs text-gray-600 mt-1">A: 实体识别基于规则匹配，请检查实体格式是否符合预定义模式，可参考案例库中的示例。</p>
                </div>
                <div className="border-l-4 border-purple-400 pl-3">
                  <p className="text-sm font-medium text-gray-800">Q: 多条件如何配置才合理？</p>
                  <p className="text-xs text-gray-600 mt-1">A: 建议先使用单条件测试，确认效果后再添加其他条件。避免条件过于严格导致误报。</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 算法说明 */}
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
              <span className="text-indigo-600 text-sm">🔬</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">算法说明</h4>
              <div className="text-xs text-gray-600 space-y-2">
                <p><strong>实体识别原理</strong>：基于正则表达式和规则库进行模式匹配，支持多种格式识别。</p>
                <p><strong>匹配策略</strong>：优先精确匹配，其次模糊匹配，最后语义匹配。</p>
                <p><strong>性能优化</strong>：针对中文特点优化的识别算法，支持常见的表达习惯。</p>
                <p><strong>扩展性</strong>：算法支持自定义实体类型和匹配规则的扩展。</p>
              </div>
            </div>
          </div>
        </div>

        {/* 快速开始 */}
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 border border-yellow-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 text-sm">🚀</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">快速开始</h4>
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <span className="bg-yellow-200 text-yellow-800 px-2 py-1 rounded">步骤1</span>
                <span>选择案例</span>
                <span>→</span>
                <span className="bg-yellow-200 text-yellow-800 px-2 py-1 rounded">步骤2</span>
                <span>点击加载</span>
                <span>→</span>
                <span className="bg-yellow-200 text-yellow-800 px-2 py-1 rounded">步骤3</span>
                <span>调整配置</span>
                <span>→</span>
                <span className="bg-yellow-200 text-yellow-800 px-2 py-1 rounded">步骤4</span>
                <span>执行测试</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EntityCheckTips; 