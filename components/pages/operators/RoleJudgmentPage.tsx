import React, { useState } from 'react';
import RoleJudgmentConcepts from './roleJudgment/RoleJudgmentConcepts';
import RoleJudgmentConfig from './roleJudgment/RoleJudgmentConfig';
import RoleJudgmentTester from './roleJudgment/RoleJudgmentTester';
import RoleJudgmentCases from './roleJudgment/RoleJudgmentCases';
import RoleJudgmentTips from './roleJudgment/RoleJudgmentTips';

/**
 * 角色判断主页面
 * @constructor
 */
const RoleJudgmentPage: React.FC = () => {
  // 配置状态
  const [config, setConfig] = useState({
    sentenceIndex: 1,
    role: 'customer' as 'customer' | 'agent',
  });

  // 测试文本状态
  const [testText, setTestText] = useState(
    '客服：您好，欢迎致电。\n客户：你好，我咨询一下。\n客服：请讲。\n客户：这个产品还有货吗？\n客服：有的，我为您下单。'
  );

  // 预设案例
  const cases = [
    {
      name: '检查开场白',
      description: '检测第一句话是否为客服',
      config: { sentenceIndex: 1, role: 'agent' as const },
      expectedResult: '应该命中',
    },
    {
      name: '检查客户响应',
      description: '检测第二句话是否为客户',
      config: { sentenceIndex: 2, role: 'customer' as const },
      expectedResult: '应该命中',
    },
    {
      name: '检查结束语',
      description: '检测最后一句话是否为客服',
      config: { sentenceIndex: -1, role: 'agent' as const },
      expectedResult: '应该命中',
    },
    {
      name: '检查倒数第二句',
      description: '检测倒数第二句话是否为客户',
      config: { sentenceIndex: -2, role: 'customer' as const },
      expectedResult: '应该命中',
    },
    {
      name: '错误案例',
      description: '检测第一句话是否为客户',
      config: { sentenceIndex: 1, role: 'customer' as const },
      expectedResult: '不应命中',
    },
  ];

  /**
   * 加载案例
   * @param caseConfig
   */
  const loadCase = (caseConfig: any) => {
    setConfig({ ...config, ...caseConfig });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                🧑‍⚖️ 角色判断
              </h1>
              <p className="text-gray-600 mt-1">
                检测指定句子的说话角色是否正确
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                🚀 基础入门
              </span>
              <span className="text-sm text-gray-500">
                算子类型：语音检查
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-full mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 左侧：60%宽度 */}
          <div className="w-[60%] space-y-6">
            <RoleJudgmentConcepts />
            <RoleJudgmentConfig config={config} setConfig={setConfig} />
            <RoleJudgmentTester config={config} text={testText} setText={setTestText} />
          </div>

          {/* 右侧：40%宽度 */}
          <div className="w-[40%] space-y-6">
            <RoleJudgmentCases cases={cases} loadCase={loadCase} />
            <RoleJudgmentTips />
          </div>
        </div>
      </div>

      {/* 学习建议 */}
      <div className="max-w-full mx-auto px-6 pb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">💡</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                学习建议
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>• <strong>理解索引</strong>：熟练掌握正负数索引的用法，这是此功能的核心。</p>
                <p>• <strong>结合场景</strong>：思考如何在实际质检流程中，通过角色判断来验证SOP执行的准确性。</p>
                <p>• <strong>扩展应用</strong>：可以将角色判断作为其他复杂规则的前置条件，实现更精细的质检逻辑。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleJudgmentPage; 