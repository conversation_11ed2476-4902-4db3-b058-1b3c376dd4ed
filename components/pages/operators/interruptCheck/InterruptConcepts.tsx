import React from 'react';

/**
 * 抢话检查核心概念组件
 * 展示抢话检查的基本概念和重要信息
 */
const InterruptConcepts: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          核心概念
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          基础知识
        </span>
      </div>
      
      <div className="space-y-6">
        {/* 功能介绍 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">🎯</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">功能介绍</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                检测对话中是否出现抢话现象。通过分析对话中的时间重叠、语句长度等特征，
                识别出可能影响服务质量的抢话行为，帮助提升对话质量。
              </p>
            </div>
          </div>
        </div>

        {/* 应用场景 */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-lg">🏢</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">应用场景</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">客服培训考核</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">服务质量监控</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">礼貌用语规范</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">客户体验优化</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 优势特点 */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-lg">⭐</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">优势特点</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">精确的时间控制</span>
                    <span className="text-xs text-gray-500 ml-1">- 毫秒级检测精度</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">智能过滤机制</span>
                    <span className="text-xs text-gray-500 ml-1">- 自动过滤短句干扰</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">灵活的配置选项</span>
                    <span className="text-xs text-gray-500 ml-1">- 多维度参数调节</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">双声道支持</span>
                    <span className="text-xs text-gray-500 ml-1">- 更准确的检测效果</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 检测原理 */}
        <div className="bg-gradient-to-r from-yellow-50 to-amber-50 rounded-lg p-4 border border-yellow-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 text-lg">🔍</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">检测原理</h3>
              <div className="space-y-2 text-sm text-gray-700">
                <p>• <strong>抢话时间</strong>：前一句结束时间与当前句开始时间的差值</p>
                <p>• <strong>最小字数</strong>：过滤掉"嗯"、"好的"等短句干扰</p>
                <p>• <strong>延时判定</strong>：设置延迟阈值，避免正常对话被误判</p>
                <p>• <strong>双声道优势</strong>：可以准确识别声音重叠的情况</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InterruptConcepts; 