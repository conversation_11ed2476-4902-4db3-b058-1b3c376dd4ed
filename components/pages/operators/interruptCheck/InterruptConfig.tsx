import React, { useState } from 'react';

interface InterruptConfigProps {
  config: {
    detectionRole: 'agent' | 'customer' | 'all';
    detectionScope: 'full' | 'range';
    rangeStart: number;
    rangeEnd: number;
    interruptTime: number;
    minWordCount: number;
    delayTime: number;
  };
  setConfig: (config: any) => void;
}

/**
 * 抢话检查配置组件
 * 提供抢话检查的各项参数配置
 */
const InterruptConfig: React.FC<InterruptConfigProps> = ({ config, setConfig }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">
          配置演示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>

      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionRole}
            onChange={(e) => setConfig({ ...config, detectionRole: e.target.value as any })}
          >
            <option value="all">所有角色</option>
            <option value="agent">客服</option>
            <option value="customer">客户</option>
          </select>
        </div>

        {/* 前置条件 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">前置条件</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled
          >
            <option value="none">无</option>
          </select>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionScope}
            onChange={(e) => setConfig({ ...config, detectionScope: e.target.value as any })}
          >
            <option value="full">全文</option>
            <option value="range">指定范围</option>
          </select>
        </div>

        {/* 检查逻辑 */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">抢话时间 {'>='}
              <input
                type="number"
                className="mx-2 w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.interruptTime}
                onChange={(e) => setConfig({ ...config, interruptTime: parseInt(e.target.value) || 0 })}
              />
              毫秒
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">抢话句子的字数 {'>='}
              <input
                type="number"
                className="mx-2 w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.minWordCount}
                onChange={(e) => setConfig({ ...config, minWordCount: parseInt(e.target.value) || 0 })}
              />
              个字
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">抢话延时判定
              <input
                type="number"
                className="mx-2 w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.delayTime}
                onChange={(e) => setConfig({ ...config, delayTime: parseInt(e.target.value) || 0 })}
              />
              毫秒
            </span>
          </div>

          <div className="text-sm text-gray-700">
            并且发生抢话句子的前一句的话语速大于 3.5 字/s 时，算作命中。
            <span className="relative inline-block">
              <span>命中率不符合预期的说明</span>
              <span 
                className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50"
                onMouseEnter={() => setShowTooltip(true)}
                onMouseLeave={() => setShowTooltip(false)}
              >
                ?
              </span>
              {showTooltip && (
                <div className="absolute left-0 bottom-6 w-80 p-2 bg-gray-900 text-white text-xs rounded shadow-lg z-10">
                  抢话发生时，相当于两个角色同时在讲话，对于单声道录音，录音转文本后，只能识别出一个角色说的话，所以抢话的情况很难检测出来。而双声道(立体声、双轨)录音，两个角色的声音保存在两个声道中，所以即使声音听起来是重叠在一起的，但是录音转文本后声音重叠部分依然可以被识别出来，所以出现抢话时是可以准确检测出来的。
                </div>
              )}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InterruptConfig; 