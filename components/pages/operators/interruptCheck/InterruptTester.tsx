import React, { useState, useEffect } from 'react';

interface InterruptTesterProps {
  config: {
    detectionRole: 'agent' | 'customer' | 'all';
    detectionScope: 'full' | 'range';
    rangeStart: number;
    rangeEnd: number;
    interruptTime: number;
    minWordCount: number;
    delayTime: number;
  };
  text: string;
  setText: (text: string) => void;
}

// 默认示例文本
const DEFAULT_EXAMPLE_TEXT = `[00:01] 客服：您好，欢迎来到我们公司。
[00:02] 客户：你好，我想咨询一下。
[00:03] 客服：好的，请问您需要了解什么产品？`;

/**
 * 抢话检查实时测试组件
 */
const InterruptTester: React.FC<InterruptTesterProps> = ({
  config,
  text,
  setText
}) => {
  // 测试结果状态
  const [testResult, setTestResult] = useState<{
    detected: boolean;
    details?: string[];
  }>({
    detected: false,
    details: []
  });

  // 初始化时加载示例文本
  useEffect(() => {
    if (!text) {
      setText(DEFAULT_EXAMPLE_TEXT);
    }
  }, []);

  // 处理文本变化
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
    setTestResult({ detected: false, details: [] }); // 清除之前的测试结果
  };

  // 执行测试
  const runTest = () => {
    if (!text.trim()) {
      return;
    }

    const lines = text.split('\n').filter(line => line.trim());
    const details: string[] = [];
    let hasInterrupt = false;

    // 根据检测角色筛选对话
    const targetLines = config.detectionRole === 'all' 
      ? lines 
      : lines.filter(line => line.startsWith(config.detectionRole === 'agent' ? '客服：' : '客户：'));

    // 根据检测范围筛选
    const scopedLines = config.detectionScope === 'range'
      ? targetLines.slice(config.rangeStart - 1, config.rangeEnd)
      : targetLines;

    // 检测抢话
    for (let i = 1; i < scopedLines.length; i++) {
      const prevLine = scopedLines[i - 1];
      const currentLine = scopedLines[i];
      
      // 检查时间标记
      const prevTime = prevLine.match(/\[(\d{2}:\d{2})\]/);
      const currentTime = currentLine.match(/\[(\d{2}:\d{2})\]/);
      
      if (prevTime && currentTime) {
        const timeDiff = getTimeDifference(prevTime[1], currentTime[1]);
        const currentText = currentLine.replace(/\[\d{2}:\d{2}\]/, '').trim();
        const wordCount = currentText.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').length;

        // 检查抢话条件
        if (timeDiff * 1000 < config.interruptTime && wordCount >= config.minWordCount) {
          const delayCheck = config.delayTime === 0 || timeDiff * 1000 >= config.delayTime;
          if (delayCheck) {
            hasInterrupt = true;
            details.push(`检测到抢话：\n  ${prevLine}\n  ${currentLine}\n  时间差：${(timeDiff * 1000).toFixed(0)}ms，字数：${wordCount}`);
          }
        }
      }
    }

    setTestResult({
      detected: hasInterrupt,
      details: details
    });
  };

  // 计算时间差（秒）
  const getTimeDifference = (time1: string, time2: string): number => {
    const [min1, sec1] = time1.split(':').map(Number);
    const [min2, sec2] = time2.split(':').map(Number);
    return (min2 * 60 + sec2) - (min1 * 60 + sec1);
  };

  // 重置测试
  const resetTest = () => {
    setText(DEFAULT_EXAMPLE_TEXT);
    setTestResult({ detected: false, details: [] });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-semibold text-gray-900">
              实时测试
            </h3>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              即时验证
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              testResult.detected 
                ? 'bg-red-100 text-red-800'
                : 'bg-green-100 text-green-800'
            }`}>
              {testResult.detected ? '检测到抢话' : '未检测到抢话'}
            </span>
          </div>
        </div>

        {/* 输入区域 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-gray-700">
              测试文本
            </label>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>行数: {text.split('\n').filter(line => line.trim()).length}</span>
              <span>•</span>
              <span>字符: {text.length}</span>
            </div>
          </div>
          <div className="relative">
            <textarea
              value={text}
              onChange={handleTextChange}
              placeholder={`请输入测试文本，格式如：\n[00:05] 客服：您好，欢迎来到我们公司\n[00:15] 客户：你好，我想咨询产品`}
              className="w-full h-40 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
            {text && (
              <button
                onClick={resetTest}
                className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600"
                title="重置为示例文本"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="mt-4 flex space-x-3">
          <button
            onClick={runTest}
            disabled={!text.trim()}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <span className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>执行测试</span>
            </span>
          </button>
          <button
            onClick={resetTest}
            className="px-4 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-medium"
          >
            重置
          </button>
        </div>

        {/* 检测结果 */}
        {testResult.details && testResult.details.length > 0 && (
          <div className="mt-4 space-y-3">
            <h4 className="font-medium text-gray-900">检测详情：</h4>
            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              {testResult.details.map((detail, index) => (
                <div key={index} className="text-sm text-gray-700 whitespace-pre-line">
                  {detail}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 使用提示 */}
        <div className="mt-4 bg-blue-50 rounded-lg p-4">
          <p className="text-sm text-blue-800">
            💡 提示：输入的文本需要包含时间标记[MM:SS]和说话角色标识。系统会根据配置的参数自动检测抢话现象。
          </p>
        </div>
      </div>
    </div>
  );
};

export default InterruptTester; 