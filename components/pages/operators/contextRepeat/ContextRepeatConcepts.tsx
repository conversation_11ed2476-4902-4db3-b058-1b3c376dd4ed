import React from 'react';

/**
 * 上下文重复检查核心概念组件
 * 介绍功能原理和应用场景
 */
const ContextRepeatConcepts: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          核心概念
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          基础知识
        </span>
      </div>
      
      <div className="space-y-6">
        {/* 功能介绍 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">🎯</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">功能介绍</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                检测当前句子与之前的句子内容是否重复。通过计算句子间的相似度，
                识别出重复表达的内容，有效避免对话中的冗余信息和低效沟通。
                支持灵活的相似度阈值设置和智能的短句过滤功能。
              </p>
            </div>
          </div>
        </div>

        {/* 应用场景 */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-lg">🏢</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">应用场景</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">客户投诉重复检测</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">客服标准回复监控</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">对话效率分析</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">重复内容质检</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 优势特点 */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-lg">⭐</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">优势特点</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">智能相似度计算</span>
                    <span className="text-xs text-gray-500 ml-1">- 基于编辑距离的精确算法</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">灵活的阈值设置</span>
                    <span className="text-xs text-gray-500 ml-1">- 0.1-1.0可调节相似度</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">短句自动过滤</span>
                    <span className="text-xs text-gray-500 ml-1">- 避免无意义短句影响</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">例外句子支持</span>
                    <span className="text-xs text-gray-500 ml-1">- 排除特定内容的重复检测</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 算法原理 */}
        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-lg">🧮</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">算法原理</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-2">
                  <span className="inline-flex items-center justify-center w-5 h-5 text-xs bg-orange-200 text-orange-800 rounded-full font-mono">1</span>
                  <span className="text-sm text-gray-700">
                    <strong>文本预处理</strong>：去除标点符号和多余空格，保留核心文字内容
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="inline-flex items-center justify-center w-5 h-5 text-xs bg-orange-200 text-orange-800 rounded-full font-mono">2</span>
                  <span className="text-sm text-gray-700">
                    <strong>相似度计算</strong>：使用编辑距离算法计算句子间的相似程度
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="inline-flex items-center justify-center w-5 h-5 text-xs bg-orange-200 text-orange-800 rounded-full font-mono">3</span>
                  <span className="text-sm text-gray-700">
                    <strong>重复判定</strong>：对比设定阈值，超过即认定为重复内容
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="inline-flex items-center justify-center w-5 h-5 text-xs bg-orange-200 text-orange-800 rounded-full font-mono">4</span>
                  <span className="text-sm text-gray-700">
                    <strong>违规统计</strong>：计算重复次数，达到设定次数则判定为违规
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 与其他算子的对比 */}
        <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-4 border border-gray-200">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <span className="text-gray-600 text-lg">⚖️</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">与其他算子的对比</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full text-xs">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-2 font-medium text-gray-800">算子类型</th>
                      <th className="text-left py-2 font-medium text-gray-800">检测目标</th>
                      <th className="text-left py-2 font-medium text-gray-800">适用场景</th>
                    </tr>
                  </thead>
                  <tbody className="text-gray-600">
                    <tr className="border-b border-gray-100">
                      <td className="py-2 font-medium text-orange-600">上下文重复检查</td>
                      <td className="py-2">句子间的重复内容</td>
                      <td className="py-2">避免冗余表达、提升对话效率</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-2">关键词检查</td>
                      <td className="py-2">特定关键词出现</td>
                      <td className="py-2">标准用语检查、敏感词监控</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-2">文本相似度</td>
                      <td className="py-2">与模板的相似程度</td>
                      <td className="py-2">标准回复检查、话术规范</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContextRepeatConcepts; 