import React from 'react';

export interface ContextRepeatConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  withinSentences: number; // 在几句以内重复
  similarityThreshold: number; // 相似度阈值 0.1-1.0
  violationCount: number; // 第几次重复算违规
  minWordCount: number; // 少于几个字不检测
  exceptionSentences: string[]; // 例外句子
}

interface ContextRepeatConfigProps {
  config: ContextRepeatConfig;
  onConfigChange: (config: ContextRepeatConfig) => void;
}

/**
 * 上下文重复检查配置演示组件
 * 提供交互式配置界面
 */
const ContextRepeatConfigComponent: React.FC<ContextRepeatConfigProps> = ({
  config,
  onConfigChange
}) => {
  const updateConfig = (updates: Partial<ContextRepeatConfig>) => {
    onConfigChange({ ...config, ...updates });
  };

  // 添加例外句子
  const addExceptionSentence = (sentence: string) => {
    if (sentence.trim() && !config.exceptionSentences.includes(sentence.trim())) {
      updateConfig({ 
        exceptionSentences: [...config.exceptionSentences, sentence.trim()] 
      });
    }
  };

  // 删除例外句子
  const removeExceptionSentence = (index: number) => {
    updateConfig({ 
      exceptionSentences: config.exceptionSentences.filter((_, i) => i !== index) 
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">
          配置演示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>
      
      {/* 配置规则说明 */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-lg">📋</span>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 mb-3">配置规则总结</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🎯 检测角色</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>客服</strong>：仅检测"客服："开头的对话</li>
                    <li>• <strong>客户</strong>：仅检测"客户："开头的对话</li>
                    <li>• <strong>所有角色</strong>：检测全部对话内容</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">📍 检测范围</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>全文</strong>：检测所有符合角色条件的句子</li>
                    <li>• <strong>指定范围</strong>：只检测第X~Y句之间的内容</li>
                  </ul>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🔍 重复逻辑</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>句内检测</strong>：在前N句内查找重复</li>
                    <li>• <strong>相似度</strong>：大于阈值即判定为重复</li>
                    <li>• <strong>违规次数</strong>：第N次重复才算违规</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">⚡ 特殊规则</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>短句过滤</strong>：少于N个字的句子不检测</li>
                    <li>• <strong>例外句子</strong>：指定句子重复时不算违规</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionRole}
            onChange={(e) => updateConfig({ detectionRole: e.target.value as any })}
          >
            <option value="agent">客服</option>
            <option value="customer">客户</option>
            <option value="all">所有角色</option>
          </select>
        </div>

        {/* 前置条件 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">前置条件</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="none">无</option>
          </select>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionScope}
            onChange={(e) => updateConfig({ detectionScope: e.target.value as any })}
          >
            <option value="full">全文</option>
            <option value="range">指定范围</option>
          </select>
        </div>

        {/* 指定范围配置 */}
        {config.detectionScope === 'range' && (
          <div className="flex items-center space-x-4">
            <label className="w-20 text-sm font-medium text-gray-700 text-right">范围</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">第</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeStart}
                onChange={(e) => updateConfig({ rangeStart: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">~</span>
              <input
                type="number"
                min="1"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.rangeEnd}
                onChange={(e) => updateConfig({ rangeEnd: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">句</span>
              <span 
                className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs text-gray-500 border border-gray-300 rounded-full cursor-help hover:bg-gray-50" 
                title="指定检测的句子范围，例如第1~3句表示只检测前3句对话"
              >
                ?
              </span>
            </div>
          </div>
        )}

        {/* 检查逻辑 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">检查逻辑</label>
          <div className="flex-1 space-y-3">
            {/* 重复检测范围 */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">在</span>
              <input
                type="number"
                min="2"
                max="10"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.withinSentences}
                onChange={(e) => updateConfig({ withinSentences: parseInt(e.target.value) || 2 })}
              />
              <span className="text-sm text-gray-700">句以内重复，重复句子的相似度大于等于</span>
              <input
                type="number"
                min="0.1"
                max="1.0"
                step="0.1"
                className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.similarityThreshold}
                onChange={(e) => updateConfig({ similarityThreshold: parseFloat(e.target.value) || 0.8 })}
              />
              <span className="text-sm text-gray-500">(0.1-1.0，分数越高，相似度越高)。</span>
            </div>

            {/* 违规条件 */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">并且在第</span>
              <input
                type="number"
                min="1"
                max="5"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.violationCount}
                onChange={(e) => updateConfig({ violationCount: parseInt(e.target.value) || 1 })}
              />
              <span className="text-sm text-gray-700">次重复出现时算违规，当一句话少于或等于</span>
              <input
                type="number"
                min="0"
                max="10"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={config.minWordCount}
                onChange={(e) => updateConfig({ minWordCount: parseInt(e.target.value) || 2 })}
              />
              <span className="text-sm text-gray-700">个字时不检测。</span>
            </div>
          </div>
        </div>

        {/* 例外句子 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">例外句子</label>
          <div className="flex-1">
            <div className="flex">
              <input
                type="text"
                className="flex-1 max-w-lg px-3 py-2 text-sm border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="输入Enter键添加例外句子"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    const input = (e.target as HTMLInputElement).value.trim();
                    if (input) {
                      addExceptionSentence(input);
                      (e.target as HTMLInputElement).value = '';
                    }
                  }
                }}
              />
              <button 
                className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white text-sm rounded-r-md hover:from-purple-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200"
                onClick={(e) => {
                  const input = (e.target as HTMLButtonElement).previousElementSibling as HTMLInputElement;
                  const inputValue = input.value.trim();
                  if (inputValue) {
                    addExceptionSentence(inputValue);
                    input.value = '';
                  }
                }}
              >
                🤖 AI优化
              </button>
            </div>
            {config.exceptionSentences.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {config.exceptionSentences.map((sentence, i) => (
                  <span key={i} className="inline-flex items-center px-2 py-1 rounded text-xs bg-yellow-100 text-yellow-800">
                    {sentence}
                    <button
                      onClick={() => removeExceptionSentence(i)}
                      className="ml-1 text-yellow-600 hover:text-yellow-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
            <div className="mt-2 text-xs text-gray-500">
              💡 可以设置例外的句子，这些句子重复出现时不算上下文重复
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContextRepeatConfigComponent; 