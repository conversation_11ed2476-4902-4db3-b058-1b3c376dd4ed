import React from 'react';
import { ContextRepeatConfig } from './ContextRepeatConfig';

interface ContextRepeatTestResult {
  matched: boolean;
  repeatedSentences: {
    current: string;
    similar: string;
    similarity: number;
    repeatCount: number;
  }[];
  filteredSentences: string[]; // 被过滤的短句
  exceptionMatches: string[]; // 命中的例外句子
  details: string;
}

interface ContextRepeatTesterProps {
  config: ContextRepeatConfig;
  testText: string;
  onTestTextChange: (text: string) => void;
  testResult: ContextRepeatTestResult | null;
  onRunTest: () => void;
  onClearResult: () => void;
}

/**
 * 计算两个字符串的编辑距离
 */
function editDistance(str1: string, str2: string): number {
  const len1 = str1.length;
  const len2 = str2.length;
  const dp: number[][] = Array(len1 + 1).fill(null).map(() => Array(len2 + 1).fill(0));
  
  for (let i = 0; i <= len1; i++) dp[i][0] = i;
  for (let j = 0; j <= len2; j++) dp[0][j] = j;
  
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1];
      } else {
        dp[i][j] = Math.min(
          dp[i - 1][j] + 1,     // 删除
          dp[i][j - 1] + 1,     // 插入
          dp[i - 1][j - 1] + 1  // 替换
        );
      }
    }
  }
  
  return dp[len1][len2];
}

/**
 * 计算两个句子的相似度
 */
function calculateSimilarity(str1: string, str2: string): number {
  // 去除标点和空格，只保留文字内容
  const clean1 = str1.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
  const clean2 = str2.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
  
  if (clean1 === clean2) return 1.0;
  if (clean1.length === 0 || clean2.length === 0) return 0.0;
  
  // 计算编辑距离
  const distance = editDistance(clean1, clean2);
  const maxLen = Math.max(clean1.length, clean2.length);
  
  // 归一化为相似度
  return 1 - (distance / maxLen);
}

/**
 * 上下文重复检查实时测试组件
 * 提供测试功能和结果展示
 */
const ContextRepeatTester: React.FC<ContextRepeatTesterProps> = ({
  config,
  testText,
  onTestTextChange,
  testResult,
  onRunTest,
  onClearResult
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          🧪 实时测试
        </h2>
        <div className="flex items-center space-x-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            即时验证
          </span>
          <button
            onClick={() => {
              onTestTextChange('客服：您好，欢迎来到我们公司。\n客户：我想咨询一下你们的产品。\n客服：您好，欢迎来到我们公司。\n客户：你好，我想了解产品信息。\n客服：好的，我为您详细介绍。');
              onClearResult();
            }}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            重置示例
          </button>
        </div>
      </div>
      
      <div className="space-y-6">
        {/* 测试文本输入区 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label htmlFor="test-text" className="block text-sm font-medium text-gray-700">
              📝 测试文本
            </label>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>行数: {testText.split('\n').filter(line => line.trim()).length}</span>
              <span>•</span>
              <span>字符: {testText.length}</span>
            </div>
          </div>
          <div className="relative">
            <textarea
              id="test-text"
              rows={10}
              className="block w-full px-4 py-3 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200"
              value={testText}
              onChange={(e) => onTestTextChange(e.target.value)}
              placeholder="请输入需要测试的对话文本，每行一句话...&#10;&#10;示例：&#10;客服：您好，欢迎来到我们公司&#10;客户：我想咨询产品&#10;客服：您好，欢迎来到我们公司（重复内容）"
            />
            {testText.trim() && (
              <button
                onClick={() => {
                  onTestTextChange('');
                  onClearResult();
                }}
                className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="清空文本"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          <div className="text-xs text-gray-500 flex items-center space-x-4">
            <span>💡 提示：每行代表一句对话，使用"客服："或"客户："开头</span>
          </div>
        </div>

        {/* 快速测试按钮 */}
        <div className="flex space-x-3">
          <button
            onClick={onRunTest}
            disabled={!testText.trim()}
            className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <span className="flex items-center justify-center space-x-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>执行测试</span>
            </span>
          </button>
          {testResult && (
            <button
              onClick={onClearResult}
              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              清除结果
            </button>
          )}
        </div>
      </div>
      
      {/* 测试结果 */}
      {testResult && (
        <div className="mt-8 space-y-6">
          {/* 结果总览 */}
          <div className="border-t border-gray-200 pt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <span>📊</span>
                <span>测试结果</span>
              </h3>
              <button
                onClick={onClearResult}
                className="text-sm text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* 主要结果卡片 */}
            <div className={`relative overflow-hidden rounded-xl border-2 ${
              testResult.matched 
                ? 'bg-gradient-to-r from-red-50 to-orange-50 border-red-200' 
                : 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'
            }`}>
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                    testResult.matched ? 'bg-red-100' : 'bg-green-100'
                  }`}>
                    <span className={`text-2xl ${testResult.matched ? 'text-red-600' : 'text-green-600'}`}>
                      {testResult.matched ? '⚠️' : '✓'}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className={`text-lg font-bold ${testResult.matched ? 'text-red-800' : 'text-green-800'}`}>
                      {testResult.matched ? '检测到重复内容' : '未检测到重复内容'}
                    </div>
                    <div className={`text-sm mt-1 ${testResult.matched ? 'text-red-700' : 'text-green-700'}`}>
                      {testResult.details}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      testResult.matched 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {testResult.matched ? '⚠️ 违规' : '✅ 正常'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 详细结果 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 重复内容详情 */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-red-500">🔍</span>
                <h4 className="font-semibold text-gray-800">重复内容</h4>
                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                  {testResult.repeatedSentences.length} 项
                </span>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {testResult.repeatedSentences.length > 0 ? (
                  testResult.repeatedSentences.map((item, i) => (
                    <div key={i} className="p-3 bg-red-50 border border-red-100 rounded-md">
                      <div className="text-sm text-gray-800 font-medium mb-1">
                        当前句子：{item.current}
                      </div>
                      <div className="text-sm text-gray-600 mb-1">
                        相似句子：{item.similar}
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-red-600">
                          相似度：{(item.similarity * 100).toFixed(1)}%
                        </span>
                        <span className="text-orange-600">
                          重复次数：{item.repeatCount}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500 text-center py-4">
                    未发现重复内容
                  </div>
                )}
              </div>
            </div>

            {/* 统计信息 */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-purple-500">📈</span>
                <h4 className="font-semibold text-gray-800">统计信息</h4>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">过滤短句数</span>
                  <span className="font-mono text-lg font-bold text-purple-600">
                    {testResult.filteredSentences.length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">例外句子命中</span>
                  <span className="font-mono text-lg font-bold text-purple-600">
                    {testResult.exceptionMatches.length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">检测范围</span>
                  <span className="font-mono text-lg font-bold text-purple-600">
                    {config.withinSentences}句内
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">相似度阈值</span>
                  <span className="font-mono text-lg font-bold text-purple-600">
                    {(config.similarityThreshold * 100).toFixed(0)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 过滤的短句 */}
          {testResult.filteredSentences.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-gray-500">🚫</span>
                <h4 className="font-semibold text-gray-800">过滤的短句</h4>
                <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                  {testResult.filteredSentences.length} 条
                </span>
              </div>
              <div className="flex flex-wrap gap-2">
                {testResult.filteredSentences.map((sentence, i) => (
                  <span key={i} className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                    {sentence}
                  </span>
                ))}
              </div>
              <div className="mt-2 text-xs text-gray-500">
                💡 少于{config.minWordCount}个字的句子已被过滤，不参与重复检测
              </div>
            </div>
          )}

          {/* 例外句子命中 */}
          {testResult.exceptionMatches.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <span className="text-yellow-500">⭐</span>
                <h4 className="font-semibold text-gray-800">例外句子命中</h4>
                <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                  {testResult.exceptionMatches.length} 条
                </span>
              </div>
              <div className="flex flex-wrap gap-2">
                {testResult.exceptionMatches.map((sentence, i) => (
                  <span key={i} className="inline-block bg-yellow-100 text-yellow-700 text-xs px-2 py-1 rounded">
                    {sentence}
                  </span>
                ))}
              </div>
              <div className="mt-2 text-xs text-gray-500">
                💡 这些句子匹配了例外规则，即使重复也不算违规
              </div>
            </div>
          )}

          {/* 配置摘要 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-gray-500">⚙️</span>
              <h4 className="font-semibold text-gray-800">当前配置</h4>
            </div>
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">检测角色:</span>
                  <span className="font-medium">{config.detectionRole === 'agent' ? '客服' : config.detectionRole === 'customer' ? '客户' : '所有角色'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">检测范围:</span>
                  <span className="font-medium">{config.detectionScope === 'full' ? '全文' : `第${config.rangeStart}-${config.rangeEnd}句`}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">检测窗口:</span>
                  <span className="font-medium">{config.withinSentences}句内</span>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">相似度阈值:</span>
                  <span className="font-medium">{(config.similarityThreshold * 100).toFixed(0)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">违规次数:</span>
                  <span className="font-medium">第{config.violationCount}次</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">最小字数:</span>
                  <span className="font-medium">{config.minWordCount}字</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export { calculateSimilarity };
export default ContextRepeatTester; 