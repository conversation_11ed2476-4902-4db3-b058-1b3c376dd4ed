import React from 'react';
import { ContextRepeatConfig } from './ContextRepeatConfig';

interface ContextRepeatCase {
  name: string;
  description: string;
  config: ContextRepeatConfig;
  testText: string;
  expectedResult: string;
}

interface ContextRepeatCasesProps {
  onLoadCase: (config: ContextRepeatConfig, testText: string) => void;
}

/**
 * 上下文重复检查案例库组件
 * 提供预设案例供用户学习和测试
 */
const ContextRepeatCases: React.FC<ContextRepeatCasesProps> = ({ onLoadCase }) => {
  const cases: ContextRepeatCase[] = [
    {
      name: '客户投诉重复检测',
      description: '检测客户在3句话内是否有重复表达不满（参考文档示例）',
      config: {
        detectionRole: 'customer',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 5,
        withinSentences: 3,
        similarityThreshold: 0.8,
        violationCount: 2,
        minWordCount: 2,
        exceptionSentences: []
      },
      testText: '客服：您好，请问有什么可以帮助您的？\n客户：你们的服务太差了！\n客服：很抱歉给您带来不便，请告诉我具体问题。\n客户：你们的服务真的很差！\n客服：我会尽快为您解决。\n客户：服务太差了，我要投诉！',
      expectedResult: '应该检测到重复（客户3次表达服务差的不满）'
    },
    {
      name: '客服标准回复监控',
      description: '检测客服是否过度使用相同的标准回复',
      config: {
        detectionRole: 'agent',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 10,
        withinSentences: 4,
        similarityThreshold: 0.9,
        violationCount: 2,
        minWordCount: 3,
        exceptionSentences: ['您好', '谢谢']
      },
      testText: '客服：您好，请问有什么可以帮助您的？\n客户：我想了解产品信息。\n客服：好的，我为您详细介绍我们的产品。\n客户：价格如何？\n客服：好的，我为您详细介绍我们的产品。\n客户：有优惠吗？\n客服：好的，我为您详细介绍我们的产品。',
      expectedResult: '应该检测到重复（客服重复使用相同回复）'
    },
    {
      name: '短句过滤演示',
      description: '演示少于2个字的句子不被检测的功能',
      config: {
        detectionRole: 'all',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 10,
        withinSentences: 3,
        similarityThreshold: 0.8,
        violationCount: 1,
        minWordCount: 2,
        exceptionSentences: []
      },
      testText: '客服：您好，请问有什么可以帮助您的？\n客户：嗯\n客服：请详细说明您的需求。\n客户：嗯\n客服：我明白了。\n客户：好\n客服：还有其他问题吗？\n客户：好',
      expectedResult: '不应该检测到重复（"嗯"、"好"等短句被过滤）'
    },
    {
      name: '例外句子演示',
      description: '演示例外句子不被检测为重复的功能',
      config: {
        detectionRole: 'agent',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 10,
        withinSentences: 5,
        similarityThreshold: 0.8,
        violationCount: 1,
        minWordCount: 2,
        exceptionSentences: ['谢谢您的咨询', '很高兴为您服务']
      },
      testText: '客服：您好，请问有什么可以帮助您的？\n客户：我想了解产品。\n客服：谢谢您的咨询，我来为您介绍。\n客户：价格如何？\n客服：谢谢您的咨询，价格是300元。\n客户：我考虑一下。\n客服：很高兴为您服务，随时欢迎您。',
      expectedResult: '不应该检测到重复（感谢用语在例外列表中）'
    },
    {
      name: '高相似度检测',
      description: '演示高相似度阈值的严格匹配效果',
      config: {
        detectionRole: 'customer',
        detectionScope: 'full',
        rangeStart: 1,
        rangeEnd: 10,
        withinSentences: 3,
        similarityThreshold: 0.95,
        violationCount: 1,
        minWordCount: 3,
        exceptionSentences: []
      },
      testText: '客服：您好，请问有什么需要帮助的？\n客户：我想买你们的产品\n客服：好的，请问您需要哪种产品？\n客户：我想购买你们的产品\n客服：明白了，我为您推荐。\n客户：我要买贵公司的产品',
      expectedResult: '应该检测到部分重复（高阈值下只有非常相似的才算重复）'
    },
    {
      name: '指定范围检测',
      description: '演示只在特定句子范围内检测重复的功能',
      config: {
        detectionRole: 'all',
        detectionScope: 'range',
        rangeStart: 2,
        rangeEnd: 5,
        withinSentences: 3,
        similarityThreshold: 0.8,
        violationCount: 1,
        minWordCount: 3,
        exceptionSentences: []
      },
      testText: '客服：您好，欢迎来到我们公司。\n客户：我想了解你们的产品信息。\n客服：好的，我为您详细介绍。\n客户：我想了解你们的产品信息。\n客服：没问题，我再为您说明。\n客户：价格如何？\n客服：我想了解你们的产品信息。',
      expectedResult: '应该检测到重复（只检测第2-5句，第6句不在范围内）'
    }
  ];

  const [selectedCase, setSelectedCase] = React.useState<number | null>(null);

  const handleLoadCase = (caseData: ContextRepeatCase, index: number) => {
    setSelectedCase(index);
    onLoadCase(caseData.config, caseData.testText);
    
    // 显示加载成功提示
    setTimeout(() => setSelectedCase(null), 1000);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">📂</span>
        <h2 className="text-xl font-semibold text-gray-900">
          案例库
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          实战演练
        </span>
      </div>
      
      <div className="space-y-4">
        {cases.map((caseData, index) => (
          <div key={index} className="group relative p-4 rounded-lg bg-gray-50 border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-all duration-200">
            <div className="flex justify-between items-start">
              <div className="flex-1 pr-4">
                <div className="flex items-center space-x-2 mb-2">
                  <h4 className="font-semibold text-gray-800 group-hover:text-blue-800 transition-colors">
                    {caseData.name}
                  </h4>
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    案例 {index + 1}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-3 group-hover:text-blue-700 transition-colors">
                  {caseData.description}
                </p>
                
                {/* 配置预览 */}
                <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-3">
                  <div>
                    <span className="font-medium">检测角色：</span>
                    {caseData.config.detectionRole === 'agent' ? '客服' : 
                     caseData.config.detectionRole === 'customer' ? '客户' : '所有角色'}
                  </div>
                  <div>
                    <span className="font-medium">相似度阈值：</span>
                    {(caseData.config.similarityThreshold * 100).toFixed(0)}%
                  </div>
                  <div>
                    <span className="font-medium">检测窗口：</span>
                    {caseData.config.withinSentences}句内
                  </div>
                  <div>
                    <span className="font-medium">违规次数：</span>
                    第{caseData.config.violationCount}次
                  </div>
                </div>

                <div className="text-xs text-blue-600 group-hover:text-blue-700 transition-colors">
                  <span className="font-medium">预期结果：</span>{caseData.expectedResult}
                </div>
              </div>
              
              <button 
                onClick={() => handleLoadCase(caseData, index)}
                disabled={selectedCase === index}
                className={`flex-shrink-0 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                  selectedCase === index
                    ? 'bg-green-500 text-white cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700'
                }`}
              >
                {selectedCase === index ? (
                  <span className="flex items-center space-x-1">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>已加载</span>
                  </span>
                ) : (
                  '加载案例'
                )}
              </button>
            </div>
            
            {/* 悬停显示测试文本预览 */}
            <div className="absolute left-0 top-full mt-2 w-full bg-white border border-gray-200 rounded-lg shadow-lg p-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
              <h5 className="font-medium text-gray-800 mb-2 text-xs">测试文本预览：</h5>
              <div className="text-xs text-gray-600 max-h-24 overflow-y-auto">
                {caseData.testText.split('\n').map((line, i) => (
                  <div key={i} className={`${line.startsWith('客服：') ? 'text-blue-600' : line.startsWith('客户：') ? 'text-green-600' : ''}`}>
                    {line}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <span className="text-lg">📖</span>
          </div>
          <div>
            <h4 className="font-semibold text-blue-900 mb-2">
              使用说明
            </h4>
            <div className="text-blue-800 text-sm space-y-1">
              <p>• <strong>点击"加载案例"</strong>：将案例配置和测试文本自动填入主界面</p>
              <p>• <strong>悬停查看预览</strong>：鼠标悬停在案例上可查看完整测试文本</p>
              <p>• <strong>配置参数说明</strong>：每个案例都展示了关键配置参数</p>
              <p>• <strong>预期结果参考</strong>：了解每个案例的预期检测效果</p>
              <p>• <strong>循序渐进学习</strong>：从简单案例开始，逐步掌握复杂功能</p>
            </div>
          </div>
        </div>
      </div>

      {/* 案例分类说明 */}
      <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <h5 className="font-semibold text-green-800 mb-2 text-sm">🎯 基础功能案例</h5>
          <ul className="text-xs text-green-700 space-y-1">
            <li>• 客户投诉重复检测</li>
            <li>• 客服标准回复监控</li>
            <li>• 指定范围检测</li>
          </ul>
        </div>
        <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
          <h5 className="font-semibold text-purple-800 mb-2 text-sm">⚡ 高级功能案例</h5>
          <ul className="text-xs text-purple-700 space-y-1">
            <li>• 短句过滤演示</li>
            <li>• 例外句子演示</li>
            <li>• 高相似度检测</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ContextRepeatCases; 