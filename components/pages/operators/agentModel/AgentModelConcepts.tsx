import React from 'react';

/**
 * AgentModel核心概念组件
 * @returns {React.ReactElement}
 */
export const AgentModelConcepts: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          核心概念
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          基础知识
        </span>
      </div>
      
      <div className="space-y-6">
        {/* 功能介绍 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">🎯</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">功能介绍</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                检测客服常见的异常情绪或违规表达，由系统内置的算法模型进行分析，无需人工配置规则。目前可检测的类型有：反问反感、引导投诉、推诿、辱骂。
              </p>
            </div>
          </div>
        </div>

        {/* 应用场景 */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-lg">🏢</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">应用场景</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">客服服务态度监控</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">识别消极服务行为</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">发现潜在客诉风险</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">规范客服用语</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 优势特点 */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-lg">⭐</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">优势特点</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">智能语义理解</span>
                    <span className="text-xs text-gray-500 ml-1">- 超越关键词匹配</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">无需手动配置</span>
                    <span className="text-xs text-gray-500 ml-1">- 开箱即用</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">覆盖场景更广</span>
                    <span className="text-xs text-gray-500 ml-1">- 泛化能力强</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 检测原理 */}
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-lg">🔧</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">检测原理</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                算子将客服角色的对话文本输入到预先训练好的意图识别模型中。模型基于深度学习和自然语言处理技术，对文本的语义进行分析和分类，最终输出最匹配的违规类型（如"推诿"）。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 