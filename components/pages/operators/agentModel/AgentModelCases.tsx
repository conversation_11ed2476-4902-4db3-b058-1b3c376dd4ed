import React from 'react';
import { AgentModelConfigType, DetectionType } from '../AgentModelPage';

/**
 * @typedef {object} Case
 * @property {string} name
 * @property {string} description
 * @property {DetectionType} detectionType
 * @property {string} testText
 */
interface Case {
  name: string;
  description: string;
  detectionType: DetectionType;
  testText: string;
}

/**
 * @typedef {object} Props
 * @property {React.Dispatch<React.SetStateAction<AgentModelConfigType>>} setConfig
 * @property {(text: string) => void} setTestText
 */
interface Props {
  setConfig: React.Dispatch<React.SetStateAction<AgentModelConfigType>>;
  setTestText: (text: string) => void;
}

const cases: Case[] = [
  {
    name: '客服反问反感检测',
    description: '检测客服使用不耐烦的反问语气。',
    detectionType: '反问反感',
    testText: '客户：我的订单怎么还没到？\n客服：我不是跟你说了吗，正在处理中。'
  },
  {
    name: '客服引导投诉检测',
    description: '检测客服用消极方式处理客户抱怨。',
    detectionType: '引导投诉',
    testText: '客户：你们的服务太差了！\n客服：那你去投诉呗。'
  },
  {
    name: '客服推诿责任检测',
    description: '检测客服回避问题、推卸责任。',
    detectionType: '推诿',
    testText: '客户：这个优惠券怎么用不了？\n客服：这个和我没关系啊，是系统问题。'
  },
  {
    name: '客服辱骂行为检测',
    description: '检测客服使用侮辱性词汇。',
    detectionType: '辱骂',
    testText: '客户：你到底懂不懂！\n客服：你才是混蛋！'
  }
];

/**
 * 客服模型检测案例库组件
 * @param {Props} props
 * @returns {React.ReactElement}
 */
export const AgentModelCases: React.FC<Props> = ({ setConfig, setTestText }) => {
  const loadCase = (c: Case) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      detectionType: c.detectionType
    }));
    setTestText(c.testText);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        📂 案例库
      </h2>
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500 mt-1">{c.description}</p>
              </div>
              <button 
                onClick={() => loadCase(c)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}; 