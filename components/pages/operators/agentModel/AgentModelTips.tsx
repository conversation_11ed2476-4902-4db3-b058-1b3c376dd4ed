import React from 'react';

/**
 * 客服模型检测使用提示组件
 * @returns {React.ReactElement}
 */
export const AgentModelTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          使用提示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        {/* 基础操作提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <h4 className="font-semibold text-gray-800 mb-2">💡 基础操作</h4>
          <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
            <li>在"配置演示"中选择一个希望检测的客服违规类型。</li>
            <li>在"实时测试"中输入对话文本，点击"执行测试"查看模型是否能识别出对应行为。</li>
            <li>可点击"加载"按钮，快速使用"案例库"中的预设场景进行测试。</li>
          </ul>
        </div>

        {/* 最佳实践 */}
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <h4 className="font-semibold text-gray-800 mb-2">🎯 最佳实践</h4>
          <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
            <li>用于自动化监控客服的服务态度和用语规范，是提升服务质量的重要工具。</li>
            <li>可将"反问反感"、"推诿"等模型与关键词检查组合，定位具体的消极服务场景。</li>
            <li>定期回顾模型命中的案例，作为客服培训和个人提升的素材。</li>
          </ul>
        </div>

        {/* 注意事项 */}
        <div className="bg-gradient-to-r from-red-50 to-rose-50 rounded-lg p-4 border border-red-100">
          <h4 className="font-semibold text-gray-800 mb-2">⚠️ 注意事项</h4>
          <ul className="list-disc list-inside text-sm text-gray-700 space-y-1">
            <li>模型检测结果依赖于算法，可能存在少量误判或漏判，属于正常现象。</li>
            <li>测试文本应尽量模拟真实对话场景，以获得更准确的测试结果。</li>
            <li>此模型仅针对"客服"角色生效，无法检测客户或其他角色的意图。</li>
          </ul>
        </div>
      </div>
    </div>
  );
}; 