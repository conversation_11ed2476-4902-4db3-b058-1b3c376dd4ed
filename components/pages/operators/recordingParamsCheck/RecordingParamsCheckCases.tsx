import React from 'react';
import { ParamCondition } from './RecordingParamsCheckConfig';

/**
 * 单个案例的接口定义
 */
interface Case {
  name: string;
  description: string;
  config: {
    conditions: ParamCondition[];
  };
  testParams: object;
  expectedResult: string;
}

/**
 * 案例库组件Props的接口定义
 */
interface RecordingParamsCheckCasesProps {
  loadCase: (config: any, testParams: any) => void;
}

/**
 * 随录参数检查 - 案例库组件
 * @param {RecordingParamsCheckCasesProps} props
 * @constructor
 */
export const RecordingParamsCheckCases: React.FC<RecordingParamsCheckCasesProps> = ({ loadCase }) => {
  const cases: Case[] = [
    {
      name: '检测特定业务线',
      description: '检测随录参数"业务线"是否为"华东"或"华南"',
      config: {
        conditions: [
          {
            id: 'cond1',
            type: 'system',
            name: '业务线',
            operator: '=',
            values: ['华东', '华南'],
          },
        ],
      },
      testParams: { '业务线': '华东' },
      expectedResult: '应该命中 (业务线为华东)',
    },
    {
      name: '排除特定客户级别',
      description: '检测"客户级别"参数不是"VIP"',
      config: {
        conditions: [
          {
            id: 'cond1',
            type: 'system',
            name: '客户级别',
            operator: '!=',
            values: ['VIP'],
          },
        ],
      },
      testParams: { '客户级别': '普通' },
      expectedResult: '应该命中 (客户级别为普通)',
    },
     {
      name: '自定义字段包含检查',
      description: '检测自定义字段"tags"是否包含"投诉"',
      config: {
        conditions: [
          {
            id: 'cond1',
            type: 'custom',
            name: 'tags',
            operator: 'contains',
            values: ['投诉'],
          },
        ],
      },
      testParams: { 'tags': '这个客户有投诉历史' },
      expectedResult: '应该命中 (tags字段包含投诉)',
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        📂 案例库
      </h2>
      <div className="space-y-4">
        {cases.map((c: Case, index: number) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-800">{c.name}</h4>
                <p className="text-xs text-gray-500 mt-1">{c.description}</p>
                <p className="text-xs text-blue-500 mt-1">预期结果：{c.expectedResult}</p>
              </div>
              <button
                onClick={() => loadCase(c.config, c.testParams)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}; 