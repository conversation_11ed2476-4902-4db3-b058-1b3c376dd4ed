import React from 'react';

/**
 * 单个参数条件的接口定义
 */
export interface ParamCondition {
  id: string;
  type: 'system' | 'custom';
  name: string;
  operator: '=' | '!=' | 'contains' | 'not_contains';
  values: string[];
}

/**
 * 配置组件的Props接口定义
 */
export interface RecordingParamsCheckConfigProps {
  config: {
    conditions: ParamCondition[];
  };
  setConfig: React.Dispatch<React.SetStateAction<any>>;
}

/**
 * 随录参数检查 - 配置组件
 * @param {RecordingParamsCheckConfigProps} props
 * @constructor
 */
export const RecordingParamsCheckConfig: React.FC<RecordingParamsCheckConfigProps> = ({ config, setConfig }) => {
  const systemFields = ['业务线', '技能组', '客户级别', '订单来源'];
  const operators = [
    { value: '=', label: '=' },
    { value: '!=', label: '≠' },
    { value: 'contains', label: '包含' },
    { value: 'not_contains', label: '不包含' },
  ];

  /**
   * @param {string} conditionId
   * @param {Partial<ParamCondition>} newValues
   */
  const updateCondition = (conditionId: string, newValues: Partial<ParamCondition>) => {
    setConfig((prev: any) => ({
      ...prev,
      conditions: prev.conditions.map((c: ParamCondition) =>
        c.id === conditionId ? { ...c, ...newValues } : c
      ),
    }));
  };

  /**
   * @param {string} conditionId
   * @param {React.KeyboardEvent<HTMLInputElement>} e
   */
  const handleValueAdd = (conditionId: string, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const target = e.target as HTMLInputElement;
      const value = target.value.trim();
      if (value) {
        const condition = config.conditions.find((c: ParamCondition) => c.id === conditionId);
        if (condition && !condition.values.includes(value)) {
          updateCondition(conditionId, { values: [...condition.values, value] });
        }
        target.value = '';
      }
    }
  };
  
  /**
   * @param {string} conditionId
   * @param {string} valueToRemove
   */
  const handleValueRemove = (conditionId: string, valueToRemove: string) => {
    const condition = config.conditions.find((c: ParamCondition) => c.id === conditionId);
    if(condition) {
        updateCondition(conditionId, { values: condition.values.filter((v: string) => v !== valueToRemove) });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">
          配置演示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>

      <div className="space-y-4">
        {config.conditions.map((condition: ParamCondition) => (
          <div key={condition.id} className="p-4 border border-gray-200 rounded-lg space-y-4 bg-gray-50">
            <div className="flex items-center space-x-4">
                <label className="w-24 text-sm font-medium text-gray-700 text-right">参数选择</label>
                <div className="flex items-center space-x-2 flex-1">
                    <select
                        className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={condition.type}
                        onChange={(e) => updateCondition(condition.id, { type: e.target.value as 'system' | 'custom', name: ''})}
                    >
                        <option value="system">系统常规字段</option>
                        <option value="custom">更多自定义字段</option>
                    </select>

                    {condition.type === 'system' ? (
                         <select
                            className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            value={condition.name}
                            onChange={(e) => updateCondition(condition.id, { name: e.target.value })}
                        >
                            <option value="" disabled>请选择</option>
                            {systemFields.map(field => (
                                <option key={field} value={field}>{field}</option>
                            ))}
                        </select>
                    ) : (
                        <input
                            type="text"
                            placeholder="输入自定义字段名"
                            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            value={condition.name}
                            onChange={(e) => updateCondition(condition.id, { name: e.target.value })}
                        />
                    )}
                   
                    <select
                        className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        value={condition.operator}
                        onChange={(e) => updateCondition(condition.id, { operator: e.target.value as any})}
                    >
                        {operators.map(op => (
                            <option key={op.value} value={op.value}>{op.label}</option>
                        ))}
                    </select>

                    <div className="flex-1">
                        <input
                        type="text"
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入完成后请按回车"
                        onKeyDown={(e) => handleValueAdd(condition.id, e)}
                        />
                    </div>
                </div>
            </div>
             {condition.values.length > 0 && (
                <div className="flex items-center space-x-4">
                    <div className="w-24"></div>
                    <div className="flex flex-wrap gap-2">
                        {condition.values.map((value: string) => (
                        <span key={value} className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                            {value}
                            <button
                                onClick={() => handleValueRemove(condition.id, value)}
                                className="ml-1.5 text-blue-600 hover:text-blue-800"
                            >
                                ×
                            </button>
                        </span>
                        ))}
                    </div>
                </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}; 