import React from 'react';

/**
 * 语速检查使用提示组件
 * 提供使用技巧和最佳实践建议
 */
const SpeechSpeedTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          使用提示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        {/* 时间格式提示 */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⏰</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">时间格式说明</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">标准格式：</span>
                    <span className="text-xs text-gray-500 block">[00:05-00:08] 表示5秒到8秒</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">简化格式：</span>
                    <span className="text-xs text-gray-500 block">[5-8] 表示5秒到8秒</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">注意事项：</span>
                    <span className="text-xs text-gray-500 block">时间戳必须准确，结束时间应大于开始时间</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 配置建议 */}
        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">配置建议</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">语速阈值设置</span>
                    <span className="text-xs text-gray-500 block">客服一般设置250-300字/分钟，销售可适当提高到350字/分钟</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">最小字数过滤</span>
                    <span className="text-xs text-gray-500 block">建议设置4-5字，过滤"好的"、"嗯"等无意义短句</span>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2"></span>
                  <div>
                    <span className="text-sm font-medium text-gray-700">检测方式选择</span>
                    <span className="text-xs text-gray-500 block">质检用单句检测，培训用平均检测</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 快速开始 */}
        <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4 border border-indigo-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
              <span className="text-indigo-600 text-sm">🚀</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">快速开始</h4>
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤1</span>
                <span>选择预设案例</span>
                <span>→</span>
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤2</span>
                <span>点击加载</span>
                <span>→</span>
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤3</span>
                <span>执行测试查看结果</span>
                <span>→</span>
                <span className="bg-indigo-200 text-indigo-800 px-2 py-1 rounded">步骤4</span>
                <span>调整配置优化效果</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpeechSpeedTips; 