import React from 'react';

/**
 * 语速检查核心概念组件
 * 介绍语速检查的基本概念、应用场景和优势特点
 */
const SpeechSpeedConcepts: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">
          核心概念
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
          基础知识
        </span>
      </div>
      
      <div className="space-y-6">
        {/* 功能介绍 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">🎯</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">功能介绍</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                检测对话中的语速是否过快或过慢，通常用来检测客服语速是否超过标准范围。
                支持单句话语速检测和整个对话平均语速检测，可以设置字数阈值避免短句干扰。
                帮助提升客服服务质量，确保客户能够清楚理解服务内容。
              </p>
            </div>
          </div>
        </div>

        {/* 应用场景 */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-lg">🏢</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">应用场景</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">客服服务质量评估</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">电话销售规范检查</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">培训效果监控</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span className="text-sm text-gray-700">用户体验优化</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 优势特点 */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-lg">⭐</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">优势特点</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">智能语速计算</span>
                    <span className="text-xs text-gray-500 ml-1">- 精确到每分钟字数</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">灵活检测模式</span>
                    <span className="text-xs text-gray-500 ml-1">- 单句或整体分析</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">自动过滤短句</span>
                    <span className="text-xs text-gray-500 ml-1">- 避免短句干扰结果</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                    <span className="text-purple-600 text-xs">✓</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-800">实时检测反馈</span>
                    <span className="text-xs text-gray-500 ml-1">- 即时发现语速问题</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 检测逻辑说明 */}
        <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4 border border-orange-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-orange-600 text-lg">🔍</span>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-3">检测逻辑说明</h3>
              <div className="space-y-3">
                <div className="bg-white/60 rounded-md p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                    <span className="text-sm font-medium text-gray-800">语速计算公式</span>
                  </div>
                  <p className="text-sm text-gray-600 ml-4">
                    语速(字/分钟) = 字符数 ÷ 时长(秒) × 60
                  </p>
                </div>
                <div className="bg-white/60 rounded-md p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                    <span className="text-sm font-medium text-gray-800">单句检测模式</span>
                  </div>
                  <p className="text-sm text-gray-600 ml-4">
                    逐句计算语速，任一句超过阈值即触发规则
                  </p>
                </div>
                <div className="bg-white/60 rounded-md p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                    <span className="text-sm font-medium text-gray-800">平均检测模式</span>
                  </div>
                  <p className="text-sm text-gray-600 ml-4">
                    计算整个对话的平均语速，超过阈值即触发规则
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpeechSpeedConcepts; 