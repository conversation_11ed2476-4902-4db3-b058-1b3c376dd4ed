import React from 'react';

interface SpeechSpeedConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  speedThreshold: number;
  minCharCount: number;
  checkMode: 'single' | 'average';
}

interface SpeechSpeedCasesProps {
  onLoadCase: (config: SpeechSpeedConfig, testText: string) => void;
}

/**
 * 语速检查案例库组件
 * 提供预设的测试案例
 */
const SpeechSpeedCases: React.FC<SpeechSpeedCasesProps> = ({ onLoadCase }) => {
  // 预设案例
  const cases = [
    {
      name: '客服语速过快检测',
      description: '检测客服语速是否超过300字/分钟（标准配置）',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        speedThreshold: 300,
        minCharCount: 4,
        checkMode: 'single' as const
      },
      testText: '[00:05-00:08] 客服：您好欢迎致电我们公司客服热线我是客服小王很高兴为您服务请问有什么可以帮助您的吗今天天气不错希望您心情愉快\n[00:09-00:12] 客户：我想咨询一下产品价格\n[00:13-00:16] 客服：好的请稍等我来为您查询相关信息\n[00:17-00:20] 客户：谢谢',
      expectedResult: '应该命中（第一句语速约450字/分钟，超过300字/分钟阈值）'
    },
    {
      name: '平均语速检测',
      description: '检测整个对话的平均语速',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        speedThreshold: 250,
        minCharCount: 4,
        checkMode: 'average' as const
      },
      testText: '[00:05-00:10] 客服：您好，欢迎来电咨询，我是客服专员\n[00:11-00:15] 客户：我想了解产品信息\n[00:16-00:25] 客服：好的，我们的产品有多种规格和型号，价格也不同，您可以根据需求选择适合的产品\n[00:26-00:30] 客户：那价格大概是多少',
      expectedResult: '应该命中（客服整体平均语速约270字/分钟，超过250字/分钟）'
    },
    {
      name: '短句过滤测试',
      description: '验证短句过滤功能，不检测少于4字的句子',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        speedThreshold: 200,
        minCharCount: 4,
        checkMode: 'single' as const
      },
      testText: '[00:05-00:06] 客服：好\n[00:07-00:08] 客户：嗯\n[00:09-00:15] 客服：我来为您详细介绍一下我们的产品服务\n[00:16-00:17] 客户：好的',
      expectedResult: '应该命中（第三句语速约240字/分钟，短句"好"、"嗯"被正确过滤）'
    },
    {
      name: '客户语速监控',
      description: '监控客户语速是否异常（可能情绪激动）',
      config: {
        detectionRole: 'customer' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        speedThreshold: 350,
        minCharCount: 5,
        checkMode: 'single' as const
      },
      testText: '[00:05-00:08] 客服：请问有什么可以帮助您的\n[00:09-00:11] 客户：我的订单出了问题你们必须马上给我解决这个问题太严重了影响我的使用\n[00:12-00:15] 客服：好的，我来帮您查询\n[00:16-00:18] 客户：快点查询',
      expectedResult: '应该命中（客户第二句语速约510字/分钟，可能情绪激动）'
    },
    {
      name: '指定范围检测',
      description: '只检测前3句对话的语速',
      config: {
        detectionRole: 'agent' as const,
        detectionScope: 'range' as const,
        rangeStart: 1,
        rangeEnd: 3,
        speedThreshold: 280,
        minCharCount: 4,
        checkMode: 'single' as const
      },
      testText: '[00:05-00:07] 客服：您好欢迎致电我们公司客服热线\n[00:08-00:10] 客户：我想咨询产品\n[00:11-00:13] 客服：好的请稍等我来查询\n[00:14-00:20] 客服：这是一段很长的介绍文本用来测试后面的句子不会被检测到因为超出了范围',
      expectedResult: '应该命中（前3句中客服语速约360字/分钟，第4句不在检测范围内）'
    },
    {
      name: '正常语速通话',
      description: '正常语速的通话，不应触发警告',
      config: {
        detectionRole: 'all' as const,
        detectionScope: 'full' as const,
        rangeStart: 1,
        rangeEnd: 5,
        speedThreshold: 300,
        minCharCount: 4,
        checkMode: 'single' as const
      },
      testText: '[00:05-00:10] 客服：您好，欢迎来电咨询\n[00:11-00:15] 客户：我想了解产品信息\n[00:16-00:22] 客服：好的，我来为您介绍\n[00:23-00:28] 客户：谢谢，请详细说明',
      expectedResult: '不应该命中（所有语速都在正常范围内，约180-240字/分钟）'
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        📂 案例库
      </h2>
      <div className="space-y-4">
        {cases.map((c, index) => (
          <div key={index} className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex justify-between items-start">
              <div className="flex-1 pr-4">
                <h4 className="font-semibold text-gray-800 mb-2">{c.name}</h4>
                <p className="text-xs text-gray-600 mb-2">{c.description}</p>
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xs text-blue-600 font-medium">配置：</span>
                  <span className="text-xs text-gray-500">
                    {(() => {
                      const detectionRole = c.config.detectionRole as 'agent' | 'customer' | 'all';
                      const checkMode = c.config.checkMode as 'single' | 'average';
                      
                      const role = detectionRole === 'agent' ? '客服' : detectionRole === 'customer' ? '客户' : '所有角色';
                      const mode = checkMode === 'single' ? '单句检测' : '平均检测';
                      const scope = c.config.detectionScope === 'range' ? `前${c.config.rangeEnd}句` : '全文';
                      
                      return `${role} | ${scope} | ${mode} | >${c.config.speedThreshold}字/分钟 | >=${c.config.minCharCount}字`;
                    })()}
                  </span>
                </div>
                <p className="text-xs text-green-600 font-medium">预期结果：{c.expectedResult}</p>
              </div>
              <button 
                onClick={() => onLoadCase(c.config, c.testText)}
                className="text-sm bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors flex-shrink-0"
              >
                加载
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SpeechSpeedCases; 