import React from 'react';

interface SpeechSpeedConfig {
  detectionRole: 'agent' | 'customer' | 'all';
  detectionScope: 'full' | 'range';
  rangeStart: number;
  rangeEnd: number;
  speedThreshold: number;
  minCharCount: number;
  checkMode: 'single' | 'average';
}

interface SpeechSpeedConfigProps {
  config: SpeechSpeedConfig;
  setConfig: (config: SpeechSpeedConfig) => void;
}

/**
 * 语速检查配置演示组件
 * 提供完整的配置界面和规则说明
 */
const SpeechSpeedConfig: React.FC<SpeechSpeedConfigProps> = ({ config, setConfig }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">⚙️</span>
        <h2 className="text-xl font-semibold text-gray-900">
          配置演示
        </h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          交互配置
        </span>
      </div>
      
      {/* 配置规则说明 */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 text-lg">📋</span>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900 mb-3">配置规则总结</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🎯 检测角色</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>客服</strong>：仅检测"客服："开头的对话</li>
                    <li>• <strong>客户</strong>：仅检测"客户："开头的对话</li>
                    <li>• <strong>所有角色</strong>：检测全部对话内容</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">📍 检测范围</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>全文</strong>：检测所有符合角色条件的句子</li>
                    <li>• <strong>指定范围</strong>：只检测第X~Y句之间的内容</li>
                  </ul>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">⚡ 语速阈值</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• 设置每分钟字数上限（一般250-350字）</li>
                    <li>• 超过阈值触发规则检测</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">📝 最小字数</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• 低于此字数的句子不参与检测</li>
                    <li>• 避免短句如"好的"、"嗯"等干扰结果</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium text-gray-800 mb-1">🔍 检查方式</h5>
                  <ul className="text-gray-600 space-y-1 ml-3">
                    <li>• <strong>单句检测</strong>：任一句超过阈值即触发</li>
                    <li>• <strong>平均检测</strong>：整体平均语速超过阈值</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        {/* 检测角色 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测角色</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionRole}
            onChange={(e) => setConfig({ ...config, detectionRole: e.target.value as any })}
          >
            <option value="agent">客服</option>
            <option value="customer">客户</option>
            <option value="all">所有角色</option>
          </select>
        </div>

        {/* 检测范围 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检测范围</label>
          <select 
            className="flex-1 max-w-xs px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={config.detectionScope}
            onChange={(e) => setConfig({ ...config, detectionScope: e.target.value as any })}
          >
            <option value="full">全文</option>
            <option value="range">指定范围</option>
          </select>
        </div>

        {/* 检查逻辑 */}
        <div className="flex items-start space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right mt-2">检查逻辑</label>
          <div className="flex-1">
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex items-center flex-wrap gap-2 text-sm">
                <span className="text-gray-700">语速每分钟超过</span>
                <input
                  type="number"
                  min="50"
                  max="500"
                  className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={config.speedThreshold}
                  onChange={(e) => setConfig({ ...config, speedThreshold: parseInt(e.target.value) || 300 })}
                />
                <span className="text-gray-700">个字时检出，当一句话少于</span>
                <input
                  type="number"
                  min="1"
                  max="20"
                  className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={config.minCharCount}
                  onChange={(e) => setConfig({ ...config, minCharCount: parseInt(e.target.value) || 4 })}
                />
                <span className="text-gray-700">个字时不检测</span>
              </div>
              <div className="mt-3 text-xs text-gray-500">
                <span className="inline-flex items-center px-2 py-1 rounded bg-blue-100 text-blue-700 mr-2">
                  建议值
                </span>
                语速阈值：250-350字/分钟，最小字数：3-5字
              </div>
            </div>
          </div>
        </div>

        {/* 检查方式 */}
        <div className="flex items-center space-x-4">
          <label className="w-20 text-sm font-medium text-gray-700 text-right">检查方式</label>
          <div className="flex items-center space-x-6">
            <label className="flex items-center">
              <input
                type="radio"
                name="check-mode"
                className="mr-2 text-blue-600 focus:ring-blue-500"
                checked={config.checkMode === 'single'}
                onChange={() => setConfig({ ...config, checkMode: 'single' })}
              />
              <span className="text-sm text-gray-700">检测单句话的语速</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="check-mode"
                className="mr-2 text-blue-600 focus:ring-blue-500"
                checked={config.checkMode === 'average'}
                onChange={() => setConfig({ ...config, checkMode: 'average' })}
              />
              <span className="text-sm text-gray-700">检测整个对话的平均语速</span>
            </label>
          </div>
        </div>

        {/* 配置预览 */}
        <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">📝</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">当前配置预览</h4>
              <div className="text-sm text-gray-700 space-y-1">
                <div>
                  <span className="font-medium">检测对象：</span>
                  <span className="text-blue-600">
                    {config.detectionRole === 'agent' ? '客服' : config.detectionRole === 'customer' ? '客户' : '所有角色'}
                  </span>
                  {config.detectionScope === 'range' && (
                    <span className="text-gray-500">（第{config.rangeStart}-{config.rangeEnd}句）</span>
                  )}
                </div>
                <div>
                  <span className="font-medium">触发条件：</span>
                  <span className="text-orange-600">
                    语速超过 {config.speedThreshold} 字/分钟
                  </span>
                </div>
                <div>
                  <span className="font-medium">过滤规则：</span>
                  <span className="text-purple-600">
                    忽略少于 {config.minCharCount} 字的句子
                  </span>
                </div>
                <div>
                  <span className="font-medium">检测方式：</span>
                  <span className="text-indigo-600">
                    {config.checkMode === 'single' ? '单句语速检测' : '平均语速检测'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpeechSpeedConfig; 