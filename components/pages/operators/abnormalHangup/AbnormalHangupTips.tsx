import React from 'react';

/**
 * 非正常挂机使用提示组件
 * @constructor
 */
const AbnormalHangupTips: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <span className="text-2xl">💡</span>
        <h2 className="text-xl font-semibold text-gray-900">使用提示</h2>
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          实用技巧
        </span>
      </div>

      <div className="space-y-4">
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-sm">⌨️</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">测试数据格式</h4>
              <div className="space-y-2 text-sm">
                <p>• <strong className="text-gray-700">句子格式:</strong> 每句话需以 `[mm:ss]` 时间戳开头，后跟角色和内容。</p>
                <p className="font-mono bg-gray-100 p-1 rounded text-xs">[00:15] 客户：那太好了，谢谢。</p>
                <p>• <strong className="text-gray-700">挂机时间:</strong> 必须在文本中明确指定挂机时间（秒）。</p>
                <p className="font-mono bg-gray-100 p-1 rounded text-xs">[HANGUP_TIME: 22.5]</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-sm">🔧</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">配置技巧</h4>
              <div className="space-y-2 text-sm">
                <p>• <strong className="text-gray-700">组合条件：</strong>规则命中需要同时满足"挂机时长"和"角色判断"两个条件。</p>
                <p>• <strong className="text-gray-700">"任意"角色：</strong>如果角色选择"任意"，则只判断挂机时长是否满足条件。</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <span className="text-green-600 text-sm">🎯</span>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-2">业务场景建议</h4>
               <div className="space-y-2 text-sm">
                <p>• <strong className="text-gray-700">场景：</strong>为了规范服务流程，通常要求客服在通话结束后5秒内主动挂机。</p>
                <p>• <strong className="text-gray-700">建议配置：</strong>设置 `大于` `5` `秒`，角色为 `任意` 或 `客服`，用于捕获那些挂机不及时的通话。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AbnormalHangupTips; 