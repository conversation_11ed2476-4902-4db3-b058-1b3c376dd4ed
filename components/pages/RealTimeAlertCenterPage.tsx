import React, { useState, useEffect } from 'react';
import { PageHeader } from '../PageHeader';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, PhoneForwarded, Zap, Clock, CheckCircle, UserCheck, ChevronDown, RefreshCw } from 'lucide-react';
import { Tooltip } from '../common/Tooltip';

// --- Types and Mock Data ---

type AlertStatus = 'pending' | 'in-progress' | 'resolved';
type AlertType = 'negative-emotion' | 'compliance-breach' | 'customer-churn-risk';

interface Alert {
    id: string;
    type: AlertType;
    status: AlertStatus;
    timestamp: Date;
    sessionId: string;
    agent: string;
    summary: string;
    details: string;
}

const initialAlerts: Alert[] = [
    { id: 'ALERT-001', type: 'negative-emotion', status: 'pending', timestamp: new Date(Date.now() - 2 * 60 * 1000), sessionId: 'SESS_B9Y4L3N0P2', agent: '坐席12', summary: '客户情绪激动', details: '检测到客户音量和语速急剧升高，出现关键词"岂有此理"。' },
    { id: 'ALERT-002', type: 'compliance-breach', status: 'pending', timestamp: new Date(Date.now() - 5 * 60 * 1000), sessionId: 'SESS_C0Z5M4O1Q3', agent: '坐席05', summary: '合规风险：承诺未兑现', details: '坐席承诺了超出权限的赔偿金额。' },
    { id: 'ALERT-003', type: 'customer-churn-risk', status: 'in-progress', timestamp: new Date(Date.now() - 15 * 60 * 1000), sessionId: 'SESS_D1A6N5P2R4', agent: '坐席08', summary: '客户流失风险', details: '客户明确表示"要转到竞争对手那边去"。' },
    { id: 'ALERT-004', type: 'negative-emotion', status: 'resolved', timestamp: new Date(Date.now() - 60 * 60 * 1000), sessionId: 'SESS_E2B7O6Q3S5', agent: '坐席12', summary: '客户情绪激动', details: '主管已介入处理，客户情绪已平复。' },
];

const alertConfig: Record<AlertType, { title: string; icon: React.ElementType; color: string; }> = {
    'negative-emotion': { title: '客户情绪预警', icon: AlertTriangle, color: 'text-red-500' },
    'compliance-breach': { title: '合规风险预警', icon: Zap, color: 'text-yellow-500' },
    'customer-churn-risk': { title: '客户流失预警', icon: PhoneForwarded, color: 'text-purple-500' },
};

const statusConfig: Record<AlertStatus, { text: string; color: string; bgColor: string; }> = {
    pending: { text: '待处理', color: 'text-red-700', bgColor: 'bg-red-100' },
    'in-progress': { text: '处理中', color: 'text-blue-700', bgColor: 'bg-blue-100' },
    resolved: { text: '已解决', color: 'text-green-700', bgColor: 'bg-green-100' },
};

// --- Main Component ---

export const RealTimeAlertCenterPage: React.FC = () => {
    const [alerts, setAlerts] = useState<Alert[]>(initialAlerts);
    const [filter, setFilter] = useState<AlertStatus | 'all'>('all');
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        // Simulate real-time updates
        const interval = setInterval(() => {
            setAlerts(prev => {
                const newAlert: Alert = { id: `ALERT-${Date.now()}`, type: 'negative-emotion', status: 'pending', timestamp: new Date(), sessionId: `SESS_${Math.random().toString(36).substr(2, 9).toUpperCase()}`, agent: `坐席${Math.floor(Math.random() * 20)}`, summary: '客户情绪激动', details: '检测到客户音量升高。' };
                return [newAlert, ...prev];
            });
        }, 15000); // Add a new alert every 15 seconds
        return () => clearInterval(interval);
    }, []);

    const handleRefresh = () => {
        setIsLoading(true);
        setTimeout(() => setIsLoading(false), 750);
    }

    const filteredAlerts = alerts.filter(a => filter === 'all' || a.status === filter);

    const handleUpdateStatus = (id: string, newStatus: AlertStatus) => {
        setAlerts(alerts.map(a => a.id === id ? { ...a, status: newStatus } : a));
    };
    
    const TimeAgo = ({ date }: { date: Date }) => {
        const [timeString, setTimeString] = useState('');
        useEffect(() => {
            const update = () => {
                const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
                if (seconds < 60) setTimeString(`${seconds}秒前`);
                else setTimeString(`${Math.floor(seconds / 60)}分钟前`);
            };
            update();
            const interval = setInterval(update, 10000);
            return () => clearInterval(interval);
        }, [date]);
        return <>{timeString}</>;
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <PageHeader
                title="实时预警中心"
                description="监控进行中的会话，及时发现并处理突发风险。"
                badge="质检工作台"
                actions={
                     <button onClick={handleRefresh} className="flex items-center bg-white text-gray-600 px-4 py-2 text-sm font-semibold rounded-lg hover:bg-gray-100 transition-colors shadow-sm border">
                        <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        刷新
                    </button>
                }
            />
            <main className="p-6 md:p-10">
                {/* Filters */}
                <div className="mb-6 flex justify-end">
                    <div className="flex p-1 bg-gray-200 rounded-lg">
                        {(['all', 'pending', 'in-progress', 'resolved'] as const).map(status => (
                            <button
                                key={status}
                                onClick={() => setFilter(status)}
                                className={`px-4 py-2 text-sm font-semibold rounded-md transition-colors ${
                                    filter === status ? 'bg-white text-gray-800 shadow' : 'text-gray-600 hover:bg-gray-100'
                                }`}
                            >
                                {status === 'all' ? '全部' : statusConfig[status].text}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Alerts Grid */}
                <AnimatePresence>
                    <motion.div
                        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                        layout
                    >
                        {filteredAlerts.map((alert) => (
                            <motion.div
                                key={alert.id}
                                layout
                                initial={{ opacity: 0, y: 50, scale: 0.8 }}
                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.5, transition: { duration: 0.2 } }}
                                className="bg-white rounded-xl shadow-md border border-gray-100 flex flex-col"
                            >
                                <div className="p-5 border-b border-gray-200">
                                    <div className="flex justify-between items-start">
                                        <div className={`flex items-center font-bold ${alertConfig[alert.type].color}`}>
                                            {React.createElement(alertConfig[alert.type].icon, { className: 'w-5 h-5 mr-2'})}
                                            {alertConfig[alert.type].title}
                                        </div>
                                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${statusConfig[alert.status].bgColor} ${statusConfig[alert.status].color}`}>{statusConfig[alert.status].text}</span>
                                    </div>
                                    <p className="text-sm text-gray-600 mt-2">{alert.summary} - <span className="font-semibold">{alert.agent}</span></p>
                                </div>
                                <div className="p-5 flex-grow">
                                    <p className="text-sm text-gray-800">{alert.details}</p>
                                    <div className="text-xs text-gray-400 mt-3 flex items-center justify-between">
                                        <Link to={`/qa-management/sessions/${alert.sessionId}`} className="hover:underline text-blue-600 font-semibold">{alert.sessionId}</Link>
                                        <span><Clock size={12} className="inline mr-1"/> <TimeAgo date={alert.timestamp} /></span>
                                    </div>
                                </div>
                                <div className="p-3 bg-gray-50/70 border-t flex justify-end items-center gap-2">
                                     {alert.status === 'pending' && <button onClick={() => handleUpdateStatus(alert.id, 'in-progress')} className="text-sm font-semibold text-blue-600 px-3 py-1 rounded-md hover:bg-blue-100">标记处理中</button>}
                                     {alert.status === 'in-progress' && <button onClick={() => handleUpdateStatus(alert.id, 'resolved')} className="text-sm font-semibold text-green-600 px-3 py-1 rounded-md hover:bg-green-100">解决</button>}
                                     <button className="text-sm font-semibold text-gray-700 px-3 py-1 rounded-md hover:bg-gray-200">查看会话</button>
                                </div>
                            </motion.div>
                        ))}
                    </motion.div>
                 </AnimatePresence>

                 {filteredAlerts.length === 0 && (
                     <div className="text-center py-20 text-gray-500">
                         <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-400" />
                         <p className="text-lg">一切正常，暂无待处理预警</p>
                     </div>
                 )}
            </main>
        </div>
    );
};

export default RealTimeAlertCenterPage; 