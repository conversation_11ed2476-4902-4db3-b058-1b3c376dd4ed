import React from 'react';
import { motion } from 'framer-motion';
import { PageHeader } from '../PageHeader';
import { FileText, Settings, PlayCircle, CheckCircle, Search, Share2, Puzzle, Microscope, ShieldQuestion } from 'lucide-react';

const ruleConcepts = [
  {
    icon: Share2,
    name: '规则 (Rule)',
    description: '由逻辑运算符 (&&, ||, !) 和"条件"组成的表达式。您可以利用括号改变运算的优先级。',
    example: '例如：条件A && (!条件B)'
  },
  {
    icon: Puzzle,
    name: '条件 (Condition)',
    description: '由"检查范围"和"算子"组成，是规则的基本判断单元。一个条件内可以包含多个算子组成的逻辑。',
    example: '例如："客服的第一句话" (检查范围) 中 "出现关键词你好" (算子)。'
  },
  {
    icon: Microscope,
    name: '检查范围 (Check Scope)',
    description: '限定算子在哪个区间内生效。可以是角色的对话、对话的顺序、甚至是某个事件发生的前后。',
    example: '例如：客户说的最后3句话。'
  },
  {
    icon: Settings,
    name: '算子 (Operator)',
    description: '对检查范围内的句子逐句做出判定的具体检测方法。',
    example: '例如：关键词检测、正则表达式、语义匹配、语速检测等。'
  }
];

const caseStudy = {
  problem: '某业务场景下，当客户提出更换磁盘类型的需求时，客服人员应提供正确的解决方案，而不是片面地引导客户更换系统盘或重装系统。',
  rule: {
    conditionA: {
      name: '条件A (前提)',
      scope: '客户说的话',
      operator: '利用正则表达式，匹配各种更换磁盘类型的说法 (如"换硬盘"、"换成SSD"等)。'
    },
    conditionB: {
      name: '条件B',
      scope: '条件A命中之后，客服说的后3句话',
      operator: '检测是否出现片面引导的关键词 (如"不支持更换"、"需要更换系统盘"等)。'
    },
    conditionC: {
      name: '条件C',
      scope: '条件A命中之前，客服说的前3句话',
      operator: '检测是否出现片面引导的关键词 (同上)。'
    },
    logic: '最终规则为 (A && (B || C))，即客户提出需求，且客服在回应中给出了片面引导。'
  },
  result: {
    dialogue: [
      { speaker: '客户', text: '你好，我想问一下，我这个云服务器的系统盘能换成SSD的吗？' },
      { speaker: '客服', text: '您好，这个是不支持直接更换磁盘类型的，您需要更换系统盘才能实现。' }
    ],
    analysis: '在上面的对话中，客户的提问命中了条件A。随后，客服的回答命中了条件B中的关键词"更换系统盘"，因此整条规则被命中，系统成功发现了一个服务不当的案例。'
  }
};


export const DetailedGuidePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50/50">
      <PageHeader
        title="智能质检实践"
        description="从核心概念到实战案例，深入理解质检系统的构建与应用。"
        badge="深度解析"
        tag="教程"
      />
      <main className="p-6 md:p-10 max-w-7xl mx-auto">
        {/* Section 1: Business Flow */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">基本业务流程</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
            {[{icon: FileText, name: '创建质检规则'}, {icon: PlayCircle, name: '配置并执行任务'}, {icon: CheckCircle, name: '复核质检结果'}, {icon: Search, name: '分析与洞察'}].map((item, index) => (
              <div key={item.name} className="flex items-center p-4 bg-white rounded-lg shadow-sm border border-gray-100">
                <item.icon className="w-8 h-8 text-blue-500 mr-4"/>
                <span className="font-semibold text-gray-700">{item.name}</span>
                {index < 3 && <ChevronRightIcon className="w-6 h-6 text-gray-300 ml-auto hidden md:block" />}
              </div>
            ))}
          </div>
        </motion.section>

        {/* Section 2: Rule Concepts */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">质检规则深度解析</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {ruleConcepts.map(concept => (
              <div key={concept.name} className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-4">
                  <concept.icon className="w-8 h-8 text-blue-500 mr-4" />
                  <h3 className="text-xl font-bold text-gray-800">{concept.name}</h3>
                </div>
                <p className="text-gray-600 mb-3 text-sm">{concept.description}</p>
                <p className="text-xs text-blue-600 bg-blue-50 p-2 rounded-md">{concept.example}</p>
              </div>
            ))}
          </div>
        </motion.section>

        {/* Section 3: Case Study */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">服务使用案例分析</h2>
          <div className="bg-white rounded-2xl shadow-xl border border-gray-200/80 overflow-hidden">
            <div className="p-6 bg-gray-50 border-b border-gray-200/80">
                <div className="flex items-center">
                    <ShieldQuestion className="w-8 h-8 text-red-500 mr-4"/>
                    <div>
                        <h3 className="text-xl font-bold text-gray-800">需要解决的问题场景</h3>
                        <p className="text-sm text-gray-600 mt-1">{caseStudy.problem}</p>
                    </div>
                </div>
            </div>
            <div className="p-6">
              <h4 className="text-lg font-bold text-gray-800 mb-4">创建的质检规则</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                <div className="p-4 bg-gray-50 rounded-lg border">
                  <p className="font-semibold text-gray-700">{caseStudy.rule.conditionA.name}</p>
                  <p className="mt-2"><b>范围:</b> {caseStudy.rule.conditionA.scope}</p>
                  <p className="mt-1"><b>算子:</b> {caseStudy.rule.conditionA.operator}</p>
                </div>
                 <div className="p-4 bg-gray-50 rounded-lg border">
                  <p className="font-semibold text-gray-700">{caseStudy.rule.conditionB.name} & {caseStudy.rule.conditionC.name}</p>
                  <p className="mt-2"><b>范围:</b> {caseStudy.rule.conditionB.scope}</p>
                  <p className="mt-1"><b>算子:</b> {caseStudy.rule.conditionB.operator}</p>
                </div>
                 <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="font-semibold text-blue-800">最终逻辑</p>
                  <p className="mt-2 text-blue-700">{caseStudy.rule.logic}</p>
                </div>
              </div>
              <h4 className="text-lg font-bold text-gray-800 mt-6 mb-4">质检结果复核</h4>
               <div className="p-4 border rounded-lg bg-white">
                {caseStudy.result.dialogue.map((turn, index) => (
                  <div key={index} className={`mb-2 p-2 rounded-md ${turn.speaker === '客户' ? 'bg-gray-100' : 'bg-blue-50'}`}>
                    <span className={`font-bold ${turn.speaker === '客户' ? 'text-gray-800' : 'text-blue-800'}`}>{turn.speaker}: </span>
                    <span>
                      {turn.text.includes("更换系统盘") ? 
                        turn.text.split("更换系统盘").flatMap((item, i) => i > 0 ? [<mark key={i} className="bg-red-200 px-1 rounded">更换系统盘</mark>, item] : [item]) : 
                        turn.text
                      }
                    </span>
                  </div>
                ))}
                <p className="mt-4 text-sm p-3 bg-green-50 text-green-800 rounded-lg border border-green-200">{caseStudy.result.analysis}</p>
              </div>
            </div>
          </div>
        </motion.section>
      </main>
    </div>
  );
};

// A helper icon component if not available in lucide-react or for custom paths
const ChevronRightIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
    <path d="m9 18 6-6-6-6"/>
  </svg>
); 