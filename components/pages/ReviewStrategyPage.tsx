import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { UnifiedPageHeader } from './final-design/components/UnifiedPageHeader';
import { Plus, Search, Edit, Copy, Trash2, ChevronLeft, ChevronRight, CheckCircle, AlertTriangle, Info, ArrowRight, Bell, BookOpen, Settings } from 'lucide-react';
import { CreateStrategyForm, conditionSubjects, actionTypes } from '../CreateStrategyForm';
import { Tooltip } from '../common/Tooltip';
import { ReviewStrategyDesignGuide } from '../guides/ReviewStrategyDesignGuide';

// Data structure for a Review Strategy
interface Condition {
    id: string;
    subject: string;
    operator: string;
    value: any;
}

interface Action {
    type: string;
    target: string;
}

interface ReviewStrategy {
    id: string;
    name: string;
    status: 'enabled' | 'disabled';
    priority: '高' | '中' | '低';
    conditions: Condition[];
    action: Action;
    creator: string;
    lastModified: string;
    description: string;
    notification?: { enabled: boolean; target: string; message: string };
}

const initialStrategiesData: ReviewStrategy[] = [
  { 
    id: 'STRAT-001', 
    name: '严重违规项自动分配主管复核', 
    status: 'enabled', 
    priority: '高',
    conditions: [{ id: '1', subject: 'ruleImportance', operator: '=', value: '严重违规' }],
    action: { type: 'assignToUser', target: '王主管' },
    creator: '系统管理员', 
    lastModified: '2024-05-28 14:00', 
    description: '所有命中了严重违规的质检结果，将自动推送给王主管进行二次复核，确保高风险问题得到及时处理。' 
  },
  { 
    id: 'STRAT-002', 
    name: '低于60分质检结果进入待定池', 
    status: 'enabled', 
    priority: '中',
    conditions: [{ id: '1', subject: 'totalScore', operator: '<', value: 60 }],
    action: { type: 'assignToGroup', target: '低分组复核池' },
    creator: '李经理', 
    lastModified: '2024-05-27 16:20', 
    description: '分数过低的质检结果需要集中观察，由质检团队成员主动领取复核。' 
  },
  { 
    id: 'STRAT-003', 
    name: '销售合规方案自动复核', 
    status: 'disabled', 
    priority: '中',
    conditions: [{ id: '1', subject: 'schemeId', operator: '=', value: '金融产品销售合规方案' }],
    action: { type: 'assignToGroup', target: '合规审查组' },
    creator: '王主管', 
    lastModified: '2024-05-25 10:00', 
    description: '所有使用金融产品销售合规方案的质检任务，其结果将自动分配给专业的合规团队进行审查。' 
  },
  { 
    id: 'STRAT-004', 
    name: '新员工质检结果100%复核', 
    status: 'enabled', 
    priority: '高',
    conditions: [{ id: '1', subject: 'agentTag', operator: '=', value: '新员工' }],
    action: { type: 'assignToGroup', target: '新人导师组' },
    creator: '李经理', 
    lastModified: '2024-05-29 09:15', 
    description: '为了加速新员工成长，所有新员工的质检结果都需要由其导师团队进行100%全量复核。' 
  },
  {
    id: 'STRAT-005',
    name: '需人工复核的规则自动分配',
    status: 'enabled',
    priority: '中',
    conditions: [{ id: '1', subject: 'ruleNeedsReview', operator: '=', value: '是' }],
    action: { type: 'assignToGroup', target: '待复核池' },
    creator: '系统管理员',
    lastModified: '2024-05-30 11:00',
    description: '将所有标记为"需要人工复核"的规则命中结果，自动分配到通用的待复核池中，等待质检员处理。'
  },
  {
    id: 'STRAT-006',
    name: '常规质检结果随机抽检',
    status: 'enabled',
    priority: '低',
    conditions: [
        { id: '1', subject: 'ruleNeedsReview', operator: '=', value: '否' },
        { id: '2', subject: 'randomSampling', operator: '=', value: 1 }
    ],
    action: { type: 'assignToGroup', target: '质检质量抽检池' },
    creator: '系统管理员',
    lastModified: '2024-05-31 09:00',
    description: '对默认无需人工复核的质检结果，进行1%的随机抽查，以监控自动化质检的准确率。'
  },
  {
    id: 'STRAT-007',
    name: '本月客户体验问题专项复核',
    status: 'enabled',
    priority: '高',
    conditions: [
        { id: '1', subject: 'ruleNeedsReview', operator: '=', value: '否' },
        { id: '2', subject: 'schemeId', operator: '=', value: '客户体验优化方案' }
    ],
    action: { type: 'assignToUser', target: '张三' },
    creator: '李经理',
    lastModified: '2024-05-31 10:00',
    description: '临时策略：本月所有使用"客户体验优化方案"且无需人工复核的质检结果，全部转给张三进行专项检查。'
  },
  {
    id: 'STRAT-008',
    name: '自动关闭无需复核的质检',
    status: 'enabled',
    priority: '低',
    conditions: [{ id: '1', subject: 'ruleNeedsReview', operator: '=', value: '否' }],
    action: { type: 'markAsAutoClosed', target: '' },
    creator: '系统管理员',
    lastModified: '2024-05-31 11:00',
    description: '兜底策略：所有无需人工复核的质检结果，如果没有被其他策略命中，则自动关闭。'
  },
   {
    id: 'STRAT-009',
    name: '需人工复核项进入优先池',
    status: 'enabled',
    priority: '高',
    conditions: [{ id: '1', subject: 'ruleNeedsReview', operator: '=', value: '是' }],
    action: { type: 'assignToGroup', target: '优先复核池' },
    creator: '系统管理员',
    lastModified: '2024-05-31 11:05',
    description: '兜底策略：所有需要人工复核的质检结果，自动进入高优先级的复核池等待处理。'
  },
  {
    id: 'STRAT-010',
    name: '高风险问题平均分配给专家组',
    status: 'enabled',
    priority: '高',
    conditions: [{ id: '1', subject: 'ruleImportance', operator: '=', value: '严重违规' }],
    action: { type: 'assignToUserAverage', target: '张三, 李四, 王五' },
    creator: '系统管理员',
    lastModified: '2024-06-01 14:00',
    description: '所有严重违规的质检结果，将自动平均分配给专家组成员（张三, 李四, 王五）进行复核。',
    notification: { enabled: true, target: '李经理', message: '新的高风险case已分配给专家组，请跟进。' }
  },
  {
    id: 'STRAT-011',
    name: '高分服务自动归档为优秀案例',
    status: 'enabled',
    priority: '低',
    conditions: [{ id: '1', subject: 'totalScore', operator: '>=', value: 95 }],
    action: { type: 'pushToCaseLibrary', target: '优秀服务案例库' },
    creator: '系统管理员',
    lastModified: '2024-06-02 10:00',
    description: '质检总分达到95分及以上的服务，将自动成为优秀案例，供团队学习。'
  }
];

const priorityConfig = {
    '高': { icon: AlertTriangle, color: 'text-red-500', bgColor: 'bg-red-100' },
    '中': { icon: Info, color: 'text-yellow-500', bgColor: 'bg-yellow-100' },
    '低': { icon: CheckCircle, color: 'text-blue-500', bgColor: 'bg-blue-100' },
};

const PAGE_SIZE = 10;

const Switch = ({ checked, onChange }: { checked: boolean, onChange: () => void }) => (
    <button
        type="button"
        onClick={onChange}
        className={`${
            checked ? 'bg-green-500' : 'bg-gray-300'
        } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out`}
        role="switch"
        aria-checked={checked}
    >
        <span
            aria-hidden="true"
            className={`${
                checked ? 'translate-x-5' : 'translate-x-0'
            } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
        />
    </button>
);

/**
 * 复核策略配置页面
 * 定义质检结果的自动分配和流转规则
 */
export const ReviewStrategyPage: React.FC = () => {
    const [strategies, setStrategies] = useState<ReviewStrategy[]>(initialStrategiesData);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [priorityFilter, setPriorityFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [editingStrategy, setEditingStrategy] = useState<ReviewStrategy | undefined>(undefined);
    const [isGuideOpen, setIsGuideOpen] = useState(false);

    const filteredStrategies = useMemo(() => {
        return strategies.filter(strategy => {
            const matchesSearch = strategy.name.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'all' || strategy.status === statusFilter;
            const matchesPriority = priorityFilter === 'all' || strategy.priority === priorityFilter;
            return matchesSearch && matchesStatus && matchesPriority;
        });
    }, [strategies, searchTerm, statusFilter, priorityFilter]);

    const totalPages = Math.ceil(filteredStrategies.length / PAGE_SIZE);
    const paginatedStrategies = filteredStrategies.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

    const handlePageChange = (page: number) => {
        if (page > 0 && page <= totalPages) {
            setCurrentPage(page);
        }
    };
    
    const handleStatusToggle = (strategyId: string) => {
        setStrategies(currentStrategies =>
            currentStrategies.map(strategy =>
                strategy.id === strategyId
                    ? { ...strategy, status: strategy.status === 'enabled' ? 'disabled' : 'enabled' }
                    : strategy
            )
        );
    };

    const handleCreateClick = () => {
        setEditingStrategy(undefined);
        setIsFormOpen(true);
    };

    const handleEditClick = (strategy: ReviewStrategy) => {
        setEditingStrategy(strategy);
        setIsFormOpen(true);
    };

    const handleFormClose = () => {
        setIsFormOpen(false);
        setEditingStrategy(undefined);
    };

    const handleFormSubmit = (strategyData: Omit<ReviewStrategy, 'id' | 'creator' | 'lastModified'> & { id?: string }) => {
        if (strategyData.id) {
            // Update existing strategy
            setStrategies(prev => prev.map(s => s.id === strategyData.id ? { ...s, ...strategyData, lastModified: new Date().toLocaleString() } : s));
        } else {
            // Create new strategy
            const newStrategy: ReviewStrategy = {
                ...strategyData,
                id: `STRAT-${Date.now()}`,
                creator: '当前用户', // Placeholder for actual user
                lastModified: new Date().toLocaleString(),
                status: 'enabled',
            };
            setStrategies(prev => [newStrategy, ...prev]);
        }
        handleFormClose();
    };

    const renderConditions = (strategy: ReviewStrategy) => {
        if (!strategy.conditions || strategy.conditions.length === 0) {
            return <span className="text-gray-500">无</span>;
        }
        
        const conditionText = strategy.conditions.map(c => {
            const subjectConfig = conditionSubjects[c.subject as keyof typeof conditionSubjects];
            const subjectLabel = subjectConfig?.label || c.subject;
            
            let valueText = `'${c.value}'`;
            if (subjectConfig?.valueType === 'percentage') {
                valueText = `${c.value}%`;
            } else if (subjectConfig?.valueType === 'number') {
                valueText = c.value;
            }

            return `${subjectLabel} ${c.operator} ${valueText}`;
        }).join(' 且 ');

        const actionConfig = actionTypes[strategy.action.type as keyof typeof actionTypes];
        let actionText = '';
        if (actionConfig) {
            if (actionConfig.targetType === 'none') {
                actionText = actionConfig.label;
            } else {
                let verb = '分配给';
                if (strategy.action.type === 'assignToGroup') verb = '分配到';
                else if (strategy.action.type === 'pushToCaseLibrary') verb = '推送到';
                else if (strategy.action.type === 'assignToUserAverage') verb = '平均分配给';
                else if (strategy.action.type === 'assignToUserRandom') verb = '随机分配给';
                
                actionText = `${verb} [${strategy.action.target}]`;
            }
        }

        return (
            <div className="flex items-center gap-2">
                <div className="flex-1 p-2 bg-gray-100 rounded-md text-center">
                    <span className="text-xs text-gray-500">IF</span>
                    <p className="font-mono text-xs text-gray-800" title={conditionText}>{conditionText}</p>
                </div>
                <ArrowRight className="w-5 h-5 text-gray-400 shrink-0"/>
                <div className="flex-1 p-2 bg-blue-50 rounded-md text-center">
                     <span className="text-xs text-blue-500">THEN</span>
                    <p className="font-mono text-xs text-blue-800 font-semibold">{actionText}</p>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gray-50/50">
            <UnifiedPageHeader
                title="复核策略配置"
                subtitle="定义质检结果的自动分配和流转规则，实现工作流自动化。"
                icon={Settings}
                badge={{ text: "质检管理", color: "blue" }}
                actions={[
                    {
                        label: "设计思路",
                        icon: BookOpen,
                        onClick: () => setIsGuideOpen(true),
                        variant: "secondary"
                    },
                    {
                        label: "创建策略",
                        icon: Plus,
                        onClick: handleCreateClick,
                        variant: "primary"
                    }
                ]}
            />
            <main className="p-6 md:p-10">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    {/* Filters and Search */}
                    <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-6">
                        <div className="relative w-full md:w-72">
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                type="text"
                                placeholder="按策略名称搜索..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            />
                        </div>
                        <div className="flex items-center gap-4">
                             <select
                                value={priorityFilter}
                                onChange={(e) => setPriorityFilter(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            >
                                <option value="all">所有优先级</option>
                                <option value="高">高</option>
                                <option value="中">中</option>
                                <option value="低">低</option>
                            </select>
                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            >
                                <option value="all">所有状态</option>
                                <option value="enabled">已启用</option>
                                <option value="disabled">已禁用</option>
                            </select>
                        </div>
                    </div>

                    {/* Strategies Table */}
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm text-left text-gray-600">
                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3">策略名称</th>
                                    <th scope="col" className="px-6 py-3">状态</th>
                                    <th scope="col" className="px-6 py-3">优先级</th>
                                    <th scope="col" className="px-6 py-3">工作流：条件与动作</th>
                                    <th scope="col" className="px-6 py-3">创建人</th>
                                    <th scope="col" className="px-6 py-3">最后修改</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedStrategies.map(strategy => {
                                    const PriorityIcon = priorityConfig[strategy.priority].icon;
                                    const priorityColor = priorityConfig[strategy.priority].color;
                                    const priorityBgColor = priorityConfig[strategy.priority].bgColor;

                                    return (
                                        <tr key={strategy.id} className="bg-white border-b hover:bg-gray-50">
                                            <td className="px-6 py-4 align-top">
                                                <div className="font-semibold text-gray-900">{strategy.name}</div>
                                                <div className="text-xs text-gray-500 font-normal truncate max-w-sm mt-1">{strategy.description}</div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <Switch checked={strategy.status === 'enabled'} onChange={() => handleStatusToggle(strategy.id)} />
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium ${priorityBgColor} ${priorityColor}`}>
                                                    <PriorityIcon className="w-3.5 h-3.5" />
                                                    {strategy.priority}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4">
                                                {renderConditions(strategy)}
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className="text-sm text-gray-900">{strategy.creator}</span>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className="text-sm text-gray-900">{strategy.lastModified}</span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <button onClick={() => handleEditClick(strategy)} className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg" title="编辑"><Edit className="w-4 h-4" /></button>
                                                <button className="text-blue-600 hover:text-blue-900 p-1.5 hover:bg-blue-50 rounded-lg mx-1" title="复制"><Copy className="w-4 h-4" /></button>
                                                <button className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg" title="删除"><Trash2 className="w-4 h-4" /></button>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                    {filteredStrategies.length === 0 && (
                        <div className="text-center py-10 text-gray-500">
                            <p>未找到匹配的策略。</p>
                        </div>
                    )}
                    
                    {/* Pagination */}
                    {totalPages > 1 && (
                         <div className="flex items-center justify-between pt-4">
                            <span className="text-sm text-gray-700">
                                共 <span className="font-semibold">{filteredStrategies.length}</span> 条策略，第 <span className="font-semibold">{currentPage}</span> / {totalPages} 页
                            </span>
                            <div className="inline-flex items-center -space-x-px">
                                <button onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} className="px-3 py-2 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50">
                                    <ChevronLeft className="w-4 h-4" />
                                </button>
                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                                    <button
                                        key={page}
                                        onClick={() => handlePageChange(page)}
                                        className={`px-3 py-2 leading-tight border border-gray-300 ${currentPage === page ? 'text-blue-600 bg-blue-50' : 'text-gray-500 bg-white'} hover:bg-gray-100 hover:text-gray-700`}
                                    >
                                        {page}
                                    </button>
                                ))}
                                <button onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages} className="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50">
                                    <ChevronRight className="w-4 h-4" />
                                </button>
                            </div>
                        </div>
                    )}
                </motion.div>
            </main>
            <AnimatePresence>
                {isFormOpen && (
                    <CreateStrategyForm
                        onClose={handleFormClose}
                        onSubmit={handleFormSubmit as any}
                        initialData={editingStrategy}
                    />
                )}
            </AnimatePresence>
            <AnimatePresence>
                {isGuideOpen && (
                    <ReviewStrategyDesignGuide onClose={() => setIsGuideOpen(false)} />
                )}
            </AnimatePresence>
        </div>
    );
};

export default ReviewStrategyPage;