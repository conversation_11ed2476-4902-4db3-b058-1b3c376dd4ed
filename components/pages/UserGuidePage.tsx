import React from 'react';
import { motion } from 'framer-motion';
import { PageHeader } from '../PageHeader';
import { Settings, UploadCloud, CheckSquare, LayoutDashboard, Link as LinkIcon } from 'lucide-react';

const guideSteps = [
  {
    step: '第一步',
    title: '质检规则配置',
    icon: Settings,
    color: 'blue',
    tags: ['SaaS', '规则检测', 'AI检测'],
    description: '将业务质检要求配置成质检规则，再组装成质检方案。平台提供 15+ 检测算子（如关键词、静音、情绪检测等），以满足不同业务场景的需求。',
    details: [
      { text: '基于达摩院先进技术，质检规则准确率和召回率可达 90%+' },
      { text: '内置电商、金融等多个行业的质检方案模版，含 200+ 质检规则，帮助企业快速冷启动。' },
    ],
    link: 'https://help.aliyun.com/zh/sca/user-guide/quality-inspection-rules/',
  },
  {
    step: '第二步',
    title: '质检任务配置',
    icon: UploadCloud,
    color: 'green',
    tags: ['文本', '录音', '实时', '离线'],
    description: '将待测语料传输至系统发起质检任务。系统支持 3 种传输方式，包括数据集上传、对接阿里云呼叫中心、自有系统 API 上传。',
    details: [
      { text: '数据集上传：适用于前期少量测试，可一键发起质检。', link: 'https://help.aliyun.com/zh/sca/user-guide/dataset-quality-inspection-new-sca/' },
      { text: '对接阿里云呼叫中心：与阿里云云呼叫中心数据打通，一键推送语音流。', link: 'https://help.aliyun.com/zh/sca/user-guide/quality-inspection-of-call-canter-new-sca/' },
      { text: '自有系统 API 上传：提供标准 API 接口，需要开发人员介入。', link: 'https://help.aliyun.com/zh/sca/developer-reference/api-qualitycheck-2019-01-15-overview' },
    ],
    link: 'https://help.aliyun.com/zh/sca/user-guide/dataset-quality-inspection-new-sca/',
  },
  {
    step: '第三步',
    title: '质检结果复核',
    icon: CheckSquare,
    color: 'purple',
    tags: ['手动分配', '自动分配', '业务闭环'],
    description: '完成质检后，可手动或通过创建复核规则，将任务高效、便捷地自动分配给质检员进行复核校验。',
    details: [
      { text: '支持管理员、质检员、坐席三种角色，功能权限分离。' },
      { text: '支持一线客服和销售人员发起申诉，实现"质检-复核-申诉"业务闭环。' },
    ],
    link: 'https://help.aliyun.com/zh/sca/user-guide/review-management-new-sca',
  },
  {
    step: '第四步',
    title: '分析结果大盘',
    icon: LayoutDashboard,
    color: 'orange',
    tags: ['数据监控', '自定义报表'],
    description: '企业需要定期跟踪质检数据，如质检总会话数、违规数、违规规则，以全面监控客服服务质量，有效掌控质检动态。',
    details: [
      { text: '提供质检概况、服务质量分析、复核统计、申诉统计四大统计模块。' },
      { text: '后台数据支持免费下载，企业可筛选所需数据生成自定义报表做业务分析。' },
    ],
    link: 'https://help.aliyun.com/zh/sca/user-guide/effect-statistics/',
  },
];

const Tag: React.FC<{ text: string; color: string }> = ({ text, color }) => (
  <span className={`inline-block px-2 py-0.5 text-xs font-medium rounded-full bg-${color}-100 text-${color}-800`}>
    {text}
  </span>
);

export const UserGuidePage: React.FC = () => {
  const getColorClasses = (color: string) => {
    const colorMap: { [key: string]: any } = {
      blue: { bg: 'bg-blue-50', border: 'border-blue-200', iconBg: 'bg-blue-100', iconText: 'text-blue-600', button: 'bg-blue-600 hover:bg-blue-700' },
      green: { bg: 'bg-green-50', border: 'border-green-200', iconBg: 'bg-green-100', iconText: 'text-green-600', button: 'bg-green-600 hover:bg-green-700' },
      purple: { bg: 'bg-purple-50', border: 'border-purple-200', iconBg: 'bg-purple-100', iconText: 'text-purple-600', button: 'bg-purple-600 hover:bg-purple-700' },
      orange: { bg: 'bg-orange-50', border: 'border-orange-200', iconBg: 'bg-orange-100', iconText: 'text-orange-600', button: 'bg-orange-600 hover:bg-orange-700' },
    };
    return colorMap[color] || { bg: 'bg-gray-50', border: 'border-gray-200', iconBg: 'bg-gray-100', iconText: 'text-gray-600', button: 'bg-gray-600 hover:bg-gray-700' };
  };

  return (
    <div className="min-h-screen bg-white">
      <PageHeader
        title="产品使用指南"
        description="从规则配置到结果分析，四步走完智能质检全链路。"
        badge="使用指南"
        tag="SaaS"
      />
      <main className="p-6 md:p-10">
        <div className="relative max-w-4xl mx-auto">
          {/* The vertical line */}
          <div className="absolute left-9 top-4 h-full w-0.5 bg-gray-200" aria-hidden="true"></div>

          {guideSteps.map((item, index) => {
            const colors = getColorClasses(item.color);
            return (
              <motion.div
                key={item.step}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.6, delay: index * 0.15 }}
                className="relative mb-16 pl-20"
              >
                {/* Icon and Step number */}
                <div className={`absolute left-0 top-0 z-10 flex items-center justify-center w-20 h-20 rounded-full ${colors.iconBg} border-8 border-white`}>
                  <item.icon className={`w-10 h-10 ${colors.iconText}`} />
                </div>
                
                {/* Content Card */}
                <div className={`pt-1`}>
                  <div className={`p-6 rounded-xl border-2 ${colors.border} ${colors.bg} shadow-md`}>
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <div className={`text-sm font-bold tracking-wider uppercase ${colors.iconText}`}>{item.step}</div>
                        <h3 className="text-2xl font-bold text-gray-800 mt-1">{item.title}</h3>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {item.tags.map(tag => <Tag key={tag} text={tag} color={item.color} />)}
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed mb-5">{item.description}</p>
                    
                    <div className="space-y-3 mb-6">
                      {item.details.map((detail, i) => (
                        <div key={i} className="flex items-center p-3 bg-white rounded-lg border border-gray-200">
                          <span className={`flex-shrink-0 w-2 h-2 mr-3 rounded-full ${colors.iconBg.replace('100', '400')}`}></span>
                          <span className="text-sm text-gray-700 flex-grow">{detail.text}</span>
                          {'link' in detail && (
                            <a href={detail.link} target="_blank" rel="noopener noreferrer" className={`ml-4 text-xs font-semibold ${colors.iconText} hover:underline`}>
                              查看详情
                            </a>
                          )}
                        </div>
                      ))}
                    </div>
                    
                    <a
                      href={item.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`inline-flex items-center px-5 py-2.5 border border-transparent text-base font-medium rounded-md shadow-sm text-white ${colors.button} transition-colors duration-200`}
                    >
                      <LinkIcon className="w-5 h-5 mr-2" />
                      学习该节文档
                    </a>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </main>
    </div>
  );
}; 