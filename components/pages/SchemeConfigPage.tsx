import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { PageHeader } from '../PageHeader';
import { Plus, Search, Edit, Copy, Trash2, ChevronLeft, ChevronRight, FileText } from 'lucide-react';
import { CreateSchemeForm } from '../CreateSchemeForm';

// Simplified Rule interface for displaying within a scheme
interface RuleInScheme {
    id: string;
    name: string;
    score: number;
}

// Scheme data structure
interface Scheme {
    id: string;
    name: string;
    status: 'enabled' | 'disabled';
    totalScore: number;
    passThreshold: number;
    appealDeadlineDays: number;
    rules: RuleInScheme[];
    creator: string;
    lastModified: string;
    description: string;
}

// Mock data for all available rules in the library
// In a real app, this would be fetched from an API
const allAvailableRules = [
  { id: 'RULE-001', name: '客户不满与合规词检测', category: '合规风险', importance: '严重违规', description: '识别客户不满关键词，并排除特定合规术语。' },
  { id: 'RULE-002', name: '标准开场白检测', category: '服务规范', importance: '轻度违规', description: '检测坐席开场白是否符合"您好，很高兴为您服务"的标准句式。' },
  { id: 'RULE-003', name: '合规声明-录音告知', category: '合规风险', importance: '中度违规', description: '验证坐席是否在通话开始时进行了录音告知。' },
  { id: 'RULE-004', name: '客户情绪-激动模型', category: '客户体验', importance: '中度违规', description: '使用AI模型结合能量检测判断客户是否存在激动情绪。' },
  { id: 'RULE-005', name: '身份证号码格式校验', category: '信息安全', importance: '严重违规', description: '通过正则表达式验证身份证号码格式是否正确。' },
  { id: 'RULE-006', name: '客服辱骂检测模型', category: '服务红线', importance: '严重违规', description: '监控客服在对话中是否存在不文明用语。' },
  { id: 'RULE-007', name: '上下文重复询问', category: '服务效率', importance: '轻度违规', description: '检测坐席是否在短时间内重复询问客户相同的问题。' },
  { id: 'RULE-008', name: '静音与抢话综合检测', category: '服务质量', importance: '中度违规', description: '检测单次静音时长是否超过30秒，以及是否存在抢话。' },
  { id: 'RULE-009', name: '信息实体-手机号与邮箱', category: '信息提取', importance: '轻度违规', description: '提取对话中客户提及的手机号码和邮箱地址。' },
  { id: 'RULE-010', name: '大模型质检-金融产品推荐', category: '销售合规', importance: '严重违规', description: '使用大模型判断坐席的金融产品推荐是否合规、适当，并校验是否提及风险。' },
];

const initialSchemesData: Scheme[] = [
  { 
    id: 'SCHEME-001', 
    name: '服务规范通用方案', 
    status: 'enabled', 
    totalScore: 100,
    passThreshold: 85,
    appealDeadlineDays: 7,
    rules: [
      { id: 'RULE-002', name: '标准开场白检测', score: -5 },
      { id: 'RULE-007', name: '上下文重复询问', score: -10 },
      { id: 'RULE-008', name: '静音与抢话综合检测', score: -15 },
    ],
    creator: '王主管', 
    lastModified: '2024-05-28 11:00', 
    description: '适用于所有常规服务场景，考察基本的服务礼仪和效率。' 
  },
  { 
    id: 'SCHEME-002', 
    name: '金融产品销售合规方案', 
    status: 'enabled', 
    totalScore: 100,
    passThreshold: 95,
    appealDeadlineDays: 3,
    rules: [
      { id: 'RULE-001', name: '客户不满与合规词检测', score: -20 },
      { id: 'RULE-003', name: '合规声明-录音告知', score: -25 },
      { id: 'RULE-005', name: '身份证号码格式校验', score: -20 },
      { id: 'RULE-010', name: '大模型质检-金融产品推荐', score: -35 },
    ],
    creator: '李经理', 
    lastModified: '2024-05-27 15:30', 
    description: '针对高风险的金融产品销售场景，强调查验合规红线。' 
  },
  { 
    id: 'SCHEME-003', 
    name: '客户体验优化方案', 
    status: 'disabled', 
    totalScore: 100,
    passThreshold: 80,
    appealDeadlineDays: 5,
    rules: [
        { id: 'RULE-004', name: '客户情绪-激动模型', score: -30 },
        { id: 'RULE-006', name: '客服辱骂检测模型', score: -50 },
        { id: 'RULE-009', name: '信息实体-手机号与邮箱', score: -5 },
    ],
    creator: '张三', 
    lastModified: '2024-05-26 09:45', 
    description: '重点关注客户的情绪反馈和服务过程中的负面体验。' 
  },
];

const PAGE_SIZE = 10;

const Switch = ({ checked, onChange }: { checked: boolean, onChange: () => void }) => (
    <button
        type="button"
        onClick={onChange}
        className={`${
            checked ? 'bg-green-500' : 'bg-gray-300'
        } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
        role="switch"
        aria-checked={checked}
    >
        <span
            aria-hidden="true"
            className={`${
                checked ? 'translate-x-5' : 'translate-x-0'
            } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
        />
    </button>
);

/**
 * 质检方案配置页面
 * 提供质检方案的创建、查看、筛选和管理功能。方案是质检规则的有序集合。
 */
export const SchemeConfigPage: React.FC = () => {
    const [schemes, setSchemes] = useState<Scheme[]>(initialSchemesData);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [editingScheme, setEditingScheme] = useState<Scheme | null>(null);

    const filteredSchemes = useMemo(() => {
        return schemes.filter(scheme => {
            const matchesSearch = scheme.name.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'all' || scheme.status === statusFilter;
            return matchesSearch && matchesStatus;
        });
    }, [schemes, searchTerm, statusFilter]);

    const totalPages = Math.ceil(filteredSchemes.length / PAGE_SIZE);
    const paginatedSchemes = filteredSchemes.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

    const handlePageChange = (page: number) => {
        if (page > 0 && page <= totalPages) {
            setCurrentPage(page);
        }
    };
    
    const handleStatusToggle = (schemeId: string) => {
        setSchemes(currentSchemes =>
            currentSchemes.map(scheme =>
                scheme.id === schemeId
                    ? { ...scheme, status: scheme.status === 'enabled' ? 'disabled' : 'enabled' }
                    : scheme
            )
        );
    };

    const handleOpenCreateForm = () => {
        setEditingScheme(null);
        setIsFormOpen(true);
    };

    const handleOpenEditForm = (scheme: Scheme) => {
        setEditingScheme(scheme);
        setIsFormOpen(true);
    };

    const handleCloseForm = () => {
        setIsFormOpen(false);
        setEditingScheme(null);
    };

    const handleSubmitScheme = (schemeData: { name: string; description: string; totalScore: number; passThreshold: number; appealDeadlineDays: number; rules: RuleInScheme[] }) => {
        setSchemes(prevSchemes => {
            if (editingScheme) {
                // Update existing scheme
                return prevSchemes.map(s => s.id === editingScheme.id ? { ...s, ...schemeData, lastModified: new Date().toLocaleString() } : s);
            } else {
                // Create new scheme
                const newScheme: Scheme = {
                    ...schemeData,
                    id: `SCHEME-${String(prevSchemes.length + 1).padStart(3, '0')}`,
                    status: 'enabled',
                    creator: '当前用户',
                    lastModified: new Date().toLocaleString(),
                };
                return [newScheme, ...prevSchemes];
            }
        });
        handleCloseForm();
    };

    return (
        <div className="min-h-screen bg-gray-50/50">
            <PageHeader
                title="质检方案配置"
                description="组合多个独立的质检规则，形成可用于具体质检任务的完整评分方案。"
                badge="质检管理"
                actions={
                    <div className="flex items-center gap-3">
                        <button 
                            onClick={handleOpenCreateForm}
                            className="flex items-center bg-blue-600 text-white px-4 py-2 text-sm font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-sm">
                            <Plus className="w-4 h-4 mr-2" />
                            创建方案
                        </button>
                    </div>
                }
            />
            <main className="p-6 md:p-10">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
                >
                    {/* Filters and Search */}
                    <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-6">
                        <div className="relative w-full md:w-72">
                            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                type="text"
                                placeholder="按方案名称搜索..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            />
                        </div>
                        <div className="flex items-center gap-4">
                            <select
                                value={statusFilter}
                                onChange={(e) => setStatusFilter(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            >
                                <option value="all">所有状态</option>
                                <option value="enabled">已启用</option>
                                <option value="disabled">已禁用</option>
                            </select>
                        </div>
                    </div>

                    {/* Schemes Table */}
                    <div className="overflow-x-auto">
                        <table className="w-full text-sm text-left text-gray-600">
                            <thead className="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3">方案名称</th>
                                    <th scope="col" className="px-6 py-3">状态</th>
                                    <th scope="col" className="px-6 py-3">申诉期限</th>
                                    <th scope="col" className="px-6 py-3">总分/合格线</th>
                                    <th scope="col" className="px-6 py-3">包含规则数</th>
                                    <th scope="col" className="px-6 py-3">规则列表</th>
                                    <th scope="col" className="px-6 py-3">创建人</th>
                                    <th scope="col" className="px-6 py-3">最后修改</th>
                                    <th scope="col" className="px-6 py-3 text-right">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedSchemes.map(scheme => (
                                    <tr key={scheme.id} className="bg-white border-b hover:bg-gray-50">
                                        <td className="px-6 py-4 align-top">
                                            <div className="font-semibold text-gray-900">{scheme.name}</div>
                                            <div className="text-xs text-gray-500 font-normal truncate max-w-sm mt-1">{scheme.description}</div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <Switch checked={scheme.status === 'enabled'} onChange={() => handleStatusToggle(scheme.id)} />
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm font-medium text-gray-800">{scheme.appealDeadlineDays} 天</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className='flex items-baseline'>
                                                <span className="font-semibold text-lg text-gray-800">{scheme.totalScore}</span>
                                                <span className="text-sm text-gray-500 ml-1">分</span>
                                            </div>
                                            <div className='text-xs text-green-600 font-bold'>≥ {scheme.passThreshold}分合格</div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900 font-medium">{scheme.rules.length}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <div className="flex flex-col items-start gap-2 max-w-xs">
                                                {scheme.rules.map(rule => (
                                                    <div key={rule.id} className="flex items-center gap-2">
                                                        <FileText className="w-4 h-4 text-gray-400 shrink-0" />
                                                        <span className="text-sm text-gray-700 truncate">{rule.name}</span>
                                                        <span className="text-sm font-semibold text-red-600 ml-auto">{rule.score}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{scheme.creator}</span>
                                        </td>
                                        <td className="px-6 py-4">
                                            <span className="text-sm text-gray-900">{scheme.lastModified}</span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button onClick={() => handleOpenEditForm(scheme)} className="text-indigo-600 hover:text-indigo-900 p-1.5 hover:bg-indigo-50 rounded-lg transition-colors" title="编辑">
                                                <Edit className="w-4 h-4" />
                                            </button>
                                            <button className="text-blue-600 hover:text-blue-900 p-1.5 hover:bg-blue-50 rounded-lg transition-colors mx-1" title="复制">
                                                <Copy className="w-4 h-4" />
                                            </button>
                                            <button className="text-red-600 hover:text-red-900 p-1.5 hover:bg-red-50 rounded-lg transition-colors" title="删除">
                                                <Trash2 className="w-4 h-4" />
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    {filteredSchemes.length === 0 && (
                        <div className="text-center py-10 text-gray-500">
                            <p>未找到匹配的方案。</p>
                        </div>
                    )}
                    
                    {/* Pagination */}
                    {totalPages > 1 && (
                         <div className="flex items-center justify-between pt-4">
                            <span className="text-sm text-gray-700">
                                共 <span className="font-semibold">{filteredSchemes.length}</span> 条方案，第 <span className="font-semibold">{currentPage}</span> / {totalPages} 页
                            </span>
                            <div className="inline-flex items-center -space-x-px">
                                <button onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} className="px-3 py-2 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50">
                                    <ChevronLeft className="w-4 h-4" />
                                </button>
                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                                    <button
                                        key={page}
                                        onClick={() => handlePageChange(page)}
                                        className={`px-3 py-2 leading-tight border border-gray-300 ${currentPage === page ? 'text-blue-600 bg-blue-50' : 'text-gray-500 bg-white'} hover:bg-gray-100 hover:text-gray-700`}
                                    >
                                        {page}
                                    </button>
                                ))}
                                <button onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages} className="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50">
                                    <ChevronRight className="w-4 h-4" />
                                </button>
                            </div>
                        </div>
                    )}
                </motion.div>
            </main>
            {isFormOpen && (
                <CreateSchemeForm 
                    onClose={handleCloseForm}
                    onSubmit={handleSubmitScheme}
                    allRules={allAvailableRules}
                    initialData={editingScheme}
                />
            )}
        </div>
    );
};

export default SchemeConfigPage; 