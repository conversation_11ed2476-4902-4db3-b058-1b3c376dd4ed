import React from 'react';
import type { ScoringConfig } from '../types';
import { ScoringMode, HitScoringTarget } from '../types';
import { QuestionMarkCircleIcon } from './icons';
import { Tooltip } from './common/Tooltip';


interface ScoringConfigSectionProps {
  config: ScoringConfig;
  onChange: <K extends keyof ScoringConfig>(field: K, value: ScoringConfig[K]) => void;
}

export const ScoringConfigSection: React.FC<ScoringConfigSectionProps> = ({ config, onChange }) => {
  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-700">评分配置</h2>
        <div className="flex items-center">
          <span className="mr-2 text-sm text-gray-600">规则评分</span>
          <label htmlFor="ruleScoringToggle" className="flex items-center cursor-pointer">
            <div className="relative">
              <input 
                type="checkbox" 
                id="ruleScoringToggle" 
                className="sr-only" 
                checked={config.ruleScoringEnabled}
                onChange={(e) => onChange('ruleScoringEnabled', e.target.checked)}
              />
              <div className={`block w-10 h-6 rounded-full ${config.ruleScoringEnabled ? 'bg-blue-600' : 'bg-gray-300'}`}></div>
              <div className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform ${config.ruleScoringEnabled ? 'transform translate-x-full' : ''}`}></div>
            </div>
          </label>
        </div>
      </div>

      {config.ruleScoringEnabled && (
        <div className="space-y-4 pl-1">
          <div className="flex items-center">
            <input
              type="radio"
              id="scoreByHit"
              name="scoringMode"
              value={ScoringMode.BY_HIT}
              checked={config.scoringMode === ScoringMode.BY_HIT}
              onChange={() => onChange('scoringMode', ScoringMode.BY_HIT)}
              className="h-4 w-4 bg-white border border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="scoreByHit" className="ml-2 text-sm font-medium text-gray-700">
              按是否命中评分
            </label>
          </div>

          <div className="pl-6 space-y-3">
            <div className="text-sm font-medium text-gray-700">命中该规则</div>
            <div className="flex items-center">
              <input
                type="radio"
                id="agentScore"
                name="hitScoringTarget"
                value={HitScoringTarget.AGENT_SCORE}
                checked={config.hitScoringTarget === HitScoringTarget.AGENT_SCORE}
                onChange={() => onChange('hitScoringTarget', HitScoringTarget.AGENT_SCORE)}
                className="h-4 w-4 bg-white border border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="agentScore" className="ml-2 text-sm text-gray-600">
                给客服人员评分
              </label>
              <input 
                type="number"
                value={config.scoreValue}
                onChange={(e) => onChange('scoreValue', parseInt(e.target.value,10) || 0)}
                className="ml-2 w-20 p-1 border border-gray-300 rounded-md text-sm text-center bg-white"
                disabled={config.hitScoringTarget !== HitScoringTarget.AGENT_SCORE}
              />
              <span className="ml-1 text-sm text-gray-600">分</span>
            </div>
            <div className="flex items-center">
              <input
                type="radio"
                id="agentOneTimeScore"
                name="hitScoringTarget"
                value={HitScoringTarget.AGENT_ONE_TIME_SCORE}
                checked={config.hitScoringTarget === HitScoringTarget.AGENT_ONE_TIME_SCORE}
                onChange={() => onChange('hitScoringTarget', HitScoringTarget.AGENT_ONE_TIME_SCORE)}
                className="h-4 w-4 bg-white border border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="agentOneTimeScore" className="ml-2 text-sm text-gray-600">
                给客服人员一次性评分
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
