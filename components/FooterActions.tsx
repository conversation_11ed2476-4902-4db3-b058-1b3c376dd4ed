
import React from 'react';

interface FooterActionsProps {
  onConfirm: () => void;
  onSaveAndTest: () => void;
  onCancel: () => void;
}

export const FooterActions: React.FC<FooterActionsProps> = ({ onConfirm, onSaveAndTest, onCancel }) => {
  return (
    <div className="mt-8 pt-6 border-t border-gray-200 flex justify-end space-x-3">
      <button
        onClick={onCancel}
        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        取消
      </button>
      <button
        onClick={onSaveAndTest}
        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        保存并测试
      </button>
      <button
        onClick={onConfirm}
        className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        确定
      </button>
    </div>
  );
};
