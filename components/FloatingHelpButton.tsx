
import React from 'react';
import { ChatBubbleLeftEllipsisIcon } from './icons'; // Using a chat icon as a placeholder for help

export const FloatingHelpButton: React.FC = () => {
  return (
    <button
      title="Help"
      className="fixed bottom-8 right-8 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
    >
      <ChatBubbleLeftEllipsisIcon className="w-6 h-6" />
    </button>
  );
};
