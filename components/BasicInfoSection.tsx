
import React from 'react';
import type { BasicInfo } from '../types';

interface BasicInfoSectionProps {
  info: BasicInfo;
  onChange: (field: keyof BasicInfo, value: string) => void;
  onEdit: () => void;
}

const InfoItem: React.FC<{ label: string; value: string | React.ReactNode }> = ({ label, value }) => (
  <div className="grid grid-cols-3 gap-4 py-3 border-b border-gray-100 last:border-b-0">
    <dt className="text-sm font-medium text-gray-600">{label}</dt>
    <dd className="text-sm text-gray-900 col-span-2">{value}</dd>
  </div>
);

const RemarkItem: React.FC<{ label: string; value: string | React.ReactNode }> = ({ label, value }) => (
  <div className="py-3 border-b border-gray-100 last:border-b-0">
    <dt className="text-sm font-medium text-gray-600 mb-2">{label}</dt>
    <dd className="text-sm text-gray-900 text-left">{value}</dd>
  </div>
);

export const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({ info }) => {
  // In a real scenario, onChange would update editable fields. For now, display only.
  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-700">基本信息</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
        <InfoItem label="规则名称" value={info.ruleName} />
        <InfoItem label="重要程度" value={info.importance} />
        <InfoItem label="规则种类" value={info.ruleCategory} />
        <InfoItem label="生效时间" value={info.effectiveTime} />
        <div className="md:col-span-2">
          <RemarkItem label="备注" value={<span className="whitespace-pre-wrap">{info.remarks}</span>} />
        </div>
        <InfoItem label="规则类型" value={info.ruleType} />
        <InfoItem label="适用业务" value={info.applicableBusiness || '无'} />
        <InfoItem label="人工复核" value={info.manualReview || '无'} />
      </div>
    </div>
  );
};
