
# SpecStory Artifacts Directory
    
This directory is automatically created and maintained by the SpecStory extension to preserve your Cursor composer and chat history.
    
## What's Here?
    
- `.specstory/history`: Contains markdown files of your AI coding sessions
    - Each file represents a separate chat or composer session
    - Files are automatically updated as you work
- `.specstory/cursor_rules_backups`: Contains backups of the `.cursor/rules/derived-cursor-rules.mdc` file
    - Backups are automatically created each time the `.cursor/rules/derived-cursor-rules.mdc` file is updated
    - You can enable/disable the Cursor Rules feature in the SpecStory settings, it is disabled by default

## Valuable Uses
    
- Capture: Keep your context window up-to-date when starting new Chat/Composer sessions via @ references
- Search: For previous prompts and code snippets 
- Learn: Meta-analyze your patterns and learn from your past experiences
- Derive: Keep Cursor on course with your past decisions by automatically deriving Cursor rules from your AI interactions
    
## Version Control
    
We recommend keeping this directory under version control to maintain a history of your AI interactions. However, if you prefer not to version these files, you can exclude them by adding this to your `.gitignore`:
    
```
.specstory
```

We recommend not keeping the `.specstory/cursor_rules_backups` directory under version control if you are already using git to version the `.cursor/rules` directory, and committing regularly. You can exclude it by adding this to your `.gitignore`:

```
.specstory/cursor_rules_backups
```

## Searching Your Codebase
    
When searching your codebase in Cursor, search results may include your previous AI coding interactions. To focus solely on your actual code files, you can exclude the AI interaction history from search results.
    
To exclude AI interaction history:
    
1. Open the "Find in Files" search in Cursor (Cmd/Ctrl + Shift + F)
2. Navigate to the "files to exclude" section
3. Add the following pattern:
    
```
.specstory/*
```
    
This will ensure your searches only return results from your working codebase files.

## Notes

- Auto-save only works when Cursor/sqlite flushes data to disk. This results in a small delay after the AI response is complete before SpecStory can save the history.
- Auto-save does not yet work on remote WSL workspaces.

## Settings
    
You can control auto-saving behavior in Cursor:
    
1. Open Cursor → Settings → VS Code Settings (Cmd/Ctrl + ,)
2. Search for "SpecStory"
3. Find "Auto Save" setting to enable/disable
    
Auto-save occurs when changes are detected in Cursor's sqlite database, or every 2 minutes as a safety net.