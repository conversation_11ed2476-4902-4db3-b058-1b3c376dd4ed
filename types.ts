export enum ConditionType {
  REGEX = 'regex',
  KEYWORD = 'keyword',
}

export enum DetectionRole {
  CUSTOMER_SERVICE = '客服',
  CUSTOMER = '客户',
  ALL = '全员',
}

export enum PrerequisiteCondition {
  NONE = '无',
  A = '条件a',
  B = '条件b',
  C = '条件c',
  D = '条件d',
  E = '条件e',
  F = '条件f',
  G = '条件g',
  H = '条件h',
}

export enum DetectionScope {
  ENTIRE_TEXT = '全文',
  SPECIFIED_RANGE = '指定范围',
  BEFORE_HIT = '前置条件命中位置前',
  AFTER_HIT = '前置条件命中位置后',
  AROUND_HIT = '前置条件命中位置前后',
}

export enum AnalysisMethod {
  SINGLE_SENTENCE = '单句分析',
  MULTI_SENTENCE = '多句分析',
}

export enum KeywordDetectionType {
  CONTAINS_ANY = '包含任意一个关键词',
  CONTAINS_ALL = '包含全部上述关键词',
  CONTAINS_ANY_N = '包含任意N个关键词',
  CONTAINS_NONE = '全部不包含',
}

export interface Condition {
  id: string;
  name: string;
  type: ConditionType;
  detectionRole: DetectionRole;
  prerequisite: PrerequisiteCondition;
  hitOrder: number;
  detectionScope: DetectionScope;
  rangeStart: number;
  rangeEnd: number;
  effectiveInSingleSentence: boolean;
  isCollapsed: boolean;

  // Regex specific
  hitPattern?: string;
  excludePattern?: string;
  aiOptimizedRegex?: string;
  testInput?: string;

  // Keyword specific
  keywords?: string[];
  currentKeywordInput?: string;
  analysisMethod?: AnalysisMethod;
  keywordDetectionType?: KeywordDetectionType;
  nValueForKeyword?: number;
}

export interface BasicInfo {
  ruleName: string;
  ruleCategory: string;
  remarks: string;
  importance: string;
  effectiveTime: string;
  ruleType: string;
  applicableBusiness: string;
  manualReview: string;
}

export enum ScoringMode {
  BY_HIT = '按是否命中评分',
}

export enum HitScoringTarget {
  AGENT_SCORE = '给客服人员评分',
  AGENT_ONE_TIME_SCORE = '给客服人员一次性评分',
}

export interface ScoringConfig {
  ruleScoringEnabled: boolean;
  scoringMode: ScoringMode;
  hitScoringTarget: HitScoringTarget;
  scoreValue: number;
}

export interface RuleData {
  basicInfo: BasicInfo;
  detectionLogic: string;
  conditions: Condition[];
  scoringConfig: ScoringConfig;
}
