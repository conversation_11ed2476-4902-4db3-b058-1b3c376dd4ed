# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI quality inspection rule configuration and learning platform for call centers. It provides a visual interface for non-technical users (business analysts, operations staff) to learn, configure, and test various "quality inspection operators" (rule modules) and combine them into complex business quality inspection SOPs (Standard Operating Procedures).

## Architecture & Key Concepts

### Core Architecture
- **Modular Design**: Each quality inspection operator (e.g., keyword check, regex check) is an independent page-level component
- **Component Pattern**: Each operator page follows a consistent structure:
  - `Concepts`: Explains the operator's core concepts and principles
  - `Config`: Provides operator configuration interface
  - `Tester`: Real-time operator testing
  - `Cases`: Provides preset typical case library
  - `Tips`: Usage tips and recommendations
- **Strong Type System**: Centralized type definitions in `types.ts` ensure data consistency
- **Unified Layout**: All pages share `MainLayout` with consistent navigation

### Quality Inspection Operators
The system supports 15+ operators organized into categories:
- **Text Inspection**: Keyword check, regex check, text similarity, context repeat, entity check
- **Speech Inspection**: Call silence, speech speed, interrupt check, role judgment
- **Behavioral Inspection**: Abnormal hangup, abnormal answer, duration check
- **AI Model Inspection**: Large model checks, agent model, customer model
- **Recording Inspection**: Recording parameters check

### Key Data Structures
- `Condition`: Core rule configuration interface
- `BasicInfo`: Rule metadata (name, category, importance, etc.)
- `ScoringConfig`: Scoring behavior configuration
- `RuleData`: Complete rule definition combining all components

## Development Commands

### Setup & Installation
```bash
npm install          # Install dependencies
```

### Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
```

### Environment Variables
Create `.env` file with:
```
GEMINI_API_KEY=your_gemini_api_key
```

## Code Organization

### Directory Structure
```
src/
├── components/
│   ├── layout/           # Main layout components (MainLayout, Navigation, Sidebar)
│   ├── pages/
│   │   ├── operators/    # Individual operator pages (15+ operators)
│   │   ├── examples/     # Complex rule examples
│   │   ├── final-design/ # Final production UI designs
│   │   └── demos/        # Demo pages
│   └── common/           # Reusable components
├── lib/                  # Utility functions
└── styles/               # Global styles
```

### Key Files
- `App.tsx`: Main routing configuration with 140+ routes
- `types.ts`: Centralized type definitions
- `vite.config.ts`: Build configuration with path alias `@`

## Development Standards

### Naming Conventions
- **Components**: PascalCase (e.g., `KeywordCheckPage`)
- **Files**: PascalCase for components, camelCase for utilities
- **No Default Exports**: Use named exports only (`export const Component`)

### Component Structure Pattern
```typescript
// Each operator page follows this pattern
export const [Operator]Page = () => {
  return (
    <div>
      <[Operator]Concepts />
      <[Operator]Config />
      <[Operator]Tester />
      <[Operator]Cases />
      <[Operator]Tips />
    </div>
  );
};
```

### Path Alias
Use `@` as root path alias in imports:
```typescript
import { Component } from '@/components/Component';
```

## Development Workflow

1. **Learning Mode**: Start with `/user-guide` to understand the system
2. **Operator Development**: Create new operators in `components/pages/operators/`
3. **Rule Composition**: Use `/rule-composition` to test complex rule combinations
4. **Final Design**: Implement production UIs in `final-design/` directory

## Testing Strategy

### Manual Testing
- Each operator page has built-in test functionality
- Use `/complex-example` to test multi-operator rule combinations
- Test edge cases with provided case libraries

### Production Testing
- All final design pages include comprehensive testing scenarios
- Real-time alert systems and notification centers for validation