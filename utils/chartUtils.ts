/**
 * 图表工具函数
 * 用于生成模拟数据和图表配置
 */

/**
 * 生成随机数据
 */
export const generateRandomData = (min: number, max: number, decimals: number = 0): number => {
  const value = Math.random() * (max - min) + min;
  return decimals > 0 ? parseFloat(value.toFixed(decimals)) : Math.floor(value);
};

/**
 * 生成时间序列数据
 */
export const generateTimeSeriesData = (hours: number = 24) => {
  const data = [];
  const labels = [];
  
  for (let i = hours - 1; i >= 0; i--) {
    const time = new Date();
    time.setHours(time.getHours() - i);
    labels.push(time.getHours().toString().padStart(2, '0') + ':00');
    data.push(generateRandomData(500, 1500));
  }
  
  return { labels, data };
};

/**
 * 生成质检分数数据
 */
export const generateScoreData = (count: number = 24) => {
  return Array.from({ length: count }, () => generateRandomData(80, 95, 1));
};

/**
 * 生成饼图数据
 */
export const generatePieData = () => {
  return [
    { value: generateRandomData(30, 40), name: '服务态度' },
    { value: generateRandomData(25, 35), name: '业务规范' },
    { value: generateRandomData(20, 30), name: '沟通技巧' },
    { value: generateRandomData(10, 20), name: '合规要求' },
    { value: generateRandomData(8, 15), name: '开场结束' },
    { value: generateRandomData(5, 12), name: '其他问题' }
  ];
};

/**
 * 生成仪表盘数据
 */
export const generateGaugeData = () => {
  return {
    qualityRate: generateRandomData(88, 98, 1),
    aiAccuracy: generateRandomData(94, 99, 1)
  };
};

/**
 * 通用图表主题配置
 */
export const chartTheme = {
  colors: [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', 
    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
  ],
  backgroundColor: 'transparent',
  textStyle: {
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
  }
};

/**
 * 响应式图表配置
 */
export const getResponsiveOption = (baseOption: any, isMobile: boolean = false) => {
  if (isMobile) {
    return {
      ...baseOption,
      grid: {
        ...baseOption.grid,
        left: '5%',
        right: '5%',
        top: '20%',
        bottom: '15%'
      },
      legend: {
        ...baseOption.legend,
        orient: 'horizontal',
        bottom: 0,
        left: 'center'
      }
    };
  }
  return baseOption;
};

/**
 * 动画配置
 */
export const animationConfig = {
  animation: true,
  animationDuration: 1000,
  animationEasing: 'cubicOut',
  animationDelay: (idx: number) => idx * 100
};
