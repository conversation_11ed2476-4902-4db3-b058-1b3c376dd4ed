version: '3.8'

services:
  rule-configuration-system:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3055:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

networks:
  app-network:
    driver: bridge