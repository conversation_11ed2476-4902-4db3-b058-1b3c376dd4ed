import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import MainLayout from './components/layout/MainLayout';
import HomePage from './components/pages/HomePage';
import ComplexExamplePage from './components/pages/ComplexExamplePage';
import KeywordCheckPage from './components/operators/KeywordCheckPage';

/**
 * 主应用组件
 * 配置路由和页面布局
 */
function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<MainLayout />}>
          <Route index element={<HomePage />} />
          <Route path="complex-example" element={<ComplexExamplePage />} />
          <Route path="operators/keyword-check" element={<KeywordCheckPage />} />
          {/* 其他质检算子路由将在这里添加 */}
        </Route>
      </Routes>
    </Router>
  );
}

export default App;
