import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import MainLayout from './components/layout/MainLayout';
import ScrollToTop from './components/common/ScrollToTop';
import HomePage from './components/pages/HomePage';
import ComplexExamplePage from './components/pages/ComplexExamplePage';
import { RuleCompositionPage } from './components/pages/RuleCompositionPage';
import { DetectionScopeDemoPage } from './components/pages/demos/DetectionScopeDemoPage';
import { UserGuidePage } from './components/pages/UserGuidePage';
import { UsageFlowPage } from './components/pages/UsageFlowPage';
import { DetailedGuidePage } from './components/pages/DetailedGuidePage';
import KeywordCheckPage from './components/pages/operators/KeywordCheckPage';
import TextSimilarityPage from './components/pages/operators/TextSimilarityPage';
import RegexCheckPage from './components/pages/operators/RegexCheckPage';
import ContextRepeatPage from './components/pages/operators/ContextRepeatPage';
import EntityCheckPage from './components/pages/operators/EntityCheckPage';
import CallSilenceCheckPage from './components/pages/operators/CallSilenceCheckPage';
import SpeechSpeedCheckPage from './components/pages/operators/SpeechSpeedCheckPage';
import InterruptCheckPage from './components/pages/operators/InterruptCheckPage';
import RoleJudgmentPage from './components/pages/operators/RoleJudgmentPage';
import AbnormalHangupPage from './components/pages/operators/AbnormalHangupPage';
import AbnormalAnswerPage from './components/pages/operators/AbnormalAnswerPage';
import DurationCheckPage from './components/pages/operators/DurationCheckPage';
import EnergyCheckPage from './components/pages/operators/EnergyCheckPage';
import SentenceCountCheckPage from './components/pages/operators/SentenceCountCheckPage';
import { AgentModelPage } from './components/pages/operators/AgentModelPage';
import { CustomerModelPage } from './components/pages/operators/CustomerModelPage';
import LargeModelCheckPage from './components/pages/operators/LargeModelCheckPage';
import { RecordingParamsCheckPage } from './components/pages/operators/RecordingParamsCheckPage';
import { ComprehensiveComplianceExample } from './components/pages/examples/ComprehensiveComplianceExample';
import { CustomerDissatisfactionExample } from './components/pages/examples/CustomerDissatisfactionExample';
import { ProactiveServiceFailureExample } from './components/pages/examples/ProactiveServiceFailureExample';
import { ProactiveVerificationExample } from './components/pages/examples/ProactiveVerificationExample';

import FinalMyReviewTasksPage from './components/pages/final-design/FinalMyReviewTasksPage';
import FinalMultiModeSessionDetailPage from './components/pages/final-design/FinalMultiModeSessionDetailPage';
import { FinalAppealProcessingListPage } from './components/pages/final-design/FinalAppealProcessingListPage';
import { FinalPersonalPerformancePage } from './components/pages/final-design/FinalPersonalPerformancePage';
import { FinalNotificationCenterPage } from './components/pages/final-design/FinalNotificationCenterPage';
import RealTimeAlertCenterPage from './components/pages/RealTimeAlertCenterPage'; // This seems like an older version, will keep for now
import FinalRealTimeAlertCenterPage from './components/pages/final-design/RealTimeAlertCenterPage';
import FinalAgentHomePage from './components/pages/final-design/FinalAgentHomePage';
import FinalTeamLeaderHomePage from './components/pages/final-design/FinalTeamLeaderHomePage';
import FinalReviewerHomePage from './components/pages/final-design/FinalReviewerHomePage';
import FinalSupervisorHomePage from './components/pages/final-design/FinalSupervisorHomePage';
import FinalRuleLibraryPage from './components/pages/final-design/FinalRuleLibraryPage';
import FinalQualityOperationOverviewPage from './components/pages/final-design/FinalQualityOperationOverviewPage';
import FinalServiceQualityDeepAnalysisPage from './components/pages/final-design/FinalServiceQualityDeepAnalysisPage';
import FinalAppealInsightReportPage from './components/pages/final-design/FinalAppealInsightReportPage';
import { ReviewAnalysisReportPage } from './components/pages/final-design/ReviewAnalysisReportPage';
import { QualityDetailQueryPage } from './components/pages/final-design/QualityDetailQueryPage';
import FinalWordLibraryPage from './components/pages/final-design/FinalWordLibraryPage';
import FinalSchemeConfigPage from './components/pages/final-design/FinalSchemeConfigPage';
import DataSourceManagementPage from './components/pages/final-design/DataSourceManagementPage';
import FinalReviewStrategyListPage from './components/pages/final-design/FinalReviewStrategyListPage';
import FinalPlanListPage from './components/pages/final-design/FinalPlanListPage';
import FinalTaskListPage from './components/pages/final-design/FinalTaskListPage';
import FinalTaskDetailPage from './components/pages/final-design/FinalTaskDetailPage';
import DesignGuideExamplePage from './components/pages/final-design/DesignGuideExamplePage';
import FinalModelManagementPage from './components/pages/final-design/FinalModelManagementPage';
import SpeechEngineManagementPage from './components/pages/final-design/SpeechEngineManagementPage';
import NotificationChannelManagementPage from './components/pages/final-design/NotificationChannelManagementPage';
import AlertFollowUpCenterPage from './components/pages/final-design/AlertFollowUpCenterPage';
import HistoricalAlertQueryPage from './components/pages/final-design/HistoricalAlertQueryPage';

/**
 * 主应用组件
 * 配置路由和页面布局
 */
function App() {
  return (
    <Router>
      <ScrollToTop />
      <Routes>
        <Route path="/" element={<MainLayout />}>
          <Route index element={<HomePage />} />

          {/* 阿里智能质检系统学习 (Restored) */}
          <Route path="user-guide" element={<UserGuidePage />} />
          <Route path="usage-flow" element={<UsageFlowPage />} />
          <Route path="detailed-guide" element={<DetailedGuidePage />} />
          <Route path="rule-composition" element={<RuleCompositionPage />} />
          <Route path="demos/detection-scope" element={<DetectionScopeDemoPage />} />
          <Route path="complex-example" element={<ComplexExamplePage />} />
          
          <Route path="operators/keyword-check" element={<KeywordCheckPage />} />
          <Route path="operators/text-similarity" element={<TextSimilarityPage />} />
          <Route path="operators/regex-check" element={<RegexCheckPage />} />
          <Route path="operators/context-repeat" element={<ContextRepeatPage />} />
          <Route path="operators/entity-check" element={<EntityCheckPage />} />
          <Route path="operators/call-silence-check" element={<CallSilenceCheckPage />} />
          <Route path="operators/speech-speed-check" element={<SpeechSpeedCheckPage />} />
          <Route path="operators/interrupt-check" element={<InterruptCheckPage />} />
          <Route path="operators/role-judgment" element={<RoleJudgmentPage />} />
          <Route path="operators/abnormal-hangup" element={<AbnormalHangupPage />} />
          <Route path="operators/abnormal-answer" element={<AbnormalAnswerPage />} />
          <Route path="operators/duration-check" element={<DurationCheckPage />} />
          <Route path="operators/energy-check" element={<EnergyCheckPage />} />
          <Route path="operators/sentence-count-check" element={<SentenceCountCheckPage />} />
          <Route path="operators/agent-model" element={<AgentModelPage />} />
          <Route path="operators/customer-model" element={<CustomerModelPage />} />
          <Route path="operators/large-model-check" element={<LargeModelCheckPage />} />
          <Route path="operators/recording-params-check" element={<RecordingParamsCheckPage />} />

          <Route path="examples/customer-dissatisfaction" element={<CustomerDissatisfactionExample />} />
          <Route path="examples/proactive-verification" element={<ProactiveVerificationExample />} />
          <Route path="examples/comprehensive-compliance" element={<ComprehensiveComplianceExample />} />
          <Route path="examples/proactive-service-failure" element={<ProactiveServiceFailureExample />} />

          {/* Final Design Routes */}
          <Route path="final-design/my-review-tasks" element={<FinalMyReviewTasksPage />} />
          <Route path="final-design/appeal-processing" element={<FinalAppealProcessingListPage />} />
          <Route path="final-design/personal-performance" element={<FinalPersonalPerformancePage />} />
          <Route path="final-design/notification-center" element={<FinalNotificationCenterPage />} />
          <Route path="final-design/alert-follow-up-center" element={<AlertFollowUpCenterPage />} />
          <Route path="final-design/review-workstation/:sessionId" element={<FinalMultiModeSessionDetailPage />} />
          <Route path="final-design/multi-mode-session-detail/:sessionId" element={<FinalMultiModeSessionDetailPage />} />
          <Route path="final-design/agent-homepage" element={<FinalAgentHomePage />} />
          <Route path="final-design/team-leader-homepage" element={<FinalTeamLeaderHomePage />} />
          <Route path="final-design/reviewer-homepage" element={<FinalReviewerHomePage />} />
          <Route path="final-design/supervisor-homepage" element={<FinalSupervisorHomePage />} />
          <Route path="final-design/rule-library" element={<FinalRuleLibraryPage />} />
          <Route path="final-design/quality-operation-overview" element={<FinalQualityOperationOverviewPage />} />
          <Route path="final-design/service-quality-deep-analysis" element={<FinalServiceQualityDeepAnalysisPage />} />
          <Route path="final-design/appeal-insight-report" element={<FinalAppealInsightReportPage />} />
          <Route path="final-design/review-analysis-report" element={<ReviewAnalysisReportPage />} />
          <Route path="final-design/quality-detail-query" element={<QualityDetailQueryPage />} />
          <Route path="final-design/data-source-management" element={<DataSourceManagementPage />} />
          <Route path="final-design/word-library" element={<FinalWordLibraryPage />} />
          <Route path="final-design/quality-scheme-config" element={<FinalSchemeConfigPage />} />
          <Route path="final-design/monitor-directory-config" element={<DataSourceManagementPage />} />
          <Route path="final-design/review-strategy-list" element={<FinalReviewStrategyListPage />} />
          <Route path="final-design/plan-list" element={<FinalPlanListPage />} />
          <Route path="final-design/task-list" element={<FinalTaskListPage />} />
          <Route path="final-design/task-detail/:taskId" element={<FinalTaskDetailPage />} />
          <Route path="final-design/design-guide-example" element={<DesignGuideExamplePage />} />
          <Route path="final-design/model-management" element={<FinalModelManagementPage />} />
          <Route path="final-design/speech-engine-management" element={<SpeechEngineManagementPage />} />
          <Route path="final-design/notification-channel-management" element={<NotificationChannelManagementPage />} />
          <Route path="final-design/real-time-alert-center" element={<FinalRealTimeAlertCenterPage />} />
          <Route path="final-design/historical-alert-query" element={<HistoricalAlertQueryPage />} />
          
          

          {/* Old Real-time Alert Center - keeping temporarily if still in use elsewhere. */} 
          <Route path="qa-workbench/real-time-alerts" element={<RealTimeAlertCenterPage />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;