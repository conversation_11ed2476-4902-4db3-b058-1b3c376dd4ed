# Docker 部署指南

智能质检规则配置系统的Docker部署文档。

## 📋 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB 可用内存
- 至少 1GB 可用磁盘空间

## 🚀 快速开始

### 方法一：使用部署脚本（推荐）

```bash
# 构建并启动服务
./deploy.sh

# 或者分步执行
./deploy.sh prod build  # 构建镜像
./deploy.sh prod up     # 启动服务
```

### 方法二：使用 Docker Compose

```bash
# 构建并启动
docker-compose up -d --build

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 方法三：使用 Docker 命令

```bash
# 构建镜像
docker build -t rule-configuration-system .

# 运行容器
docker run -d -p 3000:80 --name rule-config-app rule-configuration-system
```

## 🔧 配置说明

### 端口配置

- **容器端口**: 80 (nginx)
- **主机端口**: 3055 (可在 docker-compose.yml 中修改)
- **访问地址**: http://localhost:3055

### 环境变量

可以在 `docker-compose.yml` 中添加环境变量：

```yaml
environment:
  - NODE_ENV=production
  - API_BASE_URL=https://your-api.com
  - GEMINI_API_KEY=your-api-key
```

## 📁 文件说明

### Dockerfile
多阶段构建配置：
- **构建阶段**: 使用 Node.js 18 Alpine 镜像构建应用
- **生产阶段**: 使用 Nginx Alpine 镜像提供静态文件服务

### nginx.conf
Nginx 配置文件，包含：
- Gzip 压缩
- 静态资源缓存
- React Router 支持
- 健康检查端点

### docker-compose.yml
Docker Compose 配置：
- 服务定义
- 端口映射
- 健康检查
- 网络配置

### .dockerignore
排除不必要的文件：
- node_modules
- 开发工具配置
- 文档文件
- 日志文件

## 🛠️ 常用命令

### 部署脚本命令

```bash
# 构建镜像
./deploy.sh prod build

# 启动服务
./deploy.sh prod up

# 停止服务
./deploy.sh prod down

# 重启服务
./deploy.sh prod restart

# 查看日志
./deploy.sh prod logs

# 清理资源
./deploy.sh prod cleanup
```

### Docker Compose 命令

```bash
# 启动服务（后台运行）
docker-compose up -d

# 停止服务
docker-compose down

docker compose down

# 重新构建并启动
docker-compose up -d --build

docker compose up -d --build

# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 进入容器
docker-compose exec rule-configuration-system sh
```

### Docker 命令

```bash
# 查看运行中的容器
docker ps

# 查看所有容器
docker ps -a

# 查看镜像
docker images

# 删除容器
docker rm container_name

# 删除镜像
docker rmi image_name

# 查看容器日志
docker logs container_name
```

## 🔍 健康检查

应用提供健康检查端点：

```bash
# 检查应用状态
curl http://localhost:3055/health

# 预期响应
healthy
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :3055
   
   # 修改 docker-compose.yml 中的端口映射
   ports:
     - "8080:80"  # 改为其他端口
   ```

2. **构建失败**
   ```bash
   # 清理 Docker 缓存
   docker system prune -a
   
   # 重新构建
   docker-compose build --no-cache
   ```

3. **服务无法访问**
   ```bash
   # 检查容器状态
   docker-compose ps
   
   # 查看详细日志
   docker-compose logs
   ```

4. **内存不足**
   ```bash
   # 查看 Docker 资源使用
   docker stats
   
   # 清理未使用的资源
   docker system prune
   ```

### 日志查看

```bash
# 查看应用日志
docker-compose logs rule-configuration-system

# 实时查看日志
docker-compose logs -f rule-configuration-system

# 查看最近100行日志
docker-compose logs --tail=100 rule-configuration-system
```

## 🔒 生产环境建议

1. **安全配置**
   - 使用 HTTPS
   - 配置防火墙
   - 定期更新镜像

2. **性能优化**
   - 配置资源限制
   - 使用 CDN
   - 启用缓存

3. **监控和日志**
   - 配置日志收集
   - 设置监控告警
   - 定期备份

4. **高可用性**
   - 使用负载均衡
   - 配置多实例
   - 设置自动重启

## 📝 更新部署

```bash
# 拉取最新代码
git pull

# 重新构建并部署
./deploy.sh prod up

# 或使用 Docker Compose
docker-compose down
docker-compose up -d --build
```

## 🆘 获取帮助

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查 Docker 和 Docker Compose 版本
3. 查看应用日志获取详细错误信息
4. 确保系统资源充足

---

**注意**: 首次部署可能需要较长时间来下载依赖和构建镜像，请耐心等待。