
---

### **页面设计：25. 实时预警中心 (Real-time Alert Center)**

#### **25.1 核心定位与目标**

该页面是质检主管和班组长的**"作战指挥室"**，旨在实时、主动地暴露服务过程中的高风险通话事件。它将监控正在发生或刚刚发生的通话，并根据预设的预警规则进行即时报警，实现从被动分析到主动干预的转变。

#### **25.2 页面设计与功能模块**

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: `实时预警中心`
*   **副标题**: `实时监控高风险通话，实现主动式服务质量管理与干预`
*   **图标**: `Siren` (警笛图标)
*   **核心操作**:
    *   **预警流设置**: 一个包含设置选项的弹出框（Popover），允许用户配置"预警保留时长"等参数。
    *   **接收预警开关**: 一个全局开关 (Switch)，允许管理者临时暂停/恢复接收所有实时预警，以避免在会议等场景下被打扰。

**b. 核心实时指标 (KPI Cards)**
*   **功能**: 以经过美化的卡片形式，动态展示今日关键预警统计，并带有动画效果和趋势指示。
*   **指标内容**:
    *   **今日预警总数**: 累计触发的预警总数，带有较昨日变化的百分比趋势。
    *   **待处理预警**: 状态为"未读"或"处理中"的预警数量，以进度条形式展示占总数的比例。
    *   **最高风险等级**: 当前待处理预警中的最高风险等级（如"严重"），并配有醒目的标签。
    *   **实时通话并发数**: 当前系统正在处理的通话总数，并显示在线状态。

**c. 实时预警流 (Live Alert Feed)**
*   **功能**: 页面的核心区域，以信息流形式按时间倒序展示所有触发的预警事件。该列表可通过手动刷新按钮更新。
*   **视图切换**: 提供Tab页切换视图：
    *   **新预警 (默认)**: 显示所有状态为`未读 (unread)`和`处理中 (processing)`的预警。Tab上应有角标显示数量。
    *   **已处置**: 显示所有状态为`已读 (read)`或`已解决 (resolved)`的预警，用于快速回顾近期处理。
*   **单条预警信息卡片**:
    *   **时间戳**: 精确到秒，如 "14:30:15"。
    *   **风险等级**: 用醒目的颜色条和标签标明（`严重`、`高`、`中`、`低`）。
    *   **触发规则**: `客户提及"投诉"关键词`。
    *   **通话信息**: 坐席姓名、所属班组、脱敏后的客户号码。
    *   **上下文摘要**: 显示触发预警的句子及前后文，关键词高亮。
    *   **实时操作按钮**:
        1.  `标记已读`: 将预警状态变为`已读`。
        2.  `标记处理中`: 将状态变为`处理中`，并记录操作人，避免重复处理。
        3.  `创建跟进任务`: 弹窗创建跟进任务，并将预警状态变为`已解决`。
        4.  `实时监听` (高级): 若技术支持，可实时旁听通话。
        5.  `查看详情`: 点击打开**预警详情抽屉**。

**d. 预警规则热力图 (Heatmap/Ranking List)**
*   **功能**: 统计今日或选定时间段内，各预警规则的触发频率。
*   **展示形式**: 以带有进度条的排行榜形式展示。
*   **内容**:
    *   **Top 5 触发规则**: 帮助管理者发现当前最普遍的风险点，并显示其触发次数与占比。

#### **25.3 核心交互与工作流**

1.  **被动接收与发现**: 管理者进入页面，预警流加载当前预警，高风险事件主动呈现。
2.  **快速评估**: 通过风险等级和上下文摘要，在几秒内判断事件严重性。
3.  **分级响应与状态流转**:
    *   **低风险**: 点击 `标记已读`，事件状态变为 `已读 (read)`。
    *   **中风险**: 点击 `标记处理中`，状态变为 `处理中 (processing)`，进行临时调查。
    *   **高风险**: 点击 `创建跟进任务`，事件状态变为 `已解决 (resolved)`，并将处置流程正式移交给 **[预警跟进中心]**。
4.  **趋势分析**: 通过右侧的热力图，从宏观层面发现问题规律。

---