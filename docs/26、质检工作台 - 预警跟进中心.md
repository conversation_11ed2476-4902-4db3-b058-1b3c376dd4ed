
---

### **页面设计：26. 预警跟进中心 (Alert Follow-up Center)**

#### **26.1 核心定位与目标**

该页面是所有由实时预警转化而来的**跟进任务的管理中枢**。其核心目标是为管理者（主要是质检主管和班组长）提供一个结构化的平台，用于**追踪、处理、复盘和分析**所有高风险事件的后续处置情况。它将瞬时的“警报”转化为持久的、可审计的“管理行为记录”。

#### **26.2 页面设计与功能模块**

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: `预警跟进中心`
*   **副标题**: `追踪和管理所有高风险预警的跟进任务与处置过程`
*   **图标**: `ClipboardList` (带列表的剪贴板)
*   **核心操作**: (暂无，`导出报告`等功能可按需规划)

**b. 页面核心区域**

*   **统一搜索筛选器 (`UnifiedSearchFilter`)**:
    *   **功能**: 提供强大的任务筛选能力。
    *   **筛选字段**:
        *   `任务标题/描述`
        *   `关联的触发规则` (例如，筛选所有“客户投诉”预警产生的任务)
        *   `关联的坐席/班组`
        *   `指派给` (处理人)
        *   `优先级` (高/中/低)
        *   `创建时间` / `截止日期` 范围

*   **任务列表视图 (List View)**:
    *   **视图切换**: 页面采用Tab页切换视图，取代了旧的看板/列表模式切换。
        *   **待处理 (默认)**: 显示所有状态为`待处理 (todo)`的任务。Tab上通过角标显示任务数量。
        *   **已完成**: 显示所有状态为`已完成 (done)`的任务。
    *   **布局**: 采用传统表格，信息密度高，支持排序。
    *   **表格列**: `序号`、`任务标题`、`状态`、`优先级`、`指派给`、`关联坐席/班组`、`创建时间`、`截止日期`、`操作`。
    *   **交互**: 点击“处理”按钮，从右侧滑出**任务详情抽屉**。

**c. 任务详情抽屉 (Task Detail Drawer)**
*   **功能**: 这是处理和查看单个跟进任务的核心界面。
*   **布局**: 采用单栏上下结构。
    *   **任务详情卡片**: 展示任务的核心信息，如状态、优先级、负责人、关联坐席、截止日期、触发规则和任务描述。提供**“查看关联通话详情”**的链接。
    *   **处理结果区域**: 
        *   **处理结果输入框 (`Textarea`)**: **核心区域**。处理人在此处详细记录处置过程和结论。
        *   **附件上传**: 可上传辅导记录、截图等证明材料。
        *   **提交处理结果**: 提交后，任务状态将更新为“已完成”。

#### **26.4 核心交互与工作流**

1.  **任务接收**: 被指派人（如班组长）在**[通知中心]**收到新任务提醒，或直接在本页面“待处理”页签下看到新任务。
2.  **任务处理**:
    *   处理人从列表中找到任务，点击“处理”打开详情抽屉。
    *   通过关联链接回顾原始通话场景。
    *   执行线下辅导或沟通。
    *   返回任务详情抽屉，填写详尽的**处理结果**，并上传相关附件。
    *   点击“提交处理结果”，任务状态更新为“已完成”，并自动归档至“已完成”页签。
3.  **监督**: 质检主管可以随时查看所有任务的进展，并查看已完成任务的处理结果。
4.  **闭环与沉淀**: 任务完成后，其完整的处理过程和最终结果都被完整地保存在系统中，成为可供复盘和分析的管理案例。

---