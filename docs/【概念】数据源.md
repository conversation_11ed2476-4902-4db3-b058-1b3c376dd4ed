### 数据对接的实现方式

#### 1. API 对接 (推荐，最现代化的方式)
*   **描述**: 由呼叫中心系统、CRM 系统等提供标准的 API 接口（通常是 RESTful API），质检系统通过调用这些接口来获取数据。
*   **流程示例**:
    1.  一通电话结束后，呼叫中心的 CTI 系统通过 **Webhook（事件通知）** 实时将 CDR 数据（包含 `Call ID` 和录音文件地址）推送到质检系统。
    2.  质检系统收到通知后，根据录音文件地址去下载录音。
    3.  同时，质检系统还可以根据 CDR 中的坐席工号、客户电话等信息，再去调用 HR 系统和 CRM 系统的 API，拉取更丰富的上下文数据。
*   **优点**:
    *   **实时性高**: 可实现准实时的通话质检。
    *   **解耦性好**: 系统间依赖性低，只要接口不变，各自升级不影响对方。
    *   **安全可控**: 通过 API Key、OAuth 等方式进行认证，权限控制清晰。
*   **缺点**:
    *   依赖源系统提供稳定、完善的 API。如果已有系统比较老旧，可能没有 API 或需要额外开发。

#### 2. 文件传输 (传统但有效的方式)
*   **描述**: 源系统（呼叫中心）定期将数据以文件形式（如 CSV、JSON、XML）和录音文件一起，批量推送到一个共享的存储位置（如 **FTP/SFTP 服务器**）。
*   **流程示例**:
    1.  呼叫中心系统每天凌晨将前一天的所有录音文件上传到 SFTP 服务器的 `audio/` 目录。
    2.  同时，将前一天的所有 CDR 数据导出一个 `cdr_2023-10-27.csv` 文件，上传到 `metadata/` 目录。
    3.  质检系统也定时（如每小时或每天）扫描 SFTP 服务器，拉取新的录音和 CDR 文件进行处理和匹配。
*   **优点**:
    *   **实现简单**: 对源系统的改造要求低，很多老系统都支持导出文件。
    *   **适合批量处理**: 对于非实时性要求高的场景非常适用。
*   **缺点**:
    *   **延迟性高**: 数据不是实时的，质检结果会滞后。
    *   **稳定性风险**: 文件传输可能中断、文件格式可能出错、文件可能不完整。

#### 3. 数据库中间表/视图 (谨慎使用)
*   **描述**: 质检系统通过只读权限，直接访问呼叫中心系统的数据库中的特定表或视图来获取数据。
*   **流程示例**:
    1.  DBA 在呼叫中心数据库中创建一个专门供质检系统使用的只读用户和视图（View）。
    2.  这个视图整合了通话记录、坐席信息等。
    3.  质检系统定期查询这个视图，获取新增的通话记录。
*   **优点**:
    *   **数据访问直接**: 如果网络通畅，速度可能很快。
*   **缺点**:
    *   **高耦合、高风险**: 对源数据库有侵入性，可能影响源系统性能和稳定性。
    *   **安全隐患大**: 数据库账户权限控制不当会导致数据泄露。
    *   **维护困难**: 源数据库表结构一变，质检系统就可能出错。**通常不推荐，除非是最后的选择。**
