
---

### 页面六：申诉处理 (FinalAppealProcessingListPage.tsx)

#### 1. 核心定位与目标
该页面是**质检主管或指定申诉处理人**的工作界面。其核心目标是提供一个集中处理所有坐席申诉的平台，确保申诉流程的公正、高效和可追溯。它与“我的复核任务”页面结构相似，但处理的对象是“申诉”而非“复核”。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "申诉处理"
*   **副标题**: "处理坐席对质检结果提出的申诉"
*   **图标**: `MessageSquareX` (带叉号的对话框)，形象地表示对质检结果的异议和处理。

**b. Tab切换 (待处理 / 已完成)**
同样采用任务管理页面的经典双Tab设计，清晰分离待办和已办事项。
*   **待处理 (`pending`)**:
    *   **显示内容**: 所有已由坐席提交，但尚未处理的申诉请求。
    *   **徽章**: Tab标签上用数字角标显示待处理申诉的数量，提醒管理者工作积压情况。
*   **已完成 (`completed`)**:
    *   **显示内容**: 所有已经过处理（无论通过或驳回）的申诉记录。

**c. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 帮助管理者快速从众多申诉中定位到目标记录。
*   **筛选字段**:
    *   **通用字段**: 记录编号、所属任务、申请坐席、所属班组、AI初检得分范围、申请时间范围、质检结果。
    *   **“已完成”Tab专属字段**: 申诉结果（成功/失败）、处理时间范围、最终得分范围。
*   **交互**: 与复核任务页面的筛选器体验一致，支持展开/收起、查询和重置。

**d. 申诉任务表格 (`Table`)**
表格是页面的核心，展示了详细的申诉信息列表。

*   **“待处理”表格列**:
    *   `序号`
    *   `记录编号`
    *   `所属任务`
    *   `申诉坐席`: 显示坐席姓名和工号。
    *   `所属班组`
    *   `通话时长`
    *   `AI初检得分`: 原始AI评分。
    *   `人工复核得分`: 如果该记录经过人工复核，则显示复核后的分数。这是申诉处理的重要参考。
    *   `质检结果`: 根据当前得分显示的“合格”或“不合格”。
    *   `申请理由`: **核心信息**，简要展示坐席提出的申诉理由，通常可以悬浮查看全文。
    *   `申请时间`
    *   `操作`: 提供一个醒目的 **“处理”** 按钮。

*   **“已完成”表格列**:
    *   除了“待处理”的大部分列外，新增/替换了以下列：
    *   `最终得分`: 显示申诉处理后的最终分数。
    *   `申请结果`: 用“成功”或“失败”的徽章清晰标明处理结果。
    *   `处理时间`
    *   `操作`: 按钮变为 **“查看”**，允许管理者回顾历史申诉的处理过程和依据。

*   **表格特点**:
    *   **信息密度高**: 表格包含了处理申诉所需的所有关键上下文信息，如原始分数、复核分数、坐席理由等。
    *   **状态清晰**: 通过不同颜色的徽章（如申诉结果、质检结果）和标签，让管理者可以快速识别记录状态。

**e. 分页组件 (`UnifiedPagination`)**
*   位于表格下方，用于处理大量的申诉记录，保证页面性能。

#### 3. 核心交互与操作
*   **申诉处理流程**: 管理者的主要动线是：查看“待处理”列表 -> 选择一个申诉任务 -> 点击“处理” -> 跳转到 **[多模式会话详情页]** (`FinalMultiModeSessionDetailPage`，申诉处理模式) -> 在详情页中，结合录音、文本、AI结果和坐席理由，做出“同意申诉”或“驳回申诉”的决定，并填写处理意见 -> 完成后，该申诉任务会自动移到“已完成”列表。
*   **公正性与可追溯性**: “已完成”列表和详情查看功能，保证了每一次申诉处理都有据可查，体现了流程的公正性和透明度。
*   **管理洞察**: 通过筛选特定坐席或班组的申诉记录，管理者可以发现潜在的沟通问题或培训需求。

---