
---

### **页面设计：32. 通知渠道管理 (Notification Channel Management)**

#### **32.1 核心定位与目标**

该页面是系统的**“通讯总机”**，是一个纯粹的后台技术配置页面，服务于系统管理员。其核心目标是提供一个统一的、安全的界面，用于配置和管理系统向外部发送通知的所有**技术通道 (Channels)**。它将“发送什么内容（What）”和“何时发送（When）”（由预警规则定义）与“如何发送（How）”（由本页面定义）进行彻底解耦，极大地提升了系统的灵可活性和集成能力。

#### **32.2 页面设计与功能模块**

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: `通知渠道管理`
*   **副标题**: `配置系统用于发送邮件、Webhook等通知的外部服务通道`
*   **图标**: `Send` (纸飞机/发送图标)

**b. 页面布局**
建议采用**卡片式布局**，每个可配置的通知渠道类型作为一个独立的卡片，内容区隔清晰，便于管理员聚焦于单项配置。

---

#### **模块一：邮件服务配置卡片 (SMTP Configuration)**

*   **卡片标题**: `邮件服务 (SMTP)`
*   **状态指示器**: 在标题旁边显示一个状态徽章，如`未配置`、`连接正常` (绿色)、`连接失败` (红色)。
*   **表单字段 (Form Fields)**:
    1.  **SMTP 服务器地址 (Host)**: 输入框，例如 `smtp.example.com`。
    2.  **端口 (Port)**: 输入框，例如 `465` 或 `587`。
    3.  **加密方式 (Security)**: 下拉选择框，选项为 `无`、`SSL`、`TLS/STARTTLS`。
    4.  **发件人邮箱 (Sender Email)**: 输入框，系统发送邮件时显示的发件人地址。
    5.  **发件人名称 (Sender Name)**: 输入框，可选，邮件中显示的发件人昵称（如“智能质检系统预警中心”）。
    6.  **认证用户名 (Username)**: 输入框，通常与发件人邮箱相同。
    7.  **认证密码/授权码 (Password/Auth Code)**: 密码输入框。提示用户这里通常应填写邮箱服务商提供的“授权码”而非登录密码，以增强安全性。
*   **卡片底部操作 (Card Actions)**:
    *   `保存配置`: 点击后，系统会尝试用新配置连接SMTP服务器。连接成功则保存并更新状态为“连接正常”，失败则提示错误信息。
    *   `发送测试邮件`: 弹出一个小模态框，让管理员输入一个收件箱地址，点击发送后，系统会用当前已保存的配置发送一封测试邮件，用于验证配置是否完全正确。

---

#### **模块二：Webhook 端点管理卡片 (Webhook Endpoint Management)**

*   **卡片标题**: `Webhook 端点`
*   **功能说明**: 在卡片顶部附有一行简短的说明文字：“Webhook可用于将预警事件实时推送到钉钉、企业微信、Slack或任何支持Webhook的自定义系统中。”
*   **核心操作**: `新建Webhook` 按钮，点击后弹出用于创建新Webhook的模态框。
*   **Webhook 列表表格 (`Table`)**:
    *   **功能**: 展示所有已创建的Webhook端点。
    *   **表格列**:
        *   `名称`: 管理员为该Webhook起的别名，如“钉钉-质检主管群机器人”。
        *   `URL`: 推送目标的URL地址（出于安全考虑，可以部分打码）。
        *   `状态`: `已启用` / `已禁用` 的开关 (Switch)。
        *   `操作`:
            *   `编辑`: 打开模态框修改配置。
            *   `测试`: 向该URL发送一次预定义的、包含示例数据的测试推送。
            *   `删除`: 删除该Webhook配置。

**新建/编辑 Webhook 模态框**:
*   **标题**: `新建/编辑 Webhook 端点`
*   **表单字段**:
    1.  **名称 (Name)**: 必填，用于在预警规则中进行选择。
    2.  **URL**: 必填，接收数据的接口地址。
    3.  **请求方法 (Method)**: 下拉选择 `POST` (常用) 或 `GET`。
    4.  **自定义请求头 (Headers)**: 一个可动态增减的键值对列表，用于添加 `Content-Type`、`Authorization` 等认证信息。
    5.  **请求体模板 (Body Template)**: **核心高级功能**。
        *   **控件**: 一个代码/文本编辑器（如 Monaco Editor），支持JSON语法高亮。
        *   **功能**: 允许管理员自定义推送出去的JSON数据结构，以适配不同接收端的要求。
        *   **变量支持**: 提供一个变量列表或帮助文档，告知管理员可以在模板中使用哪些预设变量，例如：
            *   `{{alert.id}}` - 预警ID
            *   `{{alert.level}}` - 预警等级
            *   `{{rule.name}}` - 触发的规则名称
            *   `{{agent.name}}` - 关联的坐席姓名
            *   `{{agent.team}}` - 关联的班组
            *   `{{call.id}}` - 关联的通话ID
            *   `{{context.summary}}` - 上下文摘要
            *   `{{timestamp}}` - 触发时间
        *   **示例**: 管理员可以轻松配置出符合钉钉机器人格式的模板：
            ```json
            {
              "msgtype": "markdown",
              "markdown": {
                "title": "质检预警：{{rule.name}}",
                "text": "#### 质检预警：{{alert.level}} \n> **坐席：** {{agent.name}} ({{agent.team}}) \n> **规则：** {{rule.name}} \n> **摘要：** {{context.summary}} \n> [点击查看详情](http://your-system.com/alerts/{{alert.id}})"
              }
            }
            ```

---

#### **模块三：系统弹窗配置卡片 (System Pop-up Configuration)**

*   **卡片标题**: `系统浏览器弹窗`
*   **功能**: 配置系统内部的强提醒弹窗行为。
*   **表单字段**:
    1.  **全局启用/禁用 (Global Switch)**: 一个总开关，控制是否在系统内使用弹窗通知。
    2.  **自动关闭时间 (Auto-close Delay)**: 数字输入框，单位为秒。管理员可以设置弹窗在多少秒后自动消失，`0` 代表不自动关闭，需要用户手动点击。
    3.  **声音提示 (Sound Alert)**:
        *   一个开关，用于启用或禁用声音提示。
        *   一个下拉选择框，提供几种内置的提示音效（如“叮咚”、“水滴”、“警报”）。
        *   （可选）一个上传按钮，允许上传自定义的提示音文件。

#### **32.3 核心交互与设计哲学**

*   **解耦与抽象**: 页面将通知的**技术实现**（如何配置SMTP、如何构造Webhook）与业务逻辑（何时发送通知）完全分离开。业务人员在配置预警规则时，只需从下拉框中选择一个已配置好的、有业务含义的通知渠道（如“发送到钉钉主管群”），而无需关心其背后的技术细节。
*   **灵活性与集成性**: **Webhook** 的设计是系统与外部世界连接的关键。通过灵活的请求体模板和变量支持，系统可以轻松地与钉钉、企业微信、Slack、飞书、甚至自定义的工单系统、CRM等进行无代码或低代码集成，极大扩展了系统的生态和自动化能力。
*   **可用性与测试**: 每个配置项都提供了**“测试”**功能。管理员在保存配置后，可以立即验证其是否正常工作，避免了在生产环境中出现“通知发送失败”的问题，提高了系统的可靠性。