
---

### 页面十一：质检计划管理 (FinalPlanListPage.tsx)

#### 1. 核心定位与目标
该页面是**自动化、周期性质检任务**的配置中心。其核心目标是让管理者能够创建和管理一系列“质检计划 (Plan)”，这些计划会由系统在预设的时间自动触发，生成具体的“质检任务 (Task)”。例如，创建一个“每日服务质量抽检计划”。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "质检计划管理"
*   **副标题**: "创建和管理周期性执行的自动质检计划。"
*   **图标**: `Calendar` (日历图标)，象征着计划和周期性。
*   **核心操作**: `新建计划` 按钮，点击后弹出创建计划的抽屉表单 (`FinalCreatePlanPage.tsx`)。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 快速查找特定的自动化计划。
*   **筛选字段**:
    *   `计划名称`
    *   `质检方案`: 按所使用的方案筛选。
    *   `创建人`
    *   `创建时间`: 按计划的创建日期范围筛选。

**c. Tab切换**
*   **功能**: 根据计划的**生命周期状态**对列表进行分类，方便管理。
*   **分类**:
    *   `全部`
    *   `活动中`: 正在按周期正常运行的计划。
    *   `已暂停`: 被手动暂停，暂时不生成任务的计划。
    *   `已过期`: 已达到设定的结束日期，不再运行的计划。

**d. 计划列表表格 (`Table`)**
*   **布局**: 以表格形式展示所有配置的质检计划。
*   **表格列**:
    *   `序号`
    *   `计划名称`: 显示计划名称及其描述。
    *   `计划状态`: 用不同颜色的徽章显示“活动中”、“已暂停”等状态。
    *   `执行周期`: 清晰地描述计划的执行频率，例如“每天 02:00”、“每周 09:00”。
    *   `上次运行时间`: 显示该计划最近一次生成任务的时间。
    *   `上次运行状态`: 用徽章显示上次任务生成是否“成功”或“失败”。
    *   `质检方案`: 显示该计划所采用的评分方案。
    *   `创建人`
    *   `创建时间`
    *   `操作`:
        *   **编辑 (`Edit`图标)**: 修改计划的配置。
        *   **启动/暂停 (`PlayCircle`/`PauseCircle`图标)**: 切换计划的“活动中”和“已暂停”状态。
        *   **查看执行历史 (`Eye`图标)**: **关键链接**，点击后会跳转到 **[质检任务管理]** 页面 (`FinalTaskListPage`)，并自动筛选出所有由该计划生成的历史任务。
        *   **删除 (`Trash2`图标)**: 删除该计划。

**e. 创建/编辑计划的抽屉表单 (`FinalCreatePlanPage.tsx`)**
这是定义一个自动化计划的核心界面。

*   **表单内容**:
    *   **第一部分：基本信息**
        *   `计划名称` 和 `计划描述`。
    *   **第二部分：执行计划**
        *   `重复频率`: 选择“每天”、“每周”或“每月”。
        *   `执行时间`: 设置在重复周期内的具体触发时间点（时:分）。
        *   `结束于`: 可选，设置一个计划的失效日期。
    *   **第三部分：质检范围**
        *   `数据来源`: 选择要从哪个数据源获取通话记录。
        *   `通话时间范围`: **核心配置**。这里必须选择一个**动态的、相对的时间范围**，如“过去24小时”、“上周”、“上月”。系统会在每次执行时，根据这个动态范围去捞取数据。
        *   `质检对象`: 定义要对哪些坐席进行质检，选项包括“全部坐席”或“按组织架构选择”（可以选择特定部门或班组）。
    *   **第四部分：质检规则**
        *   `质检方案`: 从已启用的方案列表中选择一个。
        *   `质检模式`:
            *   **全量质检**: 对范围内所有通话进行质检。
            *   **抽样质检**: 只对范围内的一部分通话进行质检，并需要设置一个**抽样比例**（例如10%）。

#### 3. 核心交互与操作
*   **自动化配置**: 核心是“一次配置，永久（或周期性）运行”。用户定义好“WHEN”（执行计划）、“WHO”（质检对象）、“WHAT”（通话范围）、“HOW”（质检方案和模式）之后，系统将自动处理后续的质检任务生成。
*   **计划与任务的关联**: “计划”是“任务”的模板和生成器。通过“查看执行历史”按钮，将抽象的计划与具体的执行结果（任务列表）关联起来，形成了完整的追溯链。
*   **动态时间**: “动态时间范围”是实现自动化的关键。它确保了每次计划运行时，处理的都是最新的、符合条件的数据，而无需人工干预。

---