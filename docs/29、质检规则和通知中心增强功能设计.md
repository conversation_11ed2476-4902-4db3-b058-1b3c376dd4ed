
---

### **增强设计：29. 质检规则管理 (`FinalRuleLibraryPage` & `FinalCreateRuleForm`)**

#### **29.1 改造目标**
让用户在创建和管理质检规则时，能够直接配置其是否用于实时预警以及相关的预警参数。这是将“事后质检规则”与“事中预警规则”合二为一的关键。

#### **29.2 界面与功能变更**

**a. `FinalCreateRuleForm` (创建/编辑规则表单) - 新增“预警配置”区域**
*   在表单中增加一个独立的卡片或区域，标题为“预警配置”。
*   **新增字段**:
    1.  **启用实时预警 (Switch)**: 一个开关，默认关闭。只有打开后，下方的预警配置才可用。
    2.  **预警等级 (Select)**: 下拉选择 `严重`、`高`、`中`、`低`。
    3.  **通知方式 (Checkbox Group)**: 多选框，选项包括 `在预警中心显示`、`系统弹窗通知`、`发送邮件`、`Webhook`。
    4.  **Webhook 渠道选择 (Select)**: 当“通知方式”选中 `Webhook` 时，会动态出现一个多选下拉框，用于选择具体要通知的、已配置好的Webhook渠道。
    5.  **通知对象 (Checkbox Group)**: 多选框，选项包括 `该坐席的班组长`、`所有质检主管`。

**b. `FinalRuleLibraryPage` (规则列表页) - 增加信息展示**
*   **新增筛选条件**: 在 `UnifiedSearchFilter` 中增加“是否预警规则”的筛选条件（是/否）。
*   **表格列调整**:
    *   在“规则名称”列，对于已启用预警的规则，可以增加一个小的`Siren`警笛图标，以示区分。
    *   可以考虑增加一列“预警等级”，用于快速查看和排序。

---

### **增强设计：30. 通知中心 (`FinalNotificationCenterPage`)**

#### **30.1 改造目标**
使通知中心能够承载和区分新引入的“实时预警”和“预警跟进任务”两类通知，并优化其展示和交互。

#### **30.2 界面与功能变更**

**a. 左侧分类导航调整**
*   **新增分类**:
    *   **实时预警**: 图标为 `Siren`。专门汇集由预警规则触发的实时警报。
*   **“任务提醒”分类**: 该分类用于展示多种任务相关通知，包括新分配的“预警跟进任务”。

**b. 通知卡片样式与内容适配**
*   **实时预警通知卡片**:
    *   **样式**: 通过添加一个醒目的**红色分类标签** (`bg-red-100 text-red-800`)，在视觉上与其他通知明显区分。
    *   **内容**: 突出显示`预警等级`、`触发规则`和`上下文摘要`。
    *   **交互**: 点击整个通知区域可直接跳转到**[多模式会话详情页]**进行复核。
*   **预警跟进任务通知卡片 (归属于'任务提醒'分类下)**:
    *   **样式**: 通过添加**黄色分类标签** (`bg-yellow-100 text-yellow-800`) 进行识别。
    *   **内容**: 突出显示`任务标题`等信息。
    *   **交互**: 点击跳转到**[预警跟进中心]**页面。

---