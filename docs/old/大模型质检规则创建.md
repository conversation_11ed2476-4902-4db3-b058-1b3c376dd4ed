
---

**首页 > 智能对话分析 > 操作指南 > 智能对话分析（新版） > 质检方案管理 > 大模型质检规则创建**

产品详情 | ❤️ 我的收藏

# **大模型质检规则创建**
更新时间: 2025-01-22 13:57:47

## **使用前提**
若想创建该规则，规则种类只有**普通质检**、**会话组质检**、**纯人工质检**，没有看到**大模型质检**。
说明您当前未购买AI，故无法创建大模型质检规则。如需创建，请直接联系我们进行加白，同时需要提供 UID 以及企业信息提交工单联系我们，进行加白操作。

**(图片内容为“新建规则”弹窗，其中“规则种类”没有“大模型质检规则”选项)**

## **功能概述**
在智能对话分析中，有四种质检规则：**普通质检规则**、**大模型质检规则**、**人工质检规则**、**会话质检规则**。其中**大模型质检规则**是基于深度学习的语义理解能力而针对对话语义的质检方案的大模型，为帮助各类产品提升智能化和分析能力，在智能能力，只需提供质检场景下该场景下检测维度和描述，即可完成相应质检需求。

## **功能入口**
在登录智能对话分析系统控制台后，点击「**质检规则配置 > 新建规则 > 大模型质检规则**」，即可以进入到**功能配置**界面。新建规则的基本信息可参考 [2.基本信息填写](链接)。

## **功能配置**
### **新建规则**
在新建规则后，进入到新建规则配置界面，新建大模型质检规则的配置内容包括两大类：**1.基本信息**、**2.条件配置**、**3.评分配置**。

### **基本信息**
基本信息包括：**规则名称**、**规则种类**、**备注**、**重要程度**、**生效时间**、**规则类型**、**适用业务**、**人工复核**。您可以根据实际的业务需求来对基本信息的内容自定义配置。

**(图片内容为“新建规则”的“基本信息”配置界面)**

> **① 说明** 需要重点介绍的配置项：
> *   **重要程度**：可以理解是规则本身的一个标签，在复核时，会话命中的规则会以重要程度的形式来表示。
> *   **生效时间**：可以理解是规则的生效时间，可以选择全周期生效或者特定周期生效。
> *   **规则类型**：可以理解是规则的本身的一个标签，您可以自定义或从定义的规则类型添加。
> *   **适用业务**：可以理解是规则的本身的一个标签，同样也可以通过自定义的方式添加更多的类型。
> *   **人工复核**：选择需要后，此规则可以由人工进行再次复核。如果选择不需要，则此规则命中后，复核状态将自动置为“已复核”，无法人工进行复核。不勾选适用于命中准确率高，无需人工复核的规则。

### **条件配置**
条件配置是质检规则的主体部分，主要是配置条件和条件之间的“逻辑关系”。其主要包括两大配置内容：**1.检测内容** **2.检测条件**。

#### **检测内容**
创建好了质检任意条件之后，可以设置不同的逻辑组织条件关系组合，以满足业务中需要的质检场景需求。具体可参考[质检任意条件逻辑配置说明](链接)。

#### **检测条件**
检测条件分为**自定义检测条件**、**预置检测条件**。
**注意**：预置检测条件是系统内置的，也属于自定义检测条件，支持选择后可以自定义修改。

#### **自定义检测条件**
*   **场景名称**：服务质检的对话场景描述，比如：手机销售场景，不超过100字。
*   **背景知识**：可以提供增强模型场景理解的背景知识，可以提升模型识别效果。如：客服销售客户某个从未买过该类理财产品，则属于首销场景，如客服没有提到该理财产品，仅提及稳健类资产、权益类资产、每天派息、见好就收，则不属于首销场景。
*   **检测维度**：服务质检的检测维度列表，包含质检维度名称和定义，即需要大模型以什么样的标准来判断该维度是否命中。
*   **维度描述**：服务质检的规则维度名称，比如：到店迎接-欢迎语，不能超过60字。
*   **维度描述**：服务质检的规则维度定义，即该维度是否命中的具体描述，比如：销售在开场白的时候主动向客户打招呼进行欢迎，不超过400字。
*   **命中条件**：输入大模型，用于判断什么情况下，该规则应该命中。如：客服在对话过程中出现辱骂客户话术，则最终结果判为命中质检项，不超过800字。
*   **命中条件**：输入大模型，用于判断什么情况下，该规则不会命中。如：客服说“欢迎您说光临，这就是我们正常流程，你不要曲解啊”等话术，则最终结果判为未命中质检项，不超过800字。

### **保存规则**
在新建规则后也可以先完成对**基本信息**和**条件配置**后，点击“**确定**”即可保存此规则，如果需要测试，则单击“**保存并测试**”对其规则进行保存与测试。

**(图片内容为规则配置界面，包含多个检测维度的列表)**

## **功能管理**
您可以随时在后台管理里质检规则的配置，实现对规则的增、删、改、查，也包括**编辑**、**复制**、**测试**和**删除**，同时支持对质检结果进行复核。

### **编辑规则**
首先在质检规则配置界面下，单击“**编辑**”，进入到规则配置界面。
用户可以根据自己的需求来修改目前质检规则的规则信息、条件等配置信息。

**(图片内容为“质检规则配置”列表页和点击“编辑”后进入规则配置界面的截图)**

### **复制规则**
它也可以根据实际业务需求来选择“**复制**”，即可以复制并且可以按照自己的需要进行修改所需要的规则信息、条件等配置信息。

**(图片内容为“质检规则配置”列表页和点击“复制”后进入规则配置界面的截图)**

### **测试规则**
首先在这里使用预置检测条件汽车销售类，不规范服务类现检查。

*   用户可以在质检规则配置界面下，单击“**测试**”，进入到规则测试界面。
*   在规则列表中的操作中点击测试
    可以选择三种输入方式：
    1.  **文本输入**：用户可以自定义文本填写对话内容，并且可以自定义地设置说话者与客户角色。
    2.  **文本复制粘贴**：从 Excel 中复制对话内容，需要包含角色、开始时间、语速、对话内容等信息。
    3.  **从数据任务或呼叫中心任务中选取**：可以选择呼叫中心任务或数据任务集中的录音来进行测试，直接选择输入会话名称。
*   测试结果：也会包含是否命中，以及命中信息。

**(图片内容为规则测试结果的弹窗截图)**

*   在编辑规则后，可以点击“**保存并测试**”进入规则测试界面。
如果需要多次修改测试维度可以在编辑规则界面后续选择保存并测试，也会弹出测试界面，同时检测维度也会变为修改后的维度。如：这里将检测维度“客服承认自己错误”，依旧可以命中。

**(图片内容为修改规则后进行测试的界面截图)**

### **删除规则**
在测试完成规则后，您可以选择对规则进行删除不需要的规则。

**(图片内容为在“质检规则配置”列表页点击“删除”并确认的截图)**

---
### **本页导读**
*   使用前提
*   功能概述
*   功能入口
*   功能配置
    *   新建规则
    *   保存规则
*   功能管理
    *   编辑规则
    *   复制规则
    *   测试规则
    *   删除规则