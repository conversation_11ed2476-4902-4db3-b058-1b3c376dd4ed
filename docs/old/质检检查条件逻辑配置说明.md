创建好了质检检查条件之后，可以设置不同的逻辑条件关系组合，以满足业务中需要的质检场景需求。

-   **以下条件全部满足：** 要同时满足所有条件，质检规则才算命中。 例如，质检规则A包括a、b、c三个条件，需要三个条件全部命中，那么质检规则A才算命中。
    
-   **以下条件任一满足：** 满足任意一个条件，质检规则都算命中。 例如，质检规则A包括a、b、c三个条件，命中三个条件中的任意一个，质检规则A都算命中。
    
-   **以下条件任一不满足：** 任意一个条件不满足，质检规则都算命中。 例如，质检规则A包括a、b、c三个条件，三个条件不满足任意其中一个，质检规则A都算是命中的。
    
-   **自定义条件之间的逻辑关系：**
    
    -   **功能介绍：**逻辑关系的逻辑运算符（&&、||、! ）是计算机程序语言中的一种运算符，运算的最终结果只有“真”和“假”两个值。在质检规则配置时，完整自定义逻辑关系通过逻辑运算后，表达式最终结果为“真”，则质检规则算为“命中”；为“假”，则质检规则算为“未命中”。 本文仅给出基础的功能使用说明，如果有更多问题，可以咨询客服，或者查阅专业计算机知识进行了解。
        
    -   **逻辑运算优先级：** 整体上，“！”的运算级别最高，最先计算；括号的优先级次之； 最后，按照从左到右的顺序进行计算，其中“&&”跟“||”是同级。例如，表达式 a||(b&&!c)， 第一步计算“!c”的结果，第二步计算“(b&&!c)”的结果，最后计算“a||(b&&!c)”的结果。
        
-   **使用示例：**例如有三个条件a、b、c，条件之间的逻辑关系，设置方法如下：
    

a、a&&b&&c：满足所有的条件；

b、a||b||c：满足条件之一即可；

c、a&&b&&!c：满足条件a和条件b，并且不满足条件c；

d、a||(b&&!c)：满足条件a，或者满足条件b并且不满足条件c；

e、!a&&!b&&!c: 不满足所有条件；