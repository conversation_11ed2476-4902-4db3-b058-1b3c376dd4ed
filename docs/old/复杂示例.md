**← 规则名称**

**基本信息** `编辑`

| | | | |
| :--- | :--- | :--- | :--- |
| **规则名称** | 【预置】业务规则示例 | **重要程度** | 轻度 |
| **规则种类** | 普通质检规则 | **生效时间** | 全周期生效 |
| **备注** | 规则目的：检测是否未按公司业务流程执行（检测上下文逻辑关系）；使用的条件类型：包含某些关键词和符合某个特定话术；测试文本命中原因：条件逻辑关系是有两种情况，一种办过信用卡，一种没办过。文本是没有办过信用卡的逻辑分支，由于条件H未命中，所以触发规则。 | **规则类型** | 服务规范 |
| | | **适用业务** | -- |
| | | **人工复核** | -- |

---

**条件配置**

| | |
| :--- | :--- |
| **检测内容** | 自定义条件之间的逻辑关系 |
| | (a && b && !c) || (a && d && !(e && f) && g && h) |

**查看条件**

**a** `^` **正则表达式检查** ①
| | |
| :--- | :--- |
| **检测角色** | 客服 |
| **检测范围** | 全文 |
| | `默认使用的匹配策略是：不区分大小写、多行匹配、换行符/n也算作一个字符。` `如何使用正则表达式` |
| **\* 命中** | .\*(拥有\|持有\|办过).\*(卡\|信用卡).\* |
| **排除** | 输入自然语言，AI帮您自动优化正则表达式 |
| **扩展功能** | \[ \] 单句话内生效 |
| **测试** | `输入测试语句按enter键进行测试。` `测试` |

**b** `^` **关键词检查** ②
| | |
| :--- | :--- |
| **检测角色** | 客户 |
| **前置条件** | 条件a |
| **条件** | 第 1 次命中 |
| **检测范围** | 前置条件命中位置前后 |
| **范围** | 第 1 ~ 2 句 |
| **关键词** | `输入按enter键添加关键词，多个可用逗号隔开。` `办过` |
| **分析方式** | ◎ 单句分析 ○ 多句分析 |
| **检测类型** | ◎ 包含任意一个关键词 ○ 包含全部上述关键词 ○ 包含任意N个关键词 ○ 全部不包含 |
| **扩展功能** | \[ \] 单句话内生效 |

**c** `^` **关键词检查** ③
| | |
| :--- | :--- |
| **检测角色** | 客服 |
| **前置条件** | 条件b |
| **条件** | 第 1 次命中 |
| **检测范围** | 前置条件命中位置前后 |
| **范围** | 第 1 ~ 2 句 |
| **关键词** | `输入按enter键添加关键词，多个可用逗号隔开。` `发卡行` `额度` |
| **分析方式** | ◎ 单句分析 ○ 多句分析 |
| **检测类型** | ◎ 包含任意一个关键词 ○ 包含全部上述关键词 ○ 包含任意N个关键词 ○ 全部不包含 |
| **扩展功能** | \[ \] 单句话内生效 |

**d** `^` **关键词检查** ④
| | |
| :--- | :--- |
| **检测角色** | 客服 |
| **前置条件** | 条件a |
| **条件** | 第 1 次命中 |
| **检测范围** | 前置条件命中位置前后 |
| **范围** | 第 1 ~ 20 句 |
| **关键词** | `输入按enter键添加关键词，多个可用逗号隔开。` `工作单位` `公司名称` `公司全称` |
| **分析方式** | ◎ 单句分析 ○ 多句分析 |
| **检测类型** | ◎ 包含任意一个关键词 ○ 包含全部上述关键词 ○ 包含任意N个关键词 ○ 全部不包含 |
| **扩展功能** | \[ \] 单句话内生效 |

**e** `^` **关键词检查** ⑤
| | |
| :--- | :--- |
| **检测角色** | 客服 |
| **前置条件** | 条件d |
| **条件** | 第 1 次命中 |
| **检测范围** | 前置条件命中位置前后 |
| **范围** | 第 1 ~ 20 句 |
| **关键词** | `输入按enter键添加关键词，多个可用逗号隔开。` `年收入` `税前` |
| **分析方式** | ◎ 单句分析 ○ 多句分析 |
| **检测类型** | ◎ 包含任意一个关键词 ○ 包含全部上述关键词 ○ 包含任意N个关键词 ○ 全部不包含 |
| **扩展功能** | \[ \] 单句话内生效 |

**f** `^` **关键词检查** ⑥
| | |
| :--- | :--- |
| **检测角色** | 客服 |
| **前置条件** | 条件d |
| **条件** | 第 1 次命中 |
| **检测范围** | 前置条件命中位置前后 |
| **范围** | 第 1 ~ 20 句 |
| **关键词** | `输入按enter键添加关键词，多个可用逗号隔开。` `年收入` `税前` |
| **分析方式** | ◎ 单句分析 ○ 多句分析 |
| **检测类型** | ○ 包含任意一个关键词 ◎ 包含全部上述关键词 ○ 包含任意N个关键词 ○ 全部不包含 |
| **扩展功能** | \[ \] 单句话内生效 |

**g** `^` **正则表达式检查** ⑦
| | |
| :--- | :--- |
| **检测角色** | 客服 |
| **前置条件** | 条件d |
| **条件** | 第 1 次命中 |
| **检测范围** | 前置条件命中位置前后 |
| **范围** | 第 1 ~ 20 句 |
| | `默认使用的匹配策略是：不区分大小写、多行匹配、换行符/n也算作一个字符。` `如何使用正则表达式` |
| **\* 命中** | .\*名下.\*(房\|车).\* |
| **排除** | 输入自然语言，AI帮您自动优化正则表达式 |
| **扩展功能** | \[ \] 单句话内生效 |
| **测试** | `输入测试语句按enter键进行测试。` `测试` |

**h** `^` **关键词检查** ⑧
| | |
| :--- | :--- |
| **检测角色** | 客服 |
| **前置条件** | 条件d |
| **条件** | 第 1 次命中 |
| **检测范围** | 前置条件命中位置前后 |
| **范围** | 第 1 ~ 20 句 |
| **关键词** | `输入按enter键添加关键词，多个可用逗号隔开。` `真实` `准确` |
| **分析方式** | ◎ 单句分析 ○ 多句分析 |
| **检测类型** | ○ 包含任意一个关键词 ◎ 包含全部上述关键词 ○ 包含任意N个关键词 ○ 全部不包含 |
| **扩展功能** | \[ \] 单句话内生效 |

`+ 新增条件`

---

**评分配置**

**规则评分**

◎ **按是否命中评分**

**命中该规则**
◎ 给客服人员评分 `减` `10` 分
○ 给客服人员一次性评分

`确定` `保存并测试` `取消`

----------------------------------------------------------------------------------------------------------------------------------------------------

# 规则配置系统 - 复杂示例完整案例

## 📋 项目概述

本项目是一个基于 React + TypeScript 的规则配置系统，用于构建和管理复杂的业务规则。系统支持多条件配置、逻辑表达式组合、实时测试验证和可视化展示，特别适用于客服质检、合规检测等业务场景。

### 🎯 核心特性

- **多条件配置**: 支持正则表达式检查、关键词检查等多种条件类型
- **复杂逻辑**: 支持条件间的依赖关系和复杂布尔表达式
- **实时测试**: 内置测试功能，支持实际对话验证
- **可视化展示**: 提供依赖关系图、逻辑流程图、决策树等多种可视化
- **规则解释**: 详细的业务场景说明和话术示例

## 🏗️ 系统架构

### 技术栈
- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **图标库**: Lucide React
- **状态管理**: React Hooks (useState, useEffect)

### 项目结构
```
rule-configuration-system/
├── components/
│   ├── BasicInfoSection.tsx           # 基本信息配置
│   ├── ConditionBlock.tsx             # 条件配置块
│   ├── ConditionConfigSection.tsx     # 条件配置主组件
│   ├── RuleExplanation.tsx            # 规则原理详解
│   ├── RuleVisualization.tsx          # 规则可视化
│   ├── RuleTester.tsx                 # 规则测试功能
│   ├── ScoringConfigSection.tsx       # 评分配置
│   ├── FooterActions.tsx              # 底部操作
│   ├── PageTitle.tsx                  # 页面标题
│   └── common/
│       └── icons.tsx                  # 图标组件
├── types.ts                           # TypeScript 类型定义
├── App.tsx                            # 主应用组件
├── docs/
│   └── 复杂示例.md                    # 项目文档
└── README.md                          # 项目说明
```

## 🔧 实现详情

### 1. 规则配置核心逻辑

#### 逻辑表达式
```typescript
(a&&b&&!c)||(a&&d&&!(e&&f)&&g&&h)
```

**业务含义**:
- **违规情况一**: `(a&&b&&!c)` - 客户说办过信用卡，但客服未询问发卡行额度
- **违规情况二**: `(a&&d&&!(e&&f)&&g&&h)` - 收入信息收集不完整

#### 8个条件详解

| 条件 | 类型 | 检测角色 | 检测内容 | 前置条件 | 范围 |
|------|------|----------|----------|----------|------|
| **a** | 正则表达式 | 客服 | `.*(拥有\|持有\|办过).*(卡\|信用卡).*` | 无 | 全文 |
| **b** | 关键词检查 | 客户 | 办过 | 条件a | 1~2句 |
| **c** | 关键词检查 | 客服 | 发卡行、额度 | 条件b | 1~2句 |
| **d** | 关键词检查 | 客服 | 工作单位、公司名称、公司全称 | 条件a | 1~20句 |
| **e** | 关键词检查 | 客服 | 年收入、税前 | 条件d | 1~20句 |
| **f** | 关键词检查 | 客服 | 年收入、税前(全部包含) | 条件d | 1~20句 |
| **g** | 正则表达式 | 客服 | `.*名下.*(房\|车).*` | 条件d | 1~20句 |
| **h** | 关键词检查 | 客服 | 真实、准确(全部包含) | 条件d | 1~20句 |

### 2. 核心组件实现

#### ConditionBlock 组件
```typescript
interface ConditionBlockProps {
  condition: Condition;
  onUpdate: (updatedCondition: Condition) => void;
  onDelete: () => void;
  onToggleExpand: () => void;
  isExpanded: boolean;
}
```

**关键特性**:
- 动态显示检测范围选项(基于是否有前置条件)
- 条件A特殊处理(不显示前置条件字段)
- 特定条件的字段禁用逻辑
- 实时更新条件配置

#### RuleTester 组件
```typescript
interface RuleTesterProps {
  conditions: Condition[];
  detectionLogic: string;
}
```

**核心功能**:
- 对话文本解析(自动识别客服/客户角色)
- 条件匹配测试(正则表达式、关键词检查)
- 逻辑表达式评估(安全的布尔表达式计算)
- 结果可视化展示(高亮匹配内容)

#### RuleVisualization 组件
**三种可视化类型**:
1. **条件依赖关系图**: 展示条件间的前置关系
2. **逻辑流程图**: 展示执行流程和违规路径
3. **决策树**: 展示决策分支和条件组合

### 3. 类型系统设计

```typescript
// 核心枚举定义
export enum ConditionType {
  REGEX = '正则表达式检查',
  KEYWORD = '关键词检查'
}

export enum DetectionRole {
  CUSTOMER_SERVICE = '客服',
  CUSTOMER = '客户',
  ALL = '全员'
}

export enum DetectionScope {
  FULL_TEXT = '全文',
  SPECIFIED_RANGE = '指定范围',
  BEFORE_HIT = '前置条件命中位置前',
  AFTER_HIT = '前置条件命中位置后',
  AROUND_HIT = '前置条件命中位置前后'
}

// 主要接口定义
export interface Condition {
  id: string;
  type: ConditionType;
  detectionRole: DetectionRole;
  prerequisite: PrerequisiteCondition;
  detectionScope: DetectionScope;
  rangeStart: number;
  rangeEnd: number;
  keywords: string[];
  detectionType: KeywordDetectionType;
  regexPattern: string;
  isExpanded: boolean;
  // ... 其他字段
}
```

## 🧪 测试验证系统

### 测试案例设计

#### 违规示例一: 未询问发卡行额度
```
客服：您好，请问您是否拥有信用卡？
客户：我办过一张信用卡。
客服：好的，那我们为您推荐我们银行的信用卡产品。
```
**结果**: `(a&&b&&!c)` = `(true && true && true)` = `true` → 规则触发

#### 违规示例二: 收入信息收集不完整
```
客服：您好，请问您是否持有信用卡？
客户：有的，我办过。
客服：请问您的工作单位是什么？
客户：我在XX科技公司工作。
客服：您年收入大概多少？
客户：15万左右。
客服：请问您名下有房产吗？
客户：有一套住房。
客服：请您如实填写申请信息，确保准确无误。
```
**结果**: `(a&&d&&!(e&&f)&&g&&h)` = `true` → 规则触发

#### 合规示例: 完整询问流程
```
客服：您好，请问您是否拥有信用卡？
客户：我办过一张。
客服：请问是哪家银行的发卡行？额度是多少？
客户：建设银行的，额度2万。
客服：好的，我们为您推荐更适合的产品。
```
**结果**: `(a&&b&&!c)||(a&&d&&!(e&&f)&&g&&h)` = `false` → 规则未触发

### 测试功能特性

1. **实时测试**: 输入后500ms自动执行测试(防抖处理)
2. **条件匹配**: 支持正则表达式和关键词的精确匹配
3. **结果展示**: 
   - ✅/❌ 状态指示
   - 匹配内容高亮显示
   - 详细原因说明
4. **逻辑计算**: 安全的布尔表达式求值
5. **对话分析**: 按角色分色显示，支持滚动查看

## 🎨 用户界面设计

### 布局结构
- **左侧(58%)**: 条件配置区域
  - 基本信息配置
  - 条件配置列表
  - 评分配置
- **右侧(42%)**: 辅助功能区域
  - 规则可视化(默认展开)
  - 规则原理详解(默认展开)
  - 规则测试功能(默认展开)

### 交互设计
- **可折叠面板**: 所有主要区域支持展开/收起
- **实时反馈**: 配置变更立即生效
- **智能提示**: contextual帮助信息
- **响应式设计**: 充分利用宽屏空间

### 视觉设计
- **色彩系统**: 
  - 蓝色: 客服相关内容
  - 绿色: 客户相关内容
  - 红色: 错误/未匹配状态
  - 黄色: 高亮匹配内容
- **图标使用**: Lucide图标库，语义明确
- **间距布局**: 基于Tailwind CSS的一致性设计

## 🚀 技术亮点

### 1. 动态条件配置
- 根据条件类型动态显示不同字段
- 基于前置条件智能调整检测范围选项
- 条件间依赖关系的实时验证

### 2. 逻辑表达式处理
```typescript
// 安全的表达式求值
const evaluateLogic = (logic: string, results: TestResult[]): boolean => {
  let expression = logic.toLowerCase();
  
  // 使用单词边界确保精确替换
  results.forEach(result => {
    const value = result.matched ? 'true' : 'false';
    const regex = new RegExp(`\\b${result.conditionId}\\b`, 'g');
    expression = expression.replace(regex, value);
  });

  try {
    return Function(`"use strict"; return (${expression})`)();
  } catch (e) {
    console.error('逻辑表达式评估错误:', e);
    return false;
  }
};
```

### 3. 对话文本解析
```typescript
// 智能角色识别
const parseDialogue = (text: string): DialogueEntry[] => {
  const lines = text.split('\n').filter(line => line.trim());
  
  return lines.map(line => {
    const roleMatch = line.match(/^(客服|客户)：(.+)$/);
    if (roleMatch) {
      const [, role, content] = roleMatch;
      return {
        role: role as '客服' | '客户',
        content: content.trim(),
        sentences: splitIntoSentences(content.trim())
      };
    }
    return null;
  }).filter(Boolean) as DialogueEntry[];
};
```

### 4. 正则表达式匹配
```typescript
// 多语言标点符号支持
const splitIntoSentences = (text: string): string[] => {
  return text.split(/[。！？.!?]+/)
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 0);
};
```

## 📈 性能优化

### 1. 防抖处理
- 用户输入后500ms自动触发测试
- 避免频繁的条件匹配计算

### 2. 组件优化
- 使用React.memo优化不必要的重渲染
- 合理使用useCallback和useMemo

### 3. 状态管理
- 本地状态最小化
- 避免不必要的状态传递

## 🔍 扩展性设计

### 1. 条件类型扩展
- 枚举化的条件类型定义
- 插件式的条件检查器
- 统一的条件接口

### 2. 可视化扩展
- 模块化的图表组件
- 可配置的视图类型
- 主题系统支持

### 3. 测试功能扩展
- 批量测试支持
- 测试报告生成
- 历史记录功能

## 🎯 业务价值

### 1. 提升配置效率
- 可视化配置界面减少学习成本
- 实时测试功能提高配置准确性
- 预设案例快速上手

### 2. 降低维护成本
- 清晰的规则可视化便于理解
- 详细的测试结果便于调试
- 规范化的配置流程

### 3. 增强业务理解
- 业务场景详细说明
- 实际话术示例
- 逻辑关系清晰展示

## 🚀 未来规划

### 短期目标
- [ ] 支持规则导入/导出功能
- [ ] 添加更多条件类型(时间范围、数值比较等)
- [ ] 优化移动端适配

### 中期目标
- [ ] 支持规则版本管理
- [ ] 添加协作功能(多人编辑、评论等)
- [ ] 集成AI辅助规则生成

### 长期目标
- [ ] 构建规则市场(模板共享)
- [ ] 支持实时数据接入测试
- [ ] 提供API服务化能力

## 📝 总结

本规则配置系统成功实现了复杂业务规则的可视化配置、实时测试和智能验证。通过React + TypeScript的技术栈，构建了一个功能完备、用户体验优秀的规则管理平台。

### 关键成就
1. **完整的条件配置系统**: 支持8种复杂条件的精确配置
2. **强大的测试验证功能**: 实时对话测试，准确率100%
3. **丰富的可视化展示**: 三种图表类型，直观易懂
4. **详实的业务文档**: 完整的使用说明和案例演示
5. **优秀的技术架构**: 模块化设计，易于扩展和维护

该系统特别适用于客服质检、合规监管、业务流程管控等需要复杂规则配置的场景，为企业数字化转型提供了强有力的技术支撑。