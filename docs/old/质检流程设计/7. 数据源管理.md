---
### **7. 数据源管理**

**页面目标:** 为系统管理员提供一个集中、安全、可控的界面，用于配置和管理所有与外部系统对接的数据源。此页面是保障"新建质检任务"中外部数据来源选项稳定、可靠的基础。

**所属菜单:** `系统设置 / 数据集成 / 数据源管理`

**访问权限:** 仅限系统管理员角色。

---

#### **1. 页面布局与功能**

采用经典的"列表 + 新建/编辑抽屉/弹窗"的布局。

*   **页面标题:** `数据源管理`
*   **主要操作:** `+ 新建数据源` (蓝色主按钮)
*   **列表区域:** 以卡片或表格形式展示已配置的数据源。

---

#### **2. 数据源列表**

列表中每一项都代表一个已配置好的外部数据连接。

**列表列/卡片字段:**

| 标题/字段 | 说明 |
| :--- | :--- |
| **数据源名称** | 管理员为该连接设置的易于理解的别名，如"呼叫中心录音SFTP目录"、"客服系统A通话记录API"。该名称将展示在新建质检任务的下拉选项中。 |
| **数据源类型** | 用标签清晰标识：`监控目录` 或 `API接口`。 |
| **连接状态** | - 显示该数据源的健康状况，通过定期或手动的连接测试刷新。<br>- `正常` (绿色) / `异常` (红色) / `未测试` (灰色)。 |
| **创建信息** | 显示创建人及创建/更新时间。 |
| **操作** | - `编辑`: 修改此数据源的配置。<br>- `测试连接`: 手动触发一次连接健康检查。<br>- `删除`: 删除此数据源配置（需二次确认，且若有任务正在使用则禁止删除）。 |

---

#### **3. 新建/编辑数据源 (以抽屉形式弹出)**

点击"新建数据源"或"编辑"后，从右侧滑出配置表单。

##### **第一步：选择数据源类型**

*   **数据源类型:** `[单选按钮组]`，必填。
    *   `◎ 监控目录`
    *   `◎ API接口`
    *   `◎ 数据库直连`

---

##### **第二步：填写配置信息 (根据类型动态展示)**

***当选择 `监控目录` 时:***

*   **数据源名称:** `[输入框]`，必填。例如："研发部测试用SFTP目录"。
*   **连接协议:** `[下拉选择框]`，必填。支持 `SFTP`, `FTP`, `本地/网络共享路径(SMB)` 等。
*   **服务器地址:** `[输入框]`，必填。如 `sftp.example.com`。
*   **端口:** `[输入框]`，必填。如 `22`。
*   **监控根路径:** `[输入框]`，必填。用户创建任务时，只能选择此路径下的子目录。例如 `/data/call_records/`。
*   **认证方式:** `[单选按钮组]`
    *   `◎ 用户名/密码`
        *   **用户名:** `[输入框]`
        *   **密码:** `[密码输入框]`
    *   `◎ 密钥文件`
        *   `[上传密钥文件]`

***当选择 `API接口` 时:***

*   **数据源名称:** `[输入框]`，必填。例如："生产环境呼叫中心API"。
*   **基础URL (Base URL):** `[输入框]`，必填。例如 `https://api.callcenter.com/v1/`。
*   **录音文件处理策略:** `[单选按钮组]`，必填。
    *   `◎ 同步拉取存储` (默认，推荐)
    *   `◎ 引用外部链接` (不推荐，仅用于高可用的内网环境)
*   **认证方式:** `[下拉选择框]`，必填。
    *   `无认证`
    *   `API Key`
        *   **Key 名称:** `[输入框]` (如 `X-Api-Key`)
        *   **Key 值:** `[密码输入框]`
        *   **添加到:** `[下拉选择框]` (请求头 Header / 查询参数 Query Params)
    *   `Bearer Token`
        *   **Token:** `[密码输入框]`
    *   `OAuth 2.0` (高级)
        *   提供 Client ID, Client Secret, Token URL 等配置项。

***当选择 `数据库直连` 时:***

*   **数据源名称:** `[输入框]`，必填。例如："呼叫中心业务数据库"。
*   **数据库类型:** `[下拉选择框]`，必填。支持 `MySQL`, `PostgreSQL`, `SQL Server`, `Oracle`。
*   **服务器地址/主机名:** `[输入框]`，必填。
*   **端口:** `[输入框]`，必填。
*   **数据库名称/SID:** `[输入框]`，必填。
*   **用户名:** `[输入框]`，必填。
*   **密码:** `[密码输入框]`，必填。
*   **录音文件处理策略:** `[单选按钮组]`，必填。
    *   `◎ 同步拉取存储` (默认，推荐)
    *   `◎ 引用外部链接` (不推荐，仅用于高可用的内网环境)
*   **SQL查询模板:** `[SQL输入编辑器]`，必填。
    *   **说明:** 用于查询需要质检的通话记录。必须包含在字段映射中定义的字段别名。可以使用预设的时间变量（如 `{{last_execution_time}}`）来实现增量同步。
    *   **示例:** `SELECT call_id as recording_id, agent_id, start_time FROM calls WHERE start_time > '{{last_execution_time}}'`

---

##### **第三步：配置字段映射 (核心功能)**

此部分用于将外部数据的字段名，转换为质检系统内部能够理解的标准字段。

*   **标题:** `字段映射规则`
*   **说明:** "请定义如何从元数据文件或API响应中提取必要信息。对于数据库、Webhook、消息队列等集成方式，元数据中必须包含一个可供系统访问的录音文件地址（URL或网络路径）。"
*   **映射表:**
    | 系统标准字段 (必填) | 外部来源字段名/路径 | 示例值 (来自测试) |
    | :--- | :--- | :--- |
    | **录音唯一ID (`recording_id`)** | `[输入框]` (如: `callId`) | `call-123xyz` |
    | **通话开始时间 (`start_time`)** | `[输入框]` (如: `callInfo.startTime`) | `2023-11-15T10:30:00Z` |
    | **坐席工号 (`agent_id`)** | `[输入框]` (如: `agent.id`) | `8001` |
    | **技能组 (`skill_group`)** | `[输入框]` (如: `agent.group`) | `Sales` |
    | **客户号码 (`customer_number`)** | `[输入框]` (如: `customer.phone`) | `138****1234` |
    | **录音文件地址 (`audio_url`)** | `[输入框]` (对于API/数据库模式，此项必填) | `https://cdn.example.com/...` |
    | ... (可增加更多可选字段) | `[+ 添加可选字段映射]` | |

*   **说明:**
    *   **对于监控目录:** 外部来源字段名是元数据文件（如JSON）中的Key或路径（支持点状表示法 `a.b.c`）。
    *   **对于API接口:** 外部来源字段名是API响应体（JSON）中的Key或路径。

---

#### **4. 页面底部操作区**

*   **`测试连接` 按钮:**
    *   点击后，系统会尝试使用当前填写的配置去连接数据源。
    *   **对于监控目录:** 尝试登录并列出根目录下的文件夹。
    *   **对于API接口:** 尝试请求基础URL，验证认证是否通过。
    *   **同时，如果连接成功且配置了字段映射，会尝试获取一条样本数据并填充到映射表的"示例值"一列，实现"映射预览"**。
*   **`保存` 按钮 (主按钮):** 保存配置。在保存前会提示用户先进行连接测试。
*   **`取消` 按钮:** 关闭抽屉，不保存任何修改。

 