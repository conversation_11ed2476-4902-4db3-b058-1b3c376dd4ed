
---

### **3. 实时预警规则配置 - 前端页面设计**

同样，我们将设计两个核心页面：**规则列表页**和**新建/编辑规则页**。

---

#### **1. 实时预警规则配置 - 规则列表页**

**页面目标:** 集中展示和管理所有实时预警规则，让管理者能快速了解当前正在生效的监控规则，并进行启用、停用等操作。

**页面布局:**

采用"操作区 + 列表区"的经典布局。

##### **1.1 页面顶部**

*   **面包屑导航:** `首页 / 智能质检 / 实时预警规则配置`
*   **页面标题:** `实时预警规则配置`
*   **主要操作按钮:**
    *   `+ 新建规则` (醒目的蓝色主按钮)

##### **1.2 规则列表区**

使用表格（Table）组件展示数据，以便清晰地呈现多维度信息。

**表格列 (Columns):**

| 列标题 | 内容与设计说明 |
| :--- | :--- |
| **规则名称** | - 显示用户自定义的规则名称，如"客户投诉风险预警"。<br>- 下方可有灰色小字的简要描述。 |
| **预警等级** | - 使用带有颜色和文字的"标签 (Tag)"组件，视觉化呈现等级。<br>  - `危急` (深红色) <br>  - `高` (红色) <br>  - `中` (橙色) <br>  - `低` (蓝色) |
| **触发条件 (摘要)** | - 简洁地概括核心触发条件。<br>- 示例1: `客户情绪 [突变为 愤怒]`<br>- 示例2: `坐席命中关键词库 [合规禁语]`<br>- 示例3: `坐席静音 > 30秒`<br>- 如果条件过多，则显示前两条并附上"..."，鼠标悬浮时用弹窗（Tooltip）显示全部条件。 |
| **通知方式/对象** | - 简洁地概括通知动作。<br>- 示例: `系统弹窗, 钉钉 -> 被检坐席的班组长` |
| **状态** | - 使用一个"**开关 (Switch)**"组件，直观地显示和控制规则的 `启用` / `停用` 状态。 |
| **创建信息** | - 显示规则的 `创建人` 和 `创建时间`。 |
| **操作** | - `编辑`: 点击后跳转到"编辑规则"页面。<br>- `复制`: 点击后跳转到"新建规则"页面，并自动填充此规则的配置。<br>- `删除`: 点击后弹出二次确认对话框。 |

---

#### **2. 实时预警规则配置 - 新建/编辑规则页**

**页面目标:** 提供一个结构化、引导性强的界面，让用户能够通过组合不同的实时监控条件和通知动作，创建出精准、有效的预警规则。

**设计理念:** 同样采用"**IF...THEN...**"的逻辑结构，将页面分为"**基本信息**"、"**预警触发条件**"和"**预警执行动作**"三大块。

##### **2.1 页面顶部**

*   **面包屑导航:** `首页 / 智能质检 / 实时预警规则配置 / 新建规则`
*   **页面标题:** `新建预警规则`

##### **2.2 表单区域**

**第一部分：基本信息**

*   **规则名称:** `[输入框]`，必填项 `*`。
*   **规则描述:** `[文本域 Textarea]`，选填项。
*   **预警等级:** `[下拉选择框]`，必填项 `*`。选项为：危急、高、中、低。

**第二部分：预警触发条件 (IF)**

这是一个动态的"**规则构建器**"，与复核策略配置的设计类似。

*   **卡片标题:** `当通话中满足以下条件时，立即触发预警`
*   **条件逻辑:**
    *   右上角下拉框 `[满足所有条件(AND) / 满足任一条件(OR)]`。
*   **规则行 (可动态增删):**
    *   每一行代表一个独立的条件，由 `[条件类型]` + `[具体配置]` 构成。
    *   **第一列 (条件类型):** `[下拉选择框]`
        *   **设计说明:** 此下拉框中的选项**并非固定**。它们由系统后台统一的 **"质检算子库"** 动态提供。为了保证实时分析的性能，此处**仅会列出被标记为"兼容实时"的算子**。这与"质检方案配置"页面（会列出所有算子）形成对比，体现了模块复用和场景化配置的核心思想。
        *   `关键词触发`
        *   `客户情绪触发`
        *   `通话状态触发`
        *   `AI模型触发` (高级功能)
    *   **第二列及之后 (具体配置):** 根据第一列的选择动态变化。
        *   **如果选"关键词触发":**
            *   监控对象: `[下拉框: 客户、坐席、双方]`
            *   命中逻辑: `[下拉框: 包含任意、包含全部]`
            *   关键词库: `[选择器]` (可多选，如"投诉升级词库"、"合规禁语词库")
        *   **如果选"客户情绪触发":**
            *   触发条件: `[下拉框: 情绪突变为、连续检测到、情绪得分低于]`
            *   具体值: 根据上一选择出现 `[情绪类型下拉框]` 或 `[时长输入框]` 或 `[得分输入框]`。
        *   **如果选"通话状态触发":**
            *   状态类型: `[下拉框: 静音、语速、抢话]`
            *   具体配置:
                *   (静音) `[下拉框: 坐席、客户、双方]` `时长超过` `[输入框] 秒`
                *   (语速) `[下拉框: 坐席、客户]` `持续` `[输入框] 秒` `高于/低于` `[输入框] 字/分钟`
                *   (抢话) `[下拉框: 坐席打断客户、客户打断坐席]` `次数超过` `[输入框] 次`
    *   **行操作:** 每行规则末尾有 `[删除]` (垃圾桶图标) 按钮。

**第三部分：预警执行动作 (THEN)**

*   **卡片标题:** `立即执行以下动作`
*   **通知方式:** `[复选框组 Checkbox Group]`，可多选。
    *   `□ 系统内弹窗`
    *   `□ 应用消息` (如：钉钉、企业微信)
    *   `□ 短信`
    *   `□ 邮件`
*   **通知对象:** `[单选按钮组 Radio Group]`
    *   `◎ 指定人员/角色`
        *   选中后，下方出现 `[人员/角色选择器]`，支持搜索和选择。
    *   `◎ 动态关联人员`
        *   选中后，下方出现 `[下拉选择框]`，选项为：
            *   `被预警坐席的直属班组长`
            *   `被预警坐席所属的质检员`
*   **附加操作:** `[复选框组 Checkbox Group]`，可多选。
    *   `□ 自动标记通话`
        *   选中后，出现 `[标签输入/选择框]`，让用户指定要打上的标签，如"投诉风险"。
    *   `□ 自动创建质检任务` (**核心联动**)
        *   **设计理念:** 这是打通"事中预警"与"事后复盘"的关键。选中后，下方会联动出现一个必填项，用于解决"生成的质检任务应该用哪套标准来评分"的核心问题。
        *   **质检方案:** `[下拉选择框]` 必填 `*`
            *   **UI交互:** 当"自动创建质检任务"复选框被勾选时，此下拉框出现并成为必填项。
            *   **选项构成:**
                *   **具体方案列表:** 枚举系统中所有已启用的质检方案，例如：`"服务态度通用考察方案"`、`"重大客诉专项复核方案"`。让用户可以为特定的预警匹配最合适的评分标准。
                *   **动态默认选项:** 提供一个特殊选项 `"[使用坐席团队的默认方案]"`。如果选中此项，系统将自动应用被预警坐席所属团队预设的默认质检方案。此设计能极大简化大量通用规则的配置工作。

##### **2.3 页面底部操作区**

*   `保存规则` (蓝色主按钮)
*   `取消` (灰色文字链接)

---

### **设计亮点总结**

1.  **逻辑清晰，易于上手:** 延续了"IF-THEN"的配置模式，将复杂的实时监控规则配置过程分解为简单、直观的步骤，用户即使没有技术背景也能轻松创建。
2.  **触发条件全面:** 覆盖了从内容（关键词）到情感（情绪）再到行为（静音、语速）的多个监控维度，提供了构建强大预警系统的基础。
3.  **通知精准触达:** "动态关联人员"的设计是亮点，它让通知不再是静态写死的，而是能根据事件本身（具体哪个坐席触发的）智能地找到对应的负责人，极大提升了响应效率。
4.  **事中事后联动:** "附加操作"将实时预警和后续的质检分析流程打通，形成了一个从"即时发现"到"事后归档分析"的完整风险管理闭环。
5.  **视觉化管理:** 列表页的等级标签和状态开关，让管理者对所有规则的权重和状态一目了然，便于日常维护和调整。