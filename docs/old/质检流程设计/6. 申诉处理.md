
---

### **6. 申诉处理 - 前端页面设计**

我们将设计两个主要环节的页面：**坐席端 - 发起申诉**，和 **处理端 - 申诉处理工作台**。

---

#### **1. 坐席端：发起申诉**

这个功能通常不是一个独立的页面，而是嵌入在【我的成绩】页面中的一个**操作弹窗 (Modal)**，以保证操作的连贯性。

**触发入口:** 在【我的成绩】页面的成绩明细列表中，点击状态为`可申诉`记录的 `[发起申诉]` 按钮。

##### **发起申诉弹窗 (Modal) 设计**

*   **弹窗标题:** `发起申诉`
*   **申诉信息区 (只读):**
    *   **通话时间:** `2023-11-20 14:30:15`
    *   **录音ID:** `3B45F1A-XYZ`
    *   **当前得分:** `70分`
    *   **质检方案:** `标准服务流程质检方案`
    *   *目的: 让坐席再次确认自己正在为哪一条记录申诉。*
*   **申诉理由输入区 (核心):**
    *   **标题/标签:** `申诉理由` (带红色 `*` 必填标识)
    *   **输入框:** `[多行文本域 Textarea]`，并有清晰的提示文字，如：“请详细说明您申诉的理由，可以针对具体的扣分项或整体结果。清晰的理由将有助于申诉处理。”
    *   **字数统计:** 在输入框右下角显示实时字数，如 `120 / 500`。
*   **附件上传区 (可选但推荐):**
    *   **标题/标签:** `补充材料 (可选)`
    *   **控件:** `[文件上传按钮]`
    *   **说明:** 允许坐席上传截图、内部知识库文档等作为支撑证据。
*   **弹窗底部操作按钮:**
    *   `提交申诉` (蓝色主按钮): 点击后，进行表单校验（申诉理由不能为空），然后提交。
    *   `取消` (灰色描边按钮)

**交互流程:**
1.  点击`提交申诉`。
2.  系统后台将该记录的申诉状态更新为`申诉中`。
3.  弹窗关闭，原列表中的该条记录状态标签立刻从 `可申诉` 变为 `申诉中 (橙色)`。
4.  系统给出成功提示：“申诉提交成功，请耐心等待处理结果。”

---

#### **2. 处理端：申诉处理工作台**

这是**质检主管**或**指定申诉处理人**的核心工作界面。它应该与【我的复核任务】在设计上保持高度一致性，以降低学习成本，但需突出“申诉”这一主题的特殊信息。

我们将设计两个页面：**申诉任务列表页**和**申诉处理详情页**。

##### **2.1 申诉任务列表页**

**页面目标:** 为处理人提供一个清晰的待办申诉列表，了解申诉积压情况，并选择任务进行处理。

*   **面包屑导航:** `首页 / 质检管理 / 申诉处理`
*   **页面标题:** `申诉处理任务`
*   **统计卡片区:** `待处理申诉: 8`、`今日已处理: 5`。
*   **页签切换 (Tabs):**
    *   `待处理 (8)`
    *   `已处理`
*   **筛选区 (在“已处理”列表下更常用):** `[申诉人搜索框]`、`[申请时间范围选择器]`、`[处理结果下拉框: 全部/申诉成功/申诉失败]`。
*   **任务列表表格 (Table):**

| 列标题 | 内容与设计说明 |
| :--- | :--- |
| **申诉人** | 显示坐席的姓名/工号及其团队。 |
| **原质检信息** | 简洁显示 `原得分` (如70分) 和 `原复核员` (如有)。 |
| **申诉时间** | 坐席提交申诉的日期时间。 |
| **申诉理由 (摘要)** | **核心信息。** 显示申诉理由的前50个字，并附有“...[查看全文]”链接。 |
| **操作** | - `处理申诉`: 蓝色主按钮，点击跳转到**申诉处理详情页**。<br>- *在“已处理”列表，按钮变为 `查看详情`。* |

##### **2.2 申诉处理详情页 (工作台)**

**页面目标:** 提供一个集成化的工作界面，让处理人能够**对比查看所有相关信息**（录音、AI分析、原评分、坐席申诉理由），并做出公正的裁决、填写判决依据。

**设计理念:** 采用与【复核工作台】类似但经过增强的**三栏式布局**。

*   **页面顶部:**
    *   **信息条:** `申诉人: 王小明 (客服A组)` | `原得分: 70分` | `申诉时间: 2023-11-21 09:30`
*   **左栏：音频与原始证据**
    *   `[音频播放器]`
    *   `[通话画像]`
*   **中栏：通话内容与AI分析**
    *   `[带AI标注的通话转写文本]`
*   **右栏：对比、裁决与反馈区 (核心)**

右栏的设计是整个页面的精髓，需要将**原始评判**和**申诉信息**进行并列或上下对比展示。

```
+----------------------------------------+
| ▼ 原始质检结果                       |
|----------------------------------------|
| AI初检得分:    75 分                  |
| 人工复核得分:  70 分 (-5)              |
| 最终得分(原):  70 分                  |
| ---                                    |
| 复核员评语: 【李主管】在业务解释环节... |
+----------------------------------------+
| ▼ 坐席申诉信息 (申诉焦点)            |
|----------------------------------------|
| 申诉理由: “我认为关于运费险的解释是   |
| 清晰的，录音3分15秒处有提到，申请重听。”|
| 补充材料: [附件1.jpg] (可点击预览)    |
+----------------------------------------+
| ▼ 申诉处理与裁决 (您的操作区)        |
|----------------------------------------|
| **处理结果:** (必选 *)                 |
|   ◎ 维持原判 (申诉失败)               |
|   ◎ 修改结果 (申诉成功)               |
|                                        |
| **分数修正:** (选中“修改结果”后激活)     |
|   `[可编辑的评分表]` (预填了原结果)    |
|   `最终得分(新):` [动态计算结果]       |
|                                        |
| **处理意见:** (必填 *)                 |
|   `[多行文本域 Textarea]`              |
|   提示: “请详细说明您做出此裁决的依   |
|   据，此意见将对申诉人可见。”           |
+----------------------------------------+
```

*   **页面底部悬浮操作栏:**
    *   `提交处理结果` (蓝色主按钮): 点击后校验表单（处理结果和意见为必填），提交后，系统将通知坐席。
    *   `暂存`
    *   `返回列表`

---

### **设计亮点总结**

1.  **流程闭环:** 从坐席端的便捷发起，到处理端的任务列表，再到信息集成的处理工作台，最后到结果的提交与通知，形成了一个完整的闭环。
2.  **信息对称与聚焦:** 处理工作台通过将“原始结果”和“申诉理由”并列展示，帮助处理人快速聚焦争议点，避免信息遗漏，为公正裁决提供了信息基础。
3.  **强制性反馈机制:** 将“处理意见”设为必填项，强制要求处理人对其裁决进行解释。这不仅是对申诉人的尊重，也是建立质检公信力的重要一环。
4.  **裁决与操作分离:** 先做“裁决选择”（维持或修改），再进行具体操作（修改分数）。这种设计使用户的思考路径更清晰，降低了误操作的风险。
5.  **一致的用户体验:** 处理端工作台与复核工作台在布局和交互上保持一致性，降低了用户的学习成本，提升了系统的整体易用性。