
---

### **智能客服质检系统设计方案**

#### **一、 系统核心理念**

本系统旨在通过“AI初检 + 人工复核 + 数据驱动”的模式，实现对呼叫中心服务的全方位、高效率、高精度的质量监控。系统以提升服务质量、发现业务问题、赋能坐席成长为最终目标，打造一个从“质检 - 复核 - 反馈 - 申诉 - 改进”的完整管理闭环。

#### **二、 核心用户角色**

1.  **系统管理员 (Admin):** 配置系统全局规则，管理用户权限。
2.  **质检主管/质检员 (QA Manager/QA Specialist):** 创建质检任务，配置复核策略，处理申诉，分析整体质检数据。
3.  **复核员 (Reviewer):** 通常由资深质检员或班组长担任，负责对AI质检结果进行二次人工审核。
4.  **班组长 (Team Leader):** 查看本组成绩，辅助进行复核与申诉确认。
5.  **坐席 (Agent):** 查看个人质检成绩并发起申诉。

---

### **三、 模块详细设计**

#### **1. 质检任务管理 (Inspection Task Management)**

此模块是质检工作的起点，负责定义要“检什么”、“怎么检”。

**主要功能:**

*   **新建质检任务:**
    *   **任务名称:** 自定义，如“11月营销活动通话质检”。
    *   **质检范围:**
        *   **时间范围:** 选择需要质检的通话录音发生的时间段。
        *   **坐席范围:** 可选择全部坐席、特定技能组、特定班组或单个/多个坐席。
    *   **质检模式:**
        *   **全量质检:** 对范围内所有录音进行100% AI质检。适用于关键业务、合规检查等场景。
        *   **抽样质检:** 按规则进行抽检。
            *   **随机抽样:** 如按 `10%` 的比例随机抽取。
            *   **条件抽样:** 如 `通话时长 > 5分钟`、`客户情绪为负面`、`静音时长占比 > 20%`、`包含特定关键词（如“投诉”、“退款”）` 等。
    *   **质检方案:** 选择本次任务使用的评分方案。方案库中预设了不同业务场景的评分表（如服务开场、业务能力、服务态度、合规用语、结束语等）。
    *   **执行方式:**
        *   **立即执行:** 创建后马上开始。
        *   **定时执行:** 在指定时间点自动开始。
        *   **周期性任务:** 按天/周/月自动重复创建并执行任务。

*   **任务列表与监控:**
    *   以列表形式展示所有质检任务。
    *   **关键信息:** 任务名称、状态（待执行、执行中、已完成、失败）、质检模式、任务进度（如 `8500/10000`）、创建人、创建时间。
    *   **操作:** 查看详情、暂停、终止、删除、复制任务、查看结果报告。

**设计亮点:** 灵活的任务创建方式，满足不同业务场景下的质检需求，通过周期性任务实现自动化、常态化的质检监控。

---

#### **2. 复核策略配置 (Review Strategy Configuration)**

此模块是连接“AI自动质检”和“人工精准复核”的桥梁，确保将有限的人工复核资源投入到最需要的地方。

**主要功能:**

*   **新建复核策略:**
    *   **策略名称:** 自定义，如“低分录音复核策略”、“重大违规复核策略”。
    *   **触发条件 (IF):** 设置自动将录音推送至人工复核的规则（可组合）。
        *   **分数触发:** `总分低于 60分` 或 `总分高于 95分` (用于发现优秀实践)。
        *   **单项触发:** 某个关键项得分为 `0分`（如“未进行身份核实”）。
        *   **规则/标签触发:** AI质检命中特定标签，如 `严重违规`、`客户投诉风险`、`承诺超纲`。
        *   **随机抽查:** 为验证AI准确率，可设置 `随机抽取 5% 的已质检录音` 进入复核池。
    *   **分配规则 (THEN):** 设置触发后任务的分配方式。
        *   **分配到复核池:** 推送至公共复核任务池，由复核员主动领取。
        *   **按规则分配:**
            *   **轮询分配:** 平均分配给所有可用复核员。
            *   **指定分配:** 分配给特定的复核员或复核组。
            *   **关联分配:** 分配给被质检坐席所属的班组长进行复核。

*   **策略管理:**
    *   列表展示所有策略，可对其进行`启用`、`停用`、`编辑`、`删除`。可以同时启用多条策略。

**设计亮点:** 实现了从“盲目抽检”到“精准复核”的转变，极大提升了质检的深度和有效性，同时能有效监控AI质检模型的准确性。

---

### **3. 实时预警规则配置 (Real-time Alert Rule Configuration)**

此模块是呼叫中心风险防控的“神经中枢”，通过对进行中的通话进行实时监控，捕捉高风险信号或关键服务时刻，并立即触发预警，帮助管理人员从“事后追溯”变为“事中干预”。

#### **主要功能:**

##### **一、 新建预警规则**

此页面用于定义“什么情况下”触发“什么样的警报”并通知“谁”。

*   **规则名称:** 自定义，必须清晰易懂，如“客户投诉风险预警”、“自杀倾向言论预警”、“坐席违规承诺预警”。
*   **规则描述:** (选填) 对规则的详细说明，便于其他管理员理解。
*   **预警等级:** `[下拉选择框]`，用于对警报进行分级，方便接收人判断紧急程度。
    *   `危急` (如客户表达自杀倾向)
    *   `高` (如客户明确表示要投诉、升级)
    *   `中` (如客户情绪急剧恶化)
    *   `低` (如坐席长时间静音)

*   **触发条件 (IF):** 设置触发实时预警的规则（可组合，使用“与/或”逻辑）。
    *   **关键词触发:**
        *   **监控对象:** `[下拉框]` (客户、坐席、双方)
        *   **关键词库:** `[选择框]` 从预设的关键词库中选择，如“投诉升级词库”、“合规禁语词库”、“自杀风险词库”。（关键词库由管理员在另一功能中维护）
        *   **触发逻辑:** `[下拉框]` (包含任意关键词、包含全部关键词)
    *   **客户情绪触发:**
        *   **情绪类型:** `[下拉框]` (愤怒、悲伤、恐惧等)
        *   **触发条件:** `[下拉框]`
            *   `情绪突变为...` (例如，从“平静”突然变为“愤怒”)
            *   `连续X秒检测到...`
            *   `情绪得分低于...` (例如，情绪得分低于-0.8，代表极度负面)
    *   **通话状态触发:**
        *   **静音:** `[下拉框]` (坐席静音、客户静音、双方静音) `时长超过` `[输入框]` `秒`。
        *   **语速:** `[下拉框]` (坐席语速、客户语速) `持续` `[输入框]` `秒` `高于/低于` `[输入框]` `字/分钟`。
        *   **抢话/打断:** `[下拉框]` (坐席打断客户、客户打断坐席) `次数超过` `[输入框]` `次`。
    *   **自定义模型触发:** (高级功能)
        *   `命中AI模型:` `[下拉框]` 选择一个预先训练好的特定场景模型，如“欺诈销售意图模型”、“客户流失风险模型”。

*   **预警动作 (THEN):** 设置触发后系统执行的操作。
    *   **通知方式 (可多选):**
        *   `[复选框]` **系统内弹窗:** 在指定人员的监控大屏或客户端弹出强提醒窗口。
        *   `[复选框]` **发送应用消息:** 通过钉钉、企业微信等即时通讯工具发送卡片消息。
        *   `[复选框]` **发送短信:** 向指定手机号发送短信通知（用于最高级别预警）。
        *   `[复选框]` **发送邮件:** 发送邮件详情。
    *   **通知对象:**
        *   `◎` **指定人员/角色:** `[人员/角色选择器]` 可选择具体的质检主管、班组长，或“质检管理岗”等角色。
        *   `◎` **动态关联人员:** `[下拉框]` 这种方式更智能，无需硬编码。
            *   `被预警坐席的直属班组长`
            *   `被预警坐席所属的质检员`
    *   **附加操作 (可多选):**
        *   `[复选框]` **自动标记通话:** 为该通记录自动打上预警标签，如`严重投诉风险`，便于事后筛选分析。
        *   `[复选框]` **自动推送至复核池:** 将该通话记录在结束后，自动加入到指定的复核任务池中，强制进行人工复核。

##### **二、 规则管理**

*   以列表形式展示所有已创建的预警规则。
*   **列表关键信息:** 规则名称、预警等级（用不同颜色的标签显示）、触发条件摘要、通知对象、状态（启用/停用）、创建人/时间。
*   **操作:**
    *   `编辑`: 修改规则配置。
    *   `启用/停用`: 使用一个“开关 (Switch)”组件，可以快速激活或暂停某条规则，而无需删除。
    *   `删除`: 永久删除规则（需二次确认）。
    *   `复制`: 快速创建一条相似的规则。

#### **设计亮点:**

1.  **从被动到主动:** 将质检能力前置，实现了从“事后发现问题”到“事中干预、主动管理”的根本性转变，极大提升了风险控制和现场支持能力。
2.  **灵活的触发器:** 结合关键词、情绪、通话状态等多维度AI能力，可以配置出覆盖合规、服务、风险等多种场景的复杂预警规则。
3.  **精准的通知体系:** 支持多种通知渠道，并能通过“动态关联人员”的模式，将警报精准、智能地推送给最相关的责任人，实现高效闭环。
4.  **打通事中与事后:** 通过“自动标记”和“自动推送复核”等附加操作，将实时预警与事后质检复核流程无缝衔接，确保高风险事件得到100%的关注和分析。

---

#### **4. 我的复核任务 (My Review Tasks)**

这是复核员的核心工作台，界面设计需高度聚焦，提升复核效率。

**主要功能:**

*   **任务列表 (To-do List):**
    *   展示所有待处理、处理中的复核任务。
    *   **关键信息:** 录音ID、被检坐席、AI初检分数、触发复核原因（如“低分触发”）、任务分配时间、截止时间。
    *   **筛选与排序:** 可按紧急程度、坐席、任务来源等进行筛选排序。

*   **复核详情界面 (三栏式布局):**
    *   **左栏 - 通话信息与播放器:**
        *   通话基础信息（主叫/被叫、时长、时间等）。
        *   录音波形播放器，支持倍速播放、暂停、拖拽，并区分客户与坐席音轨。
    *   **中栏 - 智能转写与AI分析结果:**
        *   **文本与音频联动:** 点击文本，音频跳转至对应位置。
        *   **AI结果高亮:** AI识别出的关键词、情绪点、静默点、抢话点、违规点等在文本中用不同颜色或图标高亮展示。
    *   **右栏 - 人工评分与复核意见:**
        *   **评分表:** 加载与初检时一致的评分模板，并自动填入AI的评分结果。
        *   **人工修改:** 复核员可以直接在评分表上修改分数。修改后的分数旁应有标记，并要求填写修改理由。
        *   **复核评语:** 独立的文本框，供复核员填写对整个通话的综合评价、给坐席的改进建议或发现的亮点。
        *   **标签补充:** 复核员可以手动为该通记录打上新的标签，如 `产品建议`、`流程优化点` 等。

*   **操作按钮:** `提交复核`、`暂存`、`退回`（如录音质量问题）。

**设计亮点:** 将音频、文本、AI分析、人工评分高度整合在同一界面，复核员无需切换页面即可完成所有操作，信息一目了然，效率倍增。

---

#### **5. 质检成绩查看 (Score & Performance Viewing)**

此模块是质检结果的呈现与数据分析中心，面向不同角色提供不同维度的视图。

**主要功能:**

*   **面向坐席 - 我的成绩:**
    *   **个人看板:** 展示个人近期成绩趋势（折线图）、平均分、合格率等。
    *   **成绩列表:** 列出所有已出分的质检记录。
    *   **详情钻取:** 点击任一记录，可查看详细评分表、各项目得分、复核员评语，并可回听录音、查看转写文本。
    *   **申诉入口:** 对有异议的成绩，可直接点击 `发起申诉` 按钮。

*   **面向班组长 - 团队看板:**
    *   **团队概览:** 团队平均分、合格率、与公司平均水平对比。
    *   **成员排名:** 团队内成员的成绩排名。
    *   **问题分析:** 团队最常见的失分项、违规项统计。
    *   **详情钻取:** 可查看团队内任一成员的详细质检报告。

*   **面向质检主管 - 全局驾驶舱:**
    *   **宏观数据:** 展示整个呼叫中心的质检概况，如总质检量、平均分、高频问题TOP10等。
    *   **多维分析:** 支持按时间、技能组、业务线等维度进行交叉对比分析。
    *   **报表导出:** 支持将各类分析图表和原始数据导出为Excel或PDF。

**设计亮点:** 角色化权限与视图，让每个人都能看到自己最关心的信息，从一线坐席到高层管理，都能通过数据获得洞察。

---

#### **6. 申诉处理 (Appeal Handling)**

此模块是保障质检公平性的重要环节，提供规范、透明的申诉流程。

**主要功能:**

*   **发起申诉 (坐席端):**
    *   在成绩详情页点击“发起申诉”。
    *   弹出申诉窗口，需填写 **申诉理由** (必填)，可针对具体评分项或整体结果进行申诉。

*   **申诉任务列表 (处理端):**
    *   质检员或主管的界面中出现“申诉处理”模块。
    *   以列表形式展示待处理的申诉任务。
    *   **关键信息:** 申诉人、被申诉录音、原分数、申诉时间、申诉理由。

*   **申诉处理界面:**
    *   界面布局与复核界面类似，但额外增加 **“申诉信息”** 区域，清晰展示坐席的申诉理由。
    *   处理人（通常是质检主管或更高级别的复核员）再次对录音和评分进行审核。
    *   **处理结果:**
        *   **维持原判:** 驳回申诉。
        *   **申诉成功:** 修改分数。
    *   **最终意见:** 无论结果如何，处理人必须填写详细的 **处理意见**，向坐席解释维持原判或修改分数的原因。

*   **结果通知:**
    *   处理完成后，系统自动通过站内信或消息推送告知坐席申诉结果和处理意见。
    *   坐席可在自己的成绩页面看到申诉状态的变化（如：已申诉 -> 申诉处理中 -> 申诉已关闭）及最终结果。

**设计亮点:** 流程闭环，有理有据。既给予坐席表达意见的权利，也要求处理过程透明、有反馈，增强了员工对质检体系的信任感。

---

### **四、 总结**

这套包含五大核心模块的设计方案，构成了一个完整、高效、智能的呼叫中心质检生态系统。它不仅能完成传统的质检评分工作，更能通过策略化的复核、多维度的分析和闭环的申诉流程，真正实现以数据驱动服务质量的持续提升。