
---

### **5. 质检成绩查看 - 前端页面设计**

**核心理念:** 以用户为中心，通过分层、可视化的数据呈现，赋能各级用户（坐席、班组长、主管）从自己的视角洞察服务质量。设计强调透明度与过程追溯，确保每一次质检、每一次申诉都有据可查，建立起员工与质检体系之间的信任。

---

#### **1. 面向坐席：我的成绩 (My Performance)**

**页面目标:** 成为坐席的个人服务质量仪表盘和成长伙伴。提供清晰、正向的业绩反馈，直观揭示能力长短板，并保障其申诉权利与结果的透明度。

**页面布局:** 个人看板 + 成绩明细列表。

##### **1.1 页面顶部**

*   **面包屑导航:** `首页 / 我的工作台 / 我的成绩`
*   **页面标题:** `我的成绩`
*   **消息提醒:** 右上角 `[铃铛图标]`，用于主动推送新的成绩单和申诉结果。

##### **1.2 个人看板区 (Dashboard)**

*   **数据卡片行:**
    *   **本月平均分:** `88.5` 分 (附带与上月对比的 `↑ 1.2 分`)。
    *   **本月合格率:** `95%`。
    *   **申诉处理中:** `1` (点击可直接筛选下方列表)。
*   **图表一: 成绩趋势图 (折线图)**
    *   **标题:** `近期成绩趋势`
    *   **内容:** `我的平均分 (实线)` 与 `团队平均分 (虚线)` 对比，让坐席了解个人表现与团队基准的关系。
*   **图表二: 个人能力雷达图 (Radar Chart)**
    *   **标题:** `我的能力模型`
    *   **维度:** 质检方案中的核心能力项（服务规范、业务处理等）。
    *   **目的:** 帮助坐席快速识别个人优势与待提升领域。

##### **1.3 成绩明细列表区**

*   **标题:** `我的质检记录`
*   **筛选器:** `[日期范围选择器]`、`[申诉状态下拉框]` (包含：可申诉、申诉中、申诉成功、申诉失败)。
*   **表格 (Table):**

| 列标题 | 内容与设计说明 |
| :--- | :--- |
| **通话时间** | 通话发生的具体日期时间。 |
| **最终得分** | **核心列，增加分数演变交互。**<br>- 显示最终得分，如 **`85`**。<br>- 若分数发生过变化，旁边显示图标 **`📈`** 或 **`📉`**。<br>- **鼠标悬浮**在分数上时，**Tooltip** 显示演变路径：`AI初检: 75 -> 申诉成功: 85 (+10)`。 |
| **申诉状态** | **采用标准化的标签设计:**<br>- `可申诉` (蓝色)<br>- `申诉中` (橙色)<br>- `申诉成功` (绿色)<br>- `申诉失败` (灰色)<br>- (无申诉则为空) |
| **操作** | - `查看详情`: 始终显示，点击跳转到【单条录音质检报告页】。<br>- `发起申诉`: **仅在状态为`可申诉`时显示**。 |

---

#### **2. 面向班组长：团队看板 (Team Dashboard)**

**页面目标:** 成为班组长的管理驾驶舱。提供团队服务质量的实时监控，快速识别成员表现，并精准定位团队共性问题，为管理和辅导提供数据支持。

**页面布局:** 仪表盘式布局，强调对比、排名和问题定位。

##### **2.1 页面顶部**

*   **面包屑导航:** `首页 / 团队管理 / 团队质检看板`
*   **页面标题:** `团队质检看板`
*   **核心筛选器:** `[日期范围选择器]`、`[质检方案选择器]`。

##### **2.2 团队概览与问题分析区**

*   **数据卡片行:**
    *   **团队平均分:** `85.2` 分 (附带与公司平均分的对比)。
    *   **团队合格率:** `91%`。
*   **图表一: 团队高频失分项 (水平条形图)**
    *   **标题:** `团队 TOP 5 问题点`，直接暴露培训重点。
*   **图表二: 成员表现分布图 (箱形图或散点图)**
    *   **标题:** `团队成员表现分布`，快速识别异常值（明星员工和待关注员工）。

##### **2.3 团队成员表现列表**

*   **标题:** `团队成员表现详情`
*   **表格 (Table):**

| 列标题 | 内容与设计说明 |
| :--- | :--- |
| **排名** | 按平均分动态排序。 |
| **坐席姓名/工号** | |
| **平均分** | **增加分数演变交互。**<br>- 默认显示坐席的平均分。<br>- **鼠标悬浮**时，Tooltip可显示其分数构成摘要，如：“质检总数: 20，其中3条有分数变动”。 |
| **合格率** | |
| **申诉情况** | 简洁汇总，如 `申诉 3 / 成功 1`。 |
| **操作** | - `查看个人报告`: 点击后，以班组长视角打开该坐席的【我的成绩】页面，所有分数演变历史均可查看。 |

---

#### **3. 面向质检主管/管理员：全局驾驶舱 (QA Dashboard)**

**页面目标:** 成为管理者的战略决策中心。提供宏观、多维度的质检数据洞察，用于评估整体服务质量、监控质检体系健康度（如AI准确性、申诉率），并支持跨部门、跨业务线的横向对比。

**页面布局:** 高度可定制化的商业智能（BI）仪表盘。

##### **3.1 页面顶部**

*   **面包屑导航:** `首页 / 数据中心 / 质检全局驾驶舱`
*   **页面标题:** `质检全局驾驶舱`
*   **全局筛选器:** `[日期范围]`、`[业务线]`、`[技能组]`等。

##### **3.2 宏观指标与体系健康度监控**

*   **KPI卡片行:**
    *   `总质检量`、`整体平均分`、`整体合格率`
    *   **`申诉率`**
    *   **`申诉成功率`**
    *   **`AI-人工复核一致性`** (衡量AI模型健康度的核心指标)
*   **图表一: 申诉数据分析 (饼图 + 时间序列图)**
    *   **标题:** `申诉情况分析`
    *   **饼图:** `申诉成功` vs `申诉失败` 的占比，直观反映申诉处理的尺度。
    *   **时间序列图:** 申诉率随时间的变化趋势。
*   **图表二: 分数变更分析 (条形图)**
    *   **标题:** `分数变更来源分析`
    *   **内容:** 分析所有分数发生变化的记录，按原因归类：
        *   `人工复核修正` (多少条)
        *   `申诉成功修正` (多少条)
    *   这个图表能帮助管理者了解AI质检与人工判断的主要差异点，以及申诉流程的有效性。

##### **3.3 数据明细与导出**

*   **可切换的汇总表格:** 提供不同维度的聚合数据。
*   **导出功能:** 将当前视图及数据导出为 `PDF` 或 `Excel`。

---

### **最终版设计亮点总结**

1.  **全过程透明:** 核心亮点在于**完整呈现了分数演变历史**（AI初检 -> 复核 -> 申诉），通过列表页的轻量交互和详情页的结构化展示，确保了质检过程的100%透明，从根本上建立信任。
2.  **角色化信息架构:** 为三类核心用户提供了量身定制的视图，信息层层递进，从个人成长到团队管理再到战略决策，各取所需，精准高效。
3.  **标准化的状态反馈:** 统一并优化了申诉状态的视觉呈现（可申诉、申诉中、成功、失败），使用户能即时、准确地获取反馈。
4.  **数据驱动行动:** 所有图表和数据均服务于一个明确目的——发现问题、定位原因、驱动改进。无论是坐席的自我提升，还是班组长的团队辅导，都有明确的数据抓手。
5.  **体系健康度监控:** 在全局驾驶舱中，加入了对“申诉成功率”、“AI-人工复核一致性”等元数据的监控，使管理者不仅能看到业务质量，还能评估质检系统本身的健康状况。