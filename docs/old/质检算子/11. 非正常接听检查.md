“非正常接听检查”的核心是测量**从电话接通到有人说第一句话之间的静音时长**，并且可以指定期望是谁先说话。

以下是几个严格遵守**原子性**的示例，每个示例只解决一个最小化的问题。

---

### 原子性示例 1：检测客服响应延迟

*   **原子性目标**：只检测一种情况——客服是否在电话接通后的规定时间内主动开口说话。
*   **原子性配置**：
    *   **检查逻辑**：接通电话到说第一句话的时间 **大于 3 秒**
    *   **第一句话的角色为**：**客服**
*   **场景举例**：
    *   `00:00` - 电话接通。
    *   `00:01` - (静音)
    *   `00:02` - (静音)
    *   `00:03` - (静音)
    *   `00:04` - **客户**：“喂？有人吗？”
*   **分析**：
    *   规则命中了。因为直到第4秒，客服都没有说话，第一句话是由客户说出的。此规则的设定是期望“第一句话”的角色必须是“客服”，并且必须在3秒内发生。由于客户先开口了，对于这条规则而言，就意味着客服在3秒内并未说出“第一句话”，因此判定为异常。这个规则可以非常精准地找出那些没有及时响应、让客户先开口的客服。

---

### 原子性示例 2：检测系统或网络的整体延迟

*   **原子性目标**：不关心谁先说话，只检测通话建立后是否存在异常的“死寂”，这通常指向技术问题。
*   **原子性配置**：
    *   **检查逻辑**：接通电话到说第一句话的时间 **大于 4 秒**
    *   **第一句话的角色为**：**任意** (或者不设置角色限制)
*   **场景举例**：
    *   `00:00` - 电话接通。
    *   `00:01` - (静音)
    *   `00:02` - (静音)
    *   `00:03` - (静音)
    *   `00:04` - (静音)
    *   `00:05` - **客服**：“您好，很高兴为您服务。”
*   **分析**：
    *   规则命中了。因为它不关心最终是客服还是客户先说话，它只关心从接通到第一个声音出现，是否超过了4秒。这种情况的出现，可能不是客服的责任，而大概率是电话系统（PBX）转接延迟、网络丢包或录音系统启动慢等技术原因导致的，筛选出这些通话有助于IT部门排查问题。

---

### 原子性示例 3：识别“秒回”的优秀客服

*   **原子性目标**：反向使用，识别并表扬那些响应极其迅速的客服。
*   **原子性配置**：
    *   **检查逻辑**：接通电话到说第一句话的时间 **小于 1 秒**
    *   **第一句话的角色为**：**客服**
*   **场景举例**：
    *   `00:00.0` - 电话接通。
    *   `00:00.8` - **客服**：“您好，这里是xx服务中心。”
*   **分析**：
    *   规则命中了。这条规则用于正面激励，它可以从所有通话中筛选出那些“无缝接入”、响应最快的客服通话案例。通过分析这些案例，可以总结出优秀客服的工作习惯，并将其作为培训的标杆。同时，也可以作为绩效考核中“服务积极性”的加分项。