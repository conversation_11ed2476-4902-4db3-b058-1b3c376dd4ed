**上下文重复检查**的核心目标是识别在**一定范围（如几句话或几十秒）内**，同一方（或双方）反复说出**相同或相似**的话。这种情况通常预示着沟通陷入了僵局。

这个算子比“关键词检查”和“文本相似度检查”更进一步，它不仅关心说了什么，还关心**“说的频率和时机”**。

### 主要应用场景及示例

---

#### 场景一：识别客户无效循环，发现服务堵点

当客户的问题没有得到有效解答时，他们会倾向于用不同方式重复提问。这是最经典的应用场景。

*   **目标**：检测客户是否因为问题未解决而反复提问。
*   **配置思路**：
    *   **检测角色**：客户
    *   **检查逻辑**：在90秒内，如果客户说了3次或以上**语义相似**的话，则规则命中。
    *   **相似度阈值**：80%
    *   **排除词/例外句子**：`好的`、`嗯`、`知道了` (这些词虽然会重复，但没有实际意义)
*   **具体示例**：
    *   00:15 客户: “我的那个包裹到底寄出来了没有？”
    *   00:45 客户: “你们能查下我的订单发货了没？”
    *   01:10 客户: “我都等了三天了，为什么物流一直没更新？”
*   **说明**：这三句话字面上完全不同，但核心意思高度一致。上下文重复检查可以轻松识别出这种“变着法儿的重复”，从而标记这通电话可能存在“客服未能解决问题”的风险。

---

#### 场景二：监控客服的机械式、无效回复

有些客服在应对疑难问题时，可能会反复使用同一句标准话术来敷衍，而不是解决问题。

*   **目标**：发现客服是否在用同样的话术“打太极”。
*   **配置思路**：
    *   **检测角色**：客服
    *   **检查逻辑**：在120秒内，如果客服说了2次或以上**完全相同**的话，则规则命中。
    *   **相似度阈值**：100% (或选择“精确匹配”模式)
*   **具体示例**：
    *   02:30 客户: “你还是没告诉我怎么解决啊。”
    *   02:35 客服: “非常抱歉，给您带来不好的体验。”
    *   03:10 客户: “所以到底怎么办？”
    *   03:15 客服: “非常抱歉，给您带来不好的体验。”
*   **说明**：这种精确的重复清楚地表明客服可能没有提供实质性的帮助，只是在机械地道歉。这对提升服务质量至关重要。

---

#### 场景三：检测通话中的网络或设备问题

当通话双方听不清时，会出现大量的重复确认。

*   **目标**：识别因技术问题导致的无效沟通。
*   **配置思路**：
    *   **检测角色**：所有角色
    *   **检查逻辑**：在30秒内，如果双方合计说了4次以上与“听”相关的话，则命中。
    *   **相似度阈值**：85%
    *   **核心话术/检查内容**：“你能听到吗”、“喂”
*   **具体示例**：
    *   01:05 客服: “喂，您好，请问能听到吗？”
    *   01:10 客户: “喂？喂？听不清。”
    *   01:15 客服: “您那边信号还好吗？现在能听见了吗？”
    *   01:20 客户: “声音断断续续的，你再说说看？”
*   **说明**：这种密集的、与通话状态相关的重复，是通话质量差的明显特征。系统可以自动标记这类通话，用于后续网络质量分析。