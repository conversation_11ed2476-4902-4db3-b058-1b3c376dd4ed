### “非正常挂机”算子核心用途

**非正常挂机检查**的核心功能是**识别那些以不规范或不礼貌方式结束的通话**。它主要通过分析**谁说了最后一句话**以及**最后一句话到通话结束之间的时长**来判断。

在更广义的实践中，它通常指：
1.  **突然挂断**：一方话未说完，另一方就挂断了电话。
2.  **客户怒挂**：在激烈对话后，客户不等客服说完话就挂断。
3.  **客服先挂**：在任何情况下，客服在客户之前挂断电话（通常是严重违规）。

---

### 主要应用场景及示例

#### 场景一：识别客户强烈不满（客户怒挂）

这是最能体现客户负面情绪的终极行为。

*   **目标**：找出所有被客户含怒挂断的电话，作为最高优先级的复盘和安抚对象。
*   **配置思路**（此场景依赖系统能力，可能需要更复杂的规则组合）：
    *   **条件A**：“抢话检查”命中，客户在最后一分钟内有抢话行为。
    *   **条件B**：“角色判断”命中，最后一句话是客服说的。
    *   **条件C**：“非正常挂机”命中，最后一句话结束到挂机时间 **小于 1** 秒。
*   **具体示例**：
    *   客服：“先生关于您的问题我还需要和主管确……”
    *   *(客户直接挂断电话)*
*   **说明**：客服话说到一半，通话戛然而止。这清晰地表明客户的耐心已经耗尽，不愿意再听任何解释。将这些通话筛选出来，管理团队需要立刻启动客户安抚流程，分析服务失败的根本原因，防止客户流失或公开投诉。

---

#### 场景二：监控客服严重服务违规（客服先挂）

在呼叫中心的服务准则中，除非客户明确同意，否则严禁客服先于客户挂机。

*   **目标**：杜绝客服主动挂断客户电话的恶劣行为。
*   **配置思路**：
    *   **条件A**：“角色判断”命中，最后一句话是客户说的。
    *   **条件B**：“非正常挂机”命中，最后一句话结束时间到挂机时间 **小于 2** 秒。
*   **具体示例**：
    *   客户：“我不管，你们今天必须给我一个说法！”
    *   *(客服无言以对，主动挂断电话)*
*   **说明**：当最后一句话是客户说的，而通话又在一两秒内迅速结束，这极大概率是客服方面执行了挂机操作。这是一种极其严重的服务事故，必须严查。此规则能精准地捕捉到这种行为。

---

#### 场景三：规范通话结束效率（如文档所示）

如您提供的文档所示，该算子也可用于优化挂机流程，提高客服的工作效率。

*   **目标**：确保在双方都无话可说时，客服能主动、礼貌地结束通话，避免无效等待。
*   **配置思路**：
    *   **检查逻辑**：最后一句话结束时间到挂机时间 **大于 5** 秒
    *   **最后一句话的角色**：任意
*   **具体示例**：
    *   客服：“好的，那祝您生活愉快，再见。”
    *   客户：“嗯，好。”
    *   *(……双方沉默，等待了8秒钟，电话才被挂断……)*
*   **说明**：这段长达8秒的“垃圾时间”降低了客服的接待效率。此规则命中了这种情况，意在提醒客服：在确认客户没有其他问题，并说完标准结束语后，如果客户几秒内没有主动挂机，客服应当主动结束通话，以便接待下一位客户。