### “正则表达式检查”算子核心用途

核心用途是利用一个“规则字符串”（即正则表达式）去匹配和过滤对话内容。它极其适合用来处理有固定结构的信息，比如各种号码、ID、代码或者特定句式。

与前两者相比：
*   **关键词检查** 找的是 **“词”**。
*   **文本相似度检查** 找的是 **“意”**。
*   **正则表达式检查** 找的是 **“形”** (格式、模式、结构)。

### 主要应用场景及示例

---

#### 场景一：验证和识别结构化信息

这是正则表达式最经典的用途，用于从对话中精准地识别、提取或验证格式固定的信息。

*   **示例1：检测对话中是否提及手机号**
    *   **目标**：识别出客服或客户在对话中透露的11位手机号码，用于信息安全监控。
    *   **配置**：
        *   **检测角色**：所有角色
        *   **命中规则 (Regex)**：`1[3456789]\d{9}`
    *   **说明**：
        *   `1`：必须以数字1开头。
        *   `[3456789]`：第二位是3到9中的任意一个数字。
        *   `\d{9}`：后面跟着9个任意数字 (`\d`代表数字, `{9}`代表重复9次)。
        *   **能匹配**：“我的手机是13812345678”、“你记一下15987654321”
        *   **不能匹配**：“我电话尾号是45678”、“订单号12345678901”（因为它不是1开头）

*   **示例2：识别身份证号码**
    *   **目标**：防止对话中出现身份证号等敏感信息。
    *   **配置**：
        *   **命中规则 (Regex)**：`\d{17}[\dX]` (这是一个简化的表达式)
    *   **说明**：
        *   `\d{17}`：匹配17个数字。
        *   `[\dX]`：最后一位可以是数字或字母X（大小写不敏感）。
        *   这个规则可以非常准确地从大段文本中“捞”出身份证号，而不会被其他长串数字干扰。

---

#### 场景二：检查特定流程话术的结构

有时候，我们不仅关心客服说了什么词，还关心他们说的顺序和格式。

*   **示例1：检查价格播报的准确性**
    *   **目标**：确保客服在报价时，清晰地说出了“XX元”或“XX块”的格式。
    *   **配置**：
        *   **检测角色**：客服
        *   **命中规则 (Regex)**：`\d+(块|元)`
    *   **说明**：
        *   `\d+`：匹配一个或多个数字。
        *   `(块|元)`：后面紧跟着“块”或者“元”。
        *   **能匹配**：“这个套餐是99元”、“只要198块”
        *   **不能匹配**：“价格是九十九”、“这个要两百” （因为它不符合“数字+单位”的格式）

*   **示例2：确保风险提示语在特定内容之前**
    *   **目标**：要求客服在引导客户操作前，必须先说风险提示。
    *   **配置**：
        *   **命中规则 (Regex)**：`风险.*(操作|办理|确认)`
    *   **说明**：
        *   `风险`：句子里必须出现“风险”这个词。
        *   `.*`：代表中间可以有任意数量的任意字符。
        *   `(操作|办理|确认)`：后面必须出现“操作”、“办理”或“确认”中的一个。
        *   **能匹配**：“在进行此操作前，我需要提示您风险”、“温馨提示，该业务有风险，现在为您办理吗？”
        *   **不能匹配**：“现在为您办理，对了，这个有风险” （顺序不对）

---

#### 场景三：利用“排除”功能进行精细化过滤

如文档所示，正则表达式检查还有一个强大的“排除”功能，可以先大范围命中，再小范围排除。

*   **示例：检测所有非联通的手机号**
    *   **目标**：找出所有移动和电信的手机号，用于业务分析。
    *   **配置**：
        *   **命中规则**：`1[3-9]\d{9}` (命中所有可能的手机号)
        *   **排除规则**：`1(3[0-2]|5[56]|8[56])\d{8}` (排除所有联通号段)
    *   **说明**：这个规则首先会匹配所有看起来像手机号的号码，然后从中剔除掉所有已知的联通号段，剩下的就是移动和电信的号码了。这比写一个巨大的、包含所有移动和电信号段的命中规则要简单得多。