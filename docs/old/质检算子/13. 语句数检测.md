### “对话语句数检测”算子核心用途

**对话语句数检测**的核心功能是**量化通话中各方的参与程度**。它通过统计客服或客户在整通电话里发言的总句数，来识别那些**对话失衡**或**参与度异常**的通话。

一个健康的对话通常是“你来我往”的，如果一方说得过多，而另一方说得过少，往往预示着沟通中存在问题。

### 主要应用场景及示例

---

#### 场景一：识别客服主导的“一言堂”式服务（客服说太多）

*   **核心问题**：客服滔滔不绝，没有给客户留出提问和反馈的空间。
*   **目标**：找出那些可能因过分推销或机械式介绍，导致客户无法插话的通话。
*   **配置思路**：
    *   **条件A**: 检测角色 **客服**，对话语句数 **大于 50** 句。
    *   **条件B**: 检测角色 **客户**，对话语句数 **小于 10** 句。
    *   **组合逻辑**: **A and B**
*   **示例场景**：
    *   在一个5分钟的电话里，客服一个人说了60多句话，详细地介绍了产品的所有功能、优惠和注意事项，而客户全程只说了几句“嗯”、“好的”、“知道了”。
*   **分析**：
    *   规则命中。这种极度不平衡的对话，很可能是一次失败的沟通。客服可能没有真正了解客户的需求，只是在单向地灌输信息。这对于销售场景是致命的，客户很可能早已失去兴趣。管理者可以复盘这类通话，培训客服应如何通过提问来引导对话，而不是自顾自地演讲。

---

#### 场景二：识别客户沉默或服务失败（客户说太少）

*   **核心问题**：客户几乎不说话，通话缺乏有效信息。
*   **目标**：找出那些客户参与度极低的通话，分析其背后的原因。
*   **配置思路**：
    *   **检测角色**：**客户**
    *   **检查逻辑**：对话语句数 **小于 3** 句
    *   (可以结合“通话时长 > 60秒”来排除那些正常的短通话)
*   **示例场景**：
    *   一通持续了3分钟的电话，客服一直在努力提问和介绍，但客户从头到尾只回答了“喂”和“不需要”两句话，然后就挂断了。
*   **分析**：
    *   规则命中。客户的极低参与度表明：
        1.  **无效外呼**：这可能是一个完全没有需求的客户，客服未能有效破冰。
        2.  **服务失败**：客服可能在通话早期就说错了话，导致客户失去了沟通的意愿。
        3.  **技术问题**：客户那边可能存在麦克风故障。
    *   筛选出这类通话，有助于评估外呼名单的质量和客服的开场技巧。

---

#### 场景三：识别客户主导的冗长抱怨（客户说太多）

*   **核心问题**：客户长时间、不间断地倾诉或抱怨，客服难以介入。
*   **目标**：定位那些客户占主导地位的、充满大量情绪宣泄的通话。
*   **配置思路**：
    *   **检测角色**：**客户**
    *   **检查逻辑**：对话语句数 **大于 80** 句
*   **示例场景**：
    *   一位极度不满的客户，在电话接通后，一个人滔滔不绝地讲了10分钟，详细叙述了他糟糕的经历，期间客服几乎没有插话的机会。
*   **分析**：
    *   规则命中。这种客户发言占绝对主导的超长通话，是典型的高风险投诉案例。客服是否在适当时机进行了有效的安抚和引导，是评价这次服务质量的关键。将这类通话筛选出来，可以作为“如何处理高难度客户”的培训材料。