
---

**首页 > 智能对话分析 > 操作指南 > 智能对话分析（新版） > 质检方案管理 > 质检算子使用介绍 > 文字检查 > 信息实体检查**

产品详情 | ❤️ 我的收藏

# **信息实体检查**
更新时间: 2025-02-07 14:51:25

> **② 说明** 仅支持普通规则使用

*   **功能介绍**：支持检查句子中是否包含系统实体，如果包含则判断是否为某字段的相应内容。
*   **配置方法**：
    *   **检测角色**：角色分为：客服、客户、所用角色。
    *   **检测范围**：全文、指定范围
        *   全文：检测全文。
        *   指定范围：可指定第 a~b 句。
    *   **实体选择**：中选择您需要的实体信息即可，例如：日期、时间、省份等；其支持"="、"!"、"包含"、"不包含"共 4 种逻辑运算类型，默认为"="。以及选择系统常规字段或者更多自定义字段。

**(图片为UI配置界面截图)**

**e ^ 信息实体检查**
| | |
| :--- | :--- |
| **检测角色** | 所有角色 |
| **前置条件** | 无 |
| **检测范围** | 全文 |
| **实体选择** | 日期 | = | [更多自定义字段] |
| | **+ 新增条件** | (下拉菜单选项：=, !=, 包含, 不包含) |

| | |
| :--- | :--- |
| **评分配置** | |
| 规则评分 | (开关按钮，处于关闭状态) |

*   **本期支持以下实体**：

| 显示名称 | 数据类型 | 描述 |
| :--- | :--- | :--- |
| **日期** | string | 今天、2018 年 3 月 4 号、周三、2018-03-05、重要节假日等 |
| **时间** | string | 3 点、3 点一刻、18 点等 |
| **省份** | string | 北京、山东等省级行政区, 一级行政区（省级行政区）：34 个（23 个省、5 个自治区、4 个直辖市、2 个特别行政区） |
| **城市** | string | 北京、济南等地级行政区；增加国外热门城市；二级行政区（地级行政区）：334 个（294 个地级市、7 个地区、30 个自治州、3 个盟） |
| **区县** | string | 朝阳区等县级行政区, 三级行政区（县级行政区）：2851 个（965 个市辖区、366 个县级市、1349 个县、117 个自治县、49 个旗、3 个自治旗、1 个特区、1 个林区） |
| **身份证号码** | long | 身份证号码 |
| **人名** | string | 人名实体，比如张三、李四等 |
| **邮箱** | string | 邮箱 |

---------------------------------------------------------------------------------------------------------------------------------------------------

正确的理解：实体识别与随路参数的动态比较
您指出的核心是：右侧的字段并非一个可以留空或手动输入固定值的地方，而是一个必须选择的、与通话本身关联的"信息字段"下拉菜单。

这意味着，"信息实体检查"算子的核心功能，并不是简单地检查"有没有提到某个实体"，而是执行一个更智能、更动态的比较操作。其工作流程如下：

第一步：在对话中识别实体。 系统会根据您在"实体选择"中指定的类型（例如"人名"、"城市"或"身份证号"），在客服或客户的对话文本中进行智能识别，并抽取出具体的值。

例如，您选择检查"人名"，系统在客户的话中识别出了"张三"这个名字。
第二步：获取"随路参数"的值。 系统会根据您在右侧下拉菜单中选择的字段（例如"客服姓名"），从这通电话的背景数据（也称为"随路参数"或"元数据"）中，获取对应的值。这个值是呼叫中心系统记录的，并非来自对话内容。

例如，您选择了"客服姓名"，系统查到这通电话记录的客服姓名是"李四"。
第三步：执行比较。 系统将第一步从对话中识别出的实体值，与第二步从随路参数中获取的字段值，按照您选择的逻辑运算符（如"="或"!"）进行比较。

系统比较客户提到的"张三"和记录在案的客服姓名"李四"。因为"张三"不等于"李四"，所以如果您的逻辑是人名 = 客服姓名，那么这个条件就不会命中。
实际应用场景示例
让我们用您截图中的选项来举一个更清晰的例子：

质检目标： 检查客户在通话中提到的客服姓名，是否与实际服务的客服姓名一致。

规则配置：

检测角色： 客户
实体选择： 人名
逻辑运算符： =
字段选择（左侧）： 系统常规字段
字段选择（右侧）： 客服姓名
这条规则的含义是： "如果客户在对话中提到了一个人名，检查这个人名是否等于这通电话记录的'客服姓名'。"

如果通话记录的客服姓名是"王经理"，而客户说"王经理您好"，规则就会命中。
如果客户说的是"李经理您好"，规则就不会命中，这可能表明客户找错了人，或者坐席没有正确表明身份。
"系统常规字段"与"自定义数据"的真正区别
您的截图也清晰地揭示了这两者的区别：

系统常规字段： 指的是呼叫中心系统通常都会记录的一些标准化、通用的随路参数，如"客服姓名"、"客服ID"、"技能组名称"等。这些是平台预设好的、可以直接选用的元数据字段。
自定义数据1/2/3...： 指的是您的业务系统在发起通话时，可以额外传入的一些非标准、业务特有的信息。例如，您可以将客户的"会员等级"作为"自定义数据1"传入，然后配置规则检查客户提到的等级是否与档案一致。
总结与修正
再次感谢您的宝贵反馈。总结一下，根据您提供的实际界面：

核心功能是比较： "信息实体检查"算子的主要功能是将对话中识别出的实体，与通话的随路参数（元数据）进行比较。
无法留空： 您是对的，右侧字段不能留空。这意味着，单纯检查"某个实体是否存在"（例如，检查客户是否提到了身份证号，而不关心具体号码）这个操作，无法通过将右侧留空来实现。您必须选择一个字段进行比较。
动态而非静态： 比较的对象不是一个您手动输入的固定文本（如"山东省"），而是一个动态的、随每通电话变化的元数据值。
这个功能设计得非常巧妙，因为它能帮助您核验对话内容与业务记录的一致性，从而发现更多深层次的服务质量问题。希望这次的解释能够完全解答您的疑惑！

---------------------------------------------------------------------------------------------------------------------------------------------------

## 🔍 功能深度解析

### 核心工作原理

信息实体检查算子的独特之处在于它不是简单的"关键词匹配"，而是一个**智能的三步验证流程**：

1. **🎯 实体识别**：在对话中自动识别指定类型的实体（如人名"张三"、城市"上海"、日期"明天"）
2. **📋 参数获取**：从通话的随路参数（元数据）中获取对应字段的值（如客服姓名"李四"、客户城市"北京"）
3. **⚖️ 智能比较**：将识别出的实体值与随路参数值进行逻辑比较，验证一致性

### 业务价值与应用场景

#### 🛡️ 身份验证与安全
- **客服身份一致性**：验证客户提到的客服姓名与实际服务客服是否一致，防止身份混乱
- **敏感信息防护**：检测客服是否意外泄露客户身份证号码等敏感信息
- **角色权限验证**：确认对话内容与客服技能组权限范围匹配

#### 📊 数据质量保障
- **地址信息核验**：验证客户提供的地址信息与档案记录的一致性
- **预约信息确认**：检查对话中的日期时间与系统预约记录是否匹配
- **业务归属验证**：确认对话涉及的业务线与通话记录的业务分类一致

#### 🔍 服务质量监控
- **信息准确性**：监控服务过程中信息传递的准确性和一致性
- **流程规范性**：确保客服按规范核实和使用客户信息
- **异常情况发现**：及时发现信息不匹配、流程偏差等质量问题

### 配置要点与最佳实践

#### ⚙️ 字段类型选择策略
- **系统常规字段**：选择呼叫中心预设的标准字段（客服姓名、技能组、自定义数据1-3等）
- **更多自定义字段**：当需要比较的字段不在预设选项中时，手动输入字段名

#### 🎨 逻辑运算符应用
- **等于（=）**：验证完全一致性，如客服身份确认
- **不等于（!=）**：发现差异和冲突，如预约日期不匹配
- **包含**：模糊匹配验证，如业务范围确认
- **不包含**：安全检查，如敏感信息防护

#### 📍 检测范围优化
- **全文检测**：适用于整体一致性验证
- **指定范围**：针对特定对话环节的精确检查，如开场白、信息确认环节

### 实际案例分析

#### 案例1：客服身份验证
```
配置：人名 = 客服姓名（系统常规字段）
对话：客户："王经理您好，我想咨询业务"
元数据：客服姓名 = "王经理"
结果：✅ 命中（身份一致，服务规范）
```

#### 案例2：地址信息核验
```
配置：城市 = 自定义数据1（系统常规字段）
对话：客户："我在上海"
元数据：自定义数据1 = "上海"
结果：✅ 命中（地址信息一致）
```

#### 案例3：敏感信息防护
```
配置：身份证号码 != 自定义数据3（系统常规字段）
对话：客服："您的身份信息我已经看到了"
元数据：未检测到身份证号码泄露
结果：✅ 命中（信息安全得到保障）
```

### 注意事项与限制

#### ⚠️ 配置限制
- 期望值字段不能留空，必须选择具体的随路参数字段
- 实体类型需要与实际比较需求匹配
- 逻辑运算符的选择要符合业务验证逻辑

#### 💡 性能建议
- 合理设置检测范围，避免不必要的全文检测
- 根据业务重要性调整检测角色（客服/客户/全部）
- 定期review规则效果，优化配置参数

### 与其他算子的配合使用

信息实体检查可以与以下算子形成有效的质检组合：
- **关键词检查**：先用关键词确定业务场景，再用实体检查验证信息一致性
- **正则表达式检查**：处理复杂格式的实体识别需求
- **文本相似度检查**：验证表达方式的相似性和规范性

---

通过深入理解信息实体检查的工作原理和应用价值，您可以更有效地利用这个强大的质检工具，提升服务质量管理的精准度和效率。

