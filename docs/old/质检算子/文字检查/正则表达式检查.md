
---

**首页 > 智能对话分析 > 操作指南 > 智能对话分析（新版） > 质检方案管理 > 质检算子使用介绍 > 文字检查 > 正则表达式检查**

产品详情 | ❤️ 我的收藏

# **正则表达式检查**
更新时间: 2025-02-07 14:50:40

本文介绍正则表达式检查如何进行配置。

*   **功能介绍**：检测文本内容，是否符合正则表达式配置的规则内容。
*   **配置方法**：
    *   **检测角色**：角色分为：客服、客户、所用角色。
    *   **检测范围**：全文、指定范围
        *   全文：检测全文。
        *   指定范围：可指定第 a~b 句。
    *   **命中**：必填项，输入期望命中的正则规则内容。
    *   **排除**：非必填，在“排除”中输入在命中规则的基础上，期望过滤不命中的规则内容。
    *   **扩展功能**：单句活内生效
        *   匹配时是否限制在单句话中（单句话是指中间没有逗号、句号等），举例说明：
            测试文本：“你好，这里是 xxx，向您推荐一款产品”；
            当勾选了“单句活内生效”时，会将测试文本拆分为“你好”、“这里是 xxx”、“向您推荐一款产品”3 小段话分别进行分析，当其中 1 段或多段与当前条件匹配时，才算命中；
            当未勾选“单句活内生效”时，会将测试文本当作一整段话进行质检。
*   **测试**：在填写完文本相似度检查后，可以在测试中查看配置的效果，这里我配置的命中手机号，但是排除掉 13 开头的号码。

**(图片为UI配置界面与测试结果截图)**

**c ^ 正则表达式检查**
*   **检测角色**: 所有角色
*   **前置条件**: 无
*   **检测范围**: 全文
*   默认使用的匹配逻辑是：不区分大小写、多行匹配、换行符/n也算作一个字符。**③ 如何使用正则表达式**
*   **命中**: 1[3|4|5|7|8][0-9]{9}
*   **排除**: 13[0-9]{9}
*   **扩展功能**: [ ] 单句活内生效
*   **测试**:
    *   [ 13111111111 x ] [ 测试 ] ● 未命中
*   **+ 新增条件**

---

**正则表达式配置详细说明：**
正则表达式是对字符串操作的一种逻辑公式，它就是用事先定义好的一些特定字符及这些特定字符的组合，组成一个“规则字符串”，这个“规则字符串”用来表达对字符串的一种过滤逻辑。

*   **常用字符**：

| 元字符 | 描述 |
| :--- | :--- |
| **[ ]** | 字符范围。匹配指定范围内的任意字符。[A-Z] 26 个大写字母；[a-z] 26 个小写字母；[0-9] 0 至 9 数字；[A-Za-z0-9] 26 个大写字母、26 个小写字母和 0 至 9 数字 |
| **()** | 标记一个子表达式的开始和结束位置 |
| **\|** | 逻辑或。如：a\|b, a 或者 b |
| **.** | 匹配除“\\n”和“\\r”之外的任何单个字符 |
| **\*** | 匹配前面的子表达式任意次。 |
| **{ }** | 匹配长度。如：{n,m}, m 和 n 均为非负整数，其中 n <= m。最少匹配 n 次且最多匹配 m 次。 |

*   **示例**：

| 匹配内容 | 正则表达式 |
| :--- | :--- |
| 3 到 16 位包含小写字母、数字、_或-的用户名 | [a-z0-9_-]{3,16} |
| 身份证号（18 位） | ([0-9]{17}(X\|x))\|([0-9]{18}) |
| 验证手机号码 | 1[3\|4\|5\|7\|8][0-9]{9} |
| 必须以“请问”开头，一句话中包含：车牌号、发动机号、驾驶证号码任何一个即可 | 请问.*(车牌号\|发动机号\|驾驶证号码) |