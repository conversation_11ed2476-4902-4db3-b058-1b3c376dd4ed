
---

**首页 > 智能对话分析 > 操作指南 > 智能对话分析（新版） > 质检方案管理 > 质检算子使用介绍 > 语音检查 > 抢话设置**

产品详情 | ❤️ 我的收藏

# **抢话设置**

更新时间: 2025-01-24 18:06:52

本文介绍抢话检查如何进行配置。

*   **功能介绍**：通常用来检测客服是否出现抢话现象。
*   **配置方法**：抢话时间我们一般设置在（2000-3000 毫秒），抢话句子的字数一般设置大于等于（4 个字），低于这个字数不算为抢话。

> **② 说明** 若命中率不符合预期：抢话发生时，相当于两个角色同时在讲话，对于单声道录音，录音转文本后，只能识别出一个角色说的话，所以抢话的情况很难检测出来。而双声道（立体声、双轨）录音，两个角色的声音保存在两个声道中，所以即使声音听起来是重叠在一起的，录音转文本后声音重叠部分依然可以被识别出来，所以出现抢话时是可以准确检测出来的。
>
> **抢话时间**：发生抢话句子前一句的结束时间减去该抢话句子的开始时间。
>
> **抢话字数**：如果抢话了，但这句活字数小于设置的值，也不算抢话；比如，设置为 4，则“嗯”，“好的”这种不足 4 个字的句子是不算抢话的。
>
> **抢话延时判断**：延时判定抢话情况，比如，设置为 1000，则表示一方开始说话 1000 毫秒后，再出现对话重叠才算作抢话。

*   **使用示例**：假设现要检查客服是否有抢话时间大于 1000 毫秒，且抢话字数大于等于 4 个字，抢话延时时间配置为 0，具体配置如下图：

**条件配置**

| | |
| :--- | :--- |
| **检测内容** | 以下条件全部满足 |
| **检测条件**<br>**查看教程** | **a ^ 抢话检查** |
| | **检测角色**：所有角色 |
| | **检测范围**：全文 |
| | **检查逻辑**：抢话时间 >= [1000] 毫秒，抢话句子的字数 >= [4] 个字，抢话延时判定 [0] 毫秒，并且发生抢话句子的前一句的话语速大于 3.5 字/s 时，算作命中。命中率不符合预期的说明 |
| | **+ 新增条件** |

**评分配置**
| | |
| :--- | :--- |
| **规则评分** | (开关按钮，处于关闭状态) |