
---

**首页 > 智能对话分析 > 操作指南 > 智能对话分析（新版） > 质检方案管理 > 质检算子使用介绍 > 语音检查 > 通话静音检查**

产品详情 | ❤️ 我的收藏

# **通话静音检查**

更新时间: 2025-01-23 18:29:40

本文介绍通话静音检查如何进行配置。

*   **功能介绍**：检测通话过程中是否出现了静音。
*   **配置方法**：选择和配置期望的角色信息和时间长度等。
    *   **检测角色**：角色分为：客服、客户、所用角色。
    *   **检测范围**：全文、指定范围
        *   全文：检测全文。
        *   指定范围：可指定第 a~b 句。
    *   **检查逻辑**：选择【不同角色、不区分角色、相同角色】的静音时长【大于、小于、区间】的时间。该算子中的检测范围（不同角色之间、不区分角色、相同角色）的作用是：
        *   **不同角色之间**：静音之前的一句话的角色需要和该条件的适用角色不同；例如当前条件检测的是客服是否出现静音，那么客服出现静音之前的一句话需要是客户说的。
        *   **不区分角色**：不关心出现静音之前的一句话是哪个角色说的。
        *   **相同角色**：通过静音时长来检测相同角色在对话中是否存在异常。

*   **使用示例**：
    如下图，假设要检测客服是否出现静音超过 3 秒的情况；

**(图片为UI配置界面截图)**

**f ^ 通话静音检查**

| | |
| :--- | :--- |
| **检测角色** | 客服 |
| **前置条件** | 无 |
| **检测范围** | 全文 |
| **检查逻辑** | [不同角色之间] 静音段时长 [大于] [3] 秒 (静音时长等于后一句开始时间 减去 前一句结束时间) |
| | **+ 新增条件** |