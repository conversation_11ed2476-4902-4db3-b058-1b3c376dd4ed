### “通话静音检查”算子核心用途

**通话静音检查**的核心功能是检测在通话过程中，是否出现了**异常时长的静默**。这种静默可以发生在：
*   **客服说完话后**，客户长时间没反应。
*   **客户说完话后**，客服长时间没反应。
*   **通话过程中**，无差别地出现长时间的空白。

它通过分析音频能量（VAD - Voice Activity Detection）来实现，能精确地测量出每一段沉默的持续时间。

### 主要应用场景及示例

---

#### 场景一：监控客服响应效率（客服静音）

这是最常见的应用，用于确保客服在客户说完话后能及时、流畅地接续话题，避免让客户感到被冷落或等待。

*   **目标**：检测客服是否存在响应迟缓、查资料过久导致冷场的情况。
*   **配置思路**：
    *   **检查逻辑**：**不同角色之间** 的静音
    *   **检测角色**：客服
    *   **静音时长**：大于 **5** 秒
*   **具体示例**：
    *   `01:30` 客户: “我的那个订单号是 123456，你帮我查一下物流到哪了。”
    *   `01:31` *(客户说完话)*
    *   *(……长达6秒的沉默……)*
    *   `01:37` 客服: “好的，先生，请您稍等，我正在为您查询。”
*   **说明**：这里的 “不同角色之间” 指的是，静音前的最后一句话必须是**客户**说的（与检测角色“客服”不同）。如果客户说完话后，客服超过5秒没有做出任何回应（无论是回答、确认还是请求稍等），规则就会命中。这能有效发现那些业务不熟练、操作慢或者服务态度散漫的客服。

---

#### 场景二：识别客户潜在流失风险（客户静音）

当客户长时间不说话，可能表示他们对产品或方案不感兴趣、在犹豫，甚至是已经分心去做别的事情了。这对销售或挽留场景尤其重要。

*   **目标**：识别出那些可能对客服的推销或解释失去兴趣的客户。
*   **配置思路**：
    *   **检查逻辑**：**不同角色之间** 的静音
    *   **检测角色**：客户
    *   **静音时长**：大于 **10** 秒
*   **具体示例**：
    *   `05:20` 客服: “……以上就是我们这款白金套餐的所有优惠，每个月只需要99元，您觉得怎么样？”
    *   `05:22` *(客服说完话)*
    *   *(……长达12秒的沉默……)*
    *   `05:34` 客服: “喂？先生，请问您还在听吗？”
*   **说明**：客服介绍完方案后，客户长达10秒以上没有任何反馈。这通常是一个危险信号。规则命中后，管理者可以复盘录音，分析是客服的介绍方式有问题，还是这个产品本身对客户没有吸引力。

---

#### 场景三：检测通话质量或整体流畅度（全局静音）

有时候，通话中出现的大段空白与某一方无关，可能是网络问题、设备故障，或者双方都在等待。

*   **目标**：找出那些存在异常长时间中断的通话。
*   **配置思路**：
    *   **检查逻辑**：**不区分角色** 的静音
    *   **静音时长**：大于 **15** 秒
*   **具体示例**：
    *   `03:40` 客服: “您重启一下设备试试。”
    *   *(……长达20秒的沉默，可能是客户在重启设备……)*
    *   `04:00` 客户: “好了，我现在开机了。”
*   **说明**：这里的“不区分角色”意味着系统不在乎静音前是谁在说话。只要录音中出现了超过15秒的空白，无论原因为何，都会被标记。这对于发现技术故障（如断线、丢包）或评估一些需要客户线下操作的流程（如重启、填表）所耗费的平均时长非常有用。