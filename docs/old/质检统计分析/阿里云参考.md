质检概况
 
查询条件：今天、昨天、7天、30天、自定义日期区间，质检结果类型（下拉选择/全部、离线语音结果、离线文本结果、实时语音结果、实时文本结果、离线语音二次质检结果、离线文本二次质检结果、实时语音二次质检结果、实时文本二次质检结果、数据集语音类型结果、数据集文本类型结果），质检方案（下拉选择），技能组（下拉选择），坐席（下拉选择）
指标面板：总会话数（需要质检的会话总数）、平均得分（平均得分=所选坐席和所选结果类型会话得分总数/总会话数）、质检完成数（已经成功完成质检的会话总数）、质检完成率（质检完成率=质检完成数/总会话数）、违规数（质检过程中命中扣分规则的总会话数）、违规率（违规率=违规数/总会话数）、重度违规数（质检过程中出现重度违规的总会话数）、大模型质检完成率（大模型质检完成率=大模型质检的通话量/总的质检通话量）
较昨日（环比）
TOP10违规规则：会话数、坐席数，图表。
所有命中规则详情：列表，查询条件（规则名称/文本、不同方案数据合并统计/不同方案数据独立统计）
表头字段：
规则名称（固定）
规则种类：普通质检规则、流程质检规则、纯人工质检规则、知识库质检规则、大模型质检规则
规则评分：扣分项、加分项、一次性评分、未设置评分
质检方案
违规占比：违规占比=所有命中此扣分规则的会话总数/命中任一扣分规则的会话总数
重要程度：重度、中度、轻度
检测会话数
机检命中会话数：机器质检出的会话数
机检命中率：机检命中率=机检命中会话数/检测会话数
人工复核后命中会话数：人工复核后命中的会话数
人工复核后命中率：人工复核后命中率=人工复核后命中会话数/检测会话数
复核完成数
复核准确数
复核准确率：复核准确率=复核准确数/复核完成数
发起申诉数：所有客服对该规则发起的申诉总会话数
申诉完成数：该规则申诉完成的会话数，申诉完成包括申诉成功和申诉驳回两种情况
申诉成功数：该规则申诉成功的个数
申诉成功率：申诉成功数/申诉完成数
趋势（固定）	
操作（固定）
服务质量分析
 
查询条件：质检类型（呼叫中心质检）、结果类型（离线语音结果、离线文本结果、实时语音结果、实时文本结果、离线语音二次质检结果、离线文本二次质检结果、实时语音二次质检结果、实时文本二次质检结果）、质检方案（下拉选择）、今天、昨天、7天、30天、自定义日期区间
服务质量透视：图表
服务质量趋势：图表
服务质量得分分布：图表，坐席/技能组
服务质量详情：列表，坐席/技能组，查询条件（客服名称/技能组名称）
表头字段：
坐席名称/技能组名称
总会话数
已质检数
违规数
违规率
平均得分
操作
复核统计
 
查询条件：今天、昨天、7天、30天、自定义日期区间、质检结果类型（离线语音结果、离线文本结果、实时语音结果、实时文本结果、离线语音二次质检结果、离线文本二次质检结果、实时语音二次质检结果、实时文本二次质检结果）、质检方案（下拉选择）、复核人员（下拉选择）
指标面板：要求复核数（所选质检员要求完成的总复核会话数量）、复核完成数（所选质检员或管理员已完成的复核会话数量）、复核完成率（复核完成率=复核完成数/要求复核数）、复核准确率（复核准确率=复核准确数/复核完成数）、复核准确数（人工复核检查后，判定机器质检结果正确的数量）
较昨日（环比）
质检员复核列表：列表，查询条件（质检员名称）
表头字段：
质检员名称
要求复核数
复核完成数
复核完成率
复核准确数
复核准确率
操作
申诉统计
 
查询条件：质检类型（呼叫中心质检）、结果类型（离线语音结果、离线文本结果、实时语音结果、实时文本结果、离线语音二次质检结果、离线文本二次质检结果、实时语音二次质检结果、实时文本二次质检结果）、今天、昨天、7天、30天、自定义日期区间、质检方案（下拉选择）、技能组（下拉选择）、坐席（下拉选择）
指标面板：客服申诉数（所选客服发起的申诉总数）、申诉完成数（在发起申诉的总数中，申诉已经完成的数量。申诉完成包括以下两种情况：1、申诉成功 2、申诉完成终审（可能是成功或者驳回））、申诉完成率（申诉完成率=申诉完成数/申诉数）、申诉成功数（客服发起申诉后，至少有一个审核角色（例如质检员或质检员）确认申诉成功）、申诉成功率（申诉成功率=申诉成功数/申诉数）
较昨日（环比）
坐席申诉列表：列表，查询条件（坐席名称）
表头字段：
坐席名称
客服申诉数
申诉完成数
申诉完成率
申诉成功数
申诉成功率
操作
