### **2. AI质检准确性与复核策略分析报表 - 详细设计文档**

---

#### **2.1 报表概述**

*   **报表ID:** RPT_02
*   **报表名称:** AI质检准确性与复核策略分析
*   **核心目的:**
    1.  量化评估AI自动评分结果与人工专家判断的一致性，持续监控AI的准确度。
    2.  分析不同"复核策略"的有效性，判断哪些策略能最高效地发现问题录音，从而指导策略的优化。
*   **核心价值:** 通过数据驱动的方式，提升AI质检的可靠性，同时将有限的人工复核资源投入到最需要的地方（即最高的"问题发现率"），实现质检工作的"降本增效"。

---

#### **2.2 界面布局设计 (UI Layout)**

本报表推荐采用与全局驾驶舱类似的 **"顶部筛选器 + 网格化布局"** 设计。

*   **A区 (顶部): 筛选器区域 (Filters Area)**
    *   提供全局的数据筛选能力。
*   **B区 (中上): 核心指标卡区域 (KPI Cards Area)**
    *   以醒目的卡片形式展示最核心的准确性与效率指标。
*   **C区 (中部): 分析图表区域 (Analysis Charts Area)**
    *   占据页面的核心位置，分为 **"AI准确性分析"** 和 **"复核策略有效性分析"** 两个子区域。
*   **D区 (底部): 高偏差案例列表区域 (High-Deviation Cases Area)**
    *   以列表形式，直接展示AI与人工判断差异最大的案例，便于追溯和分析。

```
+----------------------------------------------------------------------+
| A: [时间范围] [质检方案] [复核策略] [复核员] [查询] [重置]              |
+----------------------------------------------------------------------+
| B: [ KPI卡片 1 ] [ KPI卡片 2 ] [ KPI卡片 3 ] [ KPI卡片 4 ]            |
+----------------------------------------------------------------------+
| C: [ AI准确性分析图表 1 ]             [ AI准确性分析图表 2 ]           |
|                                                                      |
|    [ 复核策略有效性图表 1 ]          [ 复核策略有效性图表 2 ]         |
+----------------------------------------------------------------------+
| D: [ 高偏差案例列表 (可滚动) ]                                       |
+----------------------------------------------------------------------+
```

---

#### **2.3 组件详细说明 (Component Details)**

##### **A. 筛选器区域 (Filters Area)**

*   **设计原则:** 所有筛选条件的变更，都需要用户主动点击【查询】按钮后才会生效。

*   **组件1: 时间范围选择器**
    *   **UI控件:** `DateRangePicker`
    *   **功能描述:** 支持预设选项 ("最近7天", "最近30天") 和自定义时间范围。
    *   **默认值:** "最近30天"。

*   **组件2: 质检方案选择器**
    *   **UI控件:** `Select` (支持多选和搜索)。
    *   **功能描述:** 分析AI在不同业务场景质检方案下的准确度。
    *   **默认值:** "全部方案"。

*   **组件3: 复核策略选择器**
    *   **UI控件:** `Select` (支持多选和搜索)。
    *   **功能描述:** **核心筛选维度**，用于对比不同策略的有效性。
    *   **数据源:** 系统内配置的所有复核策略（如："低分触发", "随机抽查", "关键词触发", "高情绪波动触发" 等）。
    *   **默认值:** "全部策略"。

*   **组件4: 复核员选择器**
    *   **UI控件:** `Select` (支持多选和搜索)。
    *   **功能描述:** 用于校准和分析不同复核员的判断标准是否存在差异。
    *   **默认值:** "全部复核员"。

*   **组件5: 操作按钮区**
    *   **UI控件:** `Button` 组，包含【查询】和【重置】。

##### **B. 核心指标卡区域 (KPI Cards Area)**

1.  **`平均分数偏差 (绝对值)`**
    *   **计算逻辑:** `平均( |人工最终分 - AI初检分| )`。该值越小，代表AI的整体评分越接近人工。
2.  **`复核分数修改率`**
    *   **计算逻辑:** `(分数被人工修改的复核记录数 / 总复核记录数) * 100%`。衡量AI结果可靠性的核心指标，此比例越高，说明AI的误判越多。
3.  **`AI-人工一致性通过率`**
    *   **计算逻辑:** `(分数偏差在预设阈值内的记录数 / 总复核记录数) * 100%`。例如，阈值设为±5分，则此指标衡量了多大比例的AI评分与人工评分基本一致。
4.  **`最高效复核策略`**
    *   **计算逻辑:** 根据 **"各复核策略的分数修改率"** 对比结果，展示修改率最高的策略名称。直接告诉管理者哪种策略发现问题的效率最高。

##### **C. 分析图表区域 (Analysis Charts Area)**

**子区域1: AI质检准确性分析**

*   **图表1: `AI-人工分数偏差分布图`**
    *   **图表类型:** 直方图 (Histogram)。
    *   **X轴:** 分数偏差值 `(人工最终分 - AI初检分)`。
    *   **Y轴:** 案例数量。
    *   **解读:** 理想状态是图形呈正态分布，且中心峰值非常接近0。如果图形整体偏左，说明AI系统性高估分数；偏右则说明AI系统性低估分数。

*   **图表2: `平均分数偏差趋势图`**
    *   **图表类型:** 折线图 (Line Chart)。
    *   **X轴:** 时间（按日/周聚合）。
    *   **Y轴:** 平均分数偏差（绝对值）。
    *   **解读:** 用于持续监控AI模型的稳定性。如果折线突然走高，可能意味着AI模型出现了异常或数据模式发生了变化。

**子区域2: 复核策略有效性分析**

*   **图表3: `各复核策略触发量与占比`**
    *   **图表类型:** 堆叠柱状图 (Stacked Column Chart) 或 环形图 (Donut Chart)。
    *   **维度:** 复核策略名称。
    *   **数值:** 各策略触发的复核任务数量及占比。
    *   **解读:** 直观展示人工复核的工作量是由哪些策略贡献的。

*   **图表4: `各复核策略的有效性对比`**
    *   **图表类型:** 分组条形图 (Grouped Bar Chart)。
    *   **Y轴:** 复核策略名称。
    *   **X轴:** 百分比/数值。
    *   **分组指标:**
        *   `分数修改率`: **(核心指标)** 不同策略触发的录音中，被人工改分的比例。
        *   `平均分数偏差`: 不同策略触发的录音中，AI与人工的平均分差。
    *   **解读:** 这是整个报表的核心。若"随机抽查"策略的"分数修改率"是5%，而"低分触发"策略的修改率高达60%，则证明"低分触发"是更高效、更精准的问题发现手段。

##### **D. 高偏差案例列表区域 (High-Deviation Cases Area)**

*   **展现形式:** 可滚动的表格 (Table)。
*   **排序:** 默认按"分数偏差"的绝对值降序排列。
*   **列定义:**
    1.  `通话ID`: 提供链接可下钻至通话详情页。
    2.  `AI初检分`: AI给出的原始分数。
    3.  `人工最终分`: 复核员给出的最终分数。
    4.  `分数偏差`: `人工最终分 - AI初检分`，并用不同颜色标识正负。
    5.  `复核员`: 执行复核的人员姓名。
    6.  `复核策略`: 该案例是由哪个策略触发的。
    7.  `操作`: [播放录音] 按钮。

---

#### **2.4 数据刷新机制 (Data Refresh)**

*   **刷新策略:** T+1。
*   **说明:** 为保证系统性能，本报表数据每日凌晨进行一次批量计算和更新。

---
#### **2.5 附注 (Notes)**

*   所有图表和指标的计算都应严格遵守顶部筛选器所设定的范围。
*   页面首次加载或查询时，应有明确的加载中（Loading）状态提示。
*   **"分数偏差"** 的计算公式为 `人工最终分 - AI初检分`，在计算平均绝对偏差时，使用其绝对值。
*   图表应支持必要的交互，如鼠标悬浮(Hover)提示具体数值，点击图例可隐藏/显示对应数据系列。 