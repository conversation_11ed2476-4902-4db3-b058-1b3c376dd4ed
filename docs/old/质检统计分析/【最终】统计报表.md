
这套设计严格遵循您系统的特点：**100% AI初检，人工只做复核**。因此，报表的核心将围绕“AI质检效能”、“服务质量短板”、“复核工作的价值”和“申诉反馈闭环”这几个关键点展开。

以下是四个报表的详细设计方案，采用了您提供的大纲格式，力求清晰、可用。

---

### **本系统统计分析报表设计方案**

#### **一、 质检运营总览 (QA Operations Overview)**

*   **对标参考:** 阿里云 - `质检概况`
*   **面向用户:** 质检主管、呼叫中心管理层
*   **核心目的:** 从最高维度宏观监控整体质检工作的健康度、AI运行效率和核心风险，为管理决策提供数据支持。
*   **统计维度 (Filters):**
    *   `时间范围` (日/周/月/自定义)
    *   `班组/团队`
    *   `质检方案`
*   **报表内容:**
    *   **核心指标卡 (KPI Cards):**
        *   `AI总质检量`: 周期内AI完成初检的通话总量。
        *   `总复核量`: 周期内人工完成复核的总量。
        *   `复核率`: `总复核量` / `AI总质检量`，反映人工审核的投入比例。
        *   `整体平均分`: 所有质检通话的最终得分（复核过的以复核分为准）的平均值。
        *   `整体合格率`: 最终得分达到合格标准的通话占比。
        *   `整体申诉率`: `总申诉数` / `AI总质检量`。
    *   **运营趋势分析 (Trend Analysis):**
        *   `质量与效率趋势图`: 按日/周/月，用双轴图展示`整体平均分`（折线）和`AI总质检量`（柱状图）的变化。
    *   **问题风险聚焦 (Risk Focus):**
        *   `高频失分规则 TOP 10`: 全局范围内，导致坐席失分次数最多的规则。
        *   `严重错误规则 TOP 10`: 全局范围内，被标记为“严重错误”且触发次数最多的规则。
        *   `团队平均分排名`: 各团队的平均分、合格率对比排名（柱状图）。

#### **二、 AI质检复核效能分析 (AI & Review Effectiveness Analysis)**

*   **对标参考:** 阿里云 - `复核统计`
*   **面向用户:** 质检主管
*   **核心目的:**
    1.  精确量化AI质检的准确性，识别AI模型的短板。
    2.  评估人工复核的价值和一致性，指导复核策略和人员培训。
*   **统计维度 (Filters):**
    *   `时间范围`
    *   `质检方案`
    *   `复核员`
    *   `复核策略` (如适用，例如：“低分触发”、“随机抽样”等)
*   **报表内容:**
    *   **第一部分：AI质检准确度评估**
        *   **核心指标卡 (KPI Cards):**
            *   `复核分数修改率`: 在所有被复核的通话中，分数被人工修改的比例。**这是衡量AI准确性的核心指标。**
            *   `平均分数偏差 (绝对值)`: `| 人工最终分 - AI初检分 |` 的平均值，代表AI与人工判断的平均差距。
            *   `AI结果采纳率`: `1 - 复核分数修改率`。
        *   **分析图表:**
            *   `AI-人工分数偏差分布图`: 直方图，展示 `(人工最终分 - AI初检分)` 的分布。用于判断AI是系统性高估还是低估分数。
            *   `高偏差案例列表`: 列出AI与人工评分差异最大的Top N条通话，并提供钻取功能，用于AI模型调优。
    *   **第二部分：人工复核工作分析**
        *   **核心指标卡 (KPI Cards):**
            *   `人均日复核量`: 复核员每个工作日平均完成的复核任务数。
        *   **分析图表:**
            *   `复核员一致性分析`: 以表格形式展示每位复核员的 `复核总量`、`平均复核耗时`、`分数修改率`、`平均分数偏差`。用于发现判断尺度明显异常的复核员，进行校准。

#### **三、 服务质量深度分析 (In-depth Service Quality Analysis)**

*   **对标参考:** 阿里云 - `服务质量分析`
*   **面向用户:** 质检主管、班组长
*   **核心目的:** 下钻到具体的质检规则维度，分析服务质量的短板，并定位到具体的团队和个人，为精准培训和辅导提供依据。
*   **统计维度 (Filters):**
    *   `时间范围`
    *   `质检方案`
    *   `班组/团队`
    *   `坐席`
*   **报表内容:**
    *   **规则表现总览 (Rule Performance Overview):**
        *   以表格形式列出所有质检规则，每条规则包含以下指标：`规则名称`、`平均得分率` (`规则得分/规则总分`)、`触发次数`、`申诉次数`、`申诉成功率`。
    *   **规则下钻分析 (Rule Drill-Down):**
        *   点击任一规则，可下钻进入该规则的专题分析页面：
            *   `规则得分趋势图`: 按时间展示该规则平均得分率的变化。
            *   `团队表现排名`: 在该规则上得分最低的团队排名。
            *   `坐席表现排名`: 在该规则上得分最低的坐席排名。
            *   `典型问题录音`: 展示在该规则上失分最严重的通话案例。
    *   **坐席/团队横向对比:**
        *   `成员表现分布图`: 用箱线图或得分分段（如0-60, 60-80, 80-100）直方图，展示团队内成员的分数分布，快速识别高绩效和低绩效坐席。

#### **四、 坐席申诉洞察报告 (Agent Appeal Insight Report)**

*   **对标参考:** 阿里云 - `申诉统计`
*   **面向用户:** 质检主管
*   **核心目的:** 监控申诉情况，保障质检公平性，并从申诉数据中反向挖掘有争议的AI规则或存在标准差异的复核环节。
*   **统计维度 (Filters):**
    *   `时间范围`
    *   `班组/团队`
    *   `坐席`
    *   `申诉处理人` (复核员)
*   **报表内容:**
    *   **申诉概览 (Appeal Overview):**
        *   `总申诉数`、`申诉率`、`申诉成功率`。
    *   **申诉趋势分析 (Appeal Trend):**
        *   按周/月展示 `申诉数量` 和 `申诉成功率` 的变化趋势。
    *   **问题定位分析 (Problem-finding Analysis):**
        *   `高申诉成功率规则排行`: 申诉成功次数最多的质检规则排名。**这直接指向了AI最不准确或定义最模糊的规则**。
        *   `高申诉率团队/坐席排行`: 申诉次数和申诉率最高的团队/坐席，可能反映了团队文化或对质检结果的接受度。

---

这四个报表相互关联，层层递进，可以从宏观到微观，从AI到人工，全面地反映您智能质检系统的运行状况，并为持续优化提供数据驱动力。