### **1. 质检综合运营报表 (全局驾驶舱) - 详细设计文档**

---

#### **1.1 报表概述**

*   **报表ID:** RPT_01
*   **报表名称:** 质检综合运营报表 (全局驾驶舱)
*   **核心目的:** 为质检主管和呼叫中心管理层提供一个宏观、全面的数据视图，用以监控整体质检工作的健康度、发现全局性问题与趋势，并快速定位潜在风险。
*   **核心价值:** 实现"一图看全貌"，将最重要的数据指标集中呈现，帮助管理者从日常琐碎的质检任务中抽离出来，聚焦于运营效率、服务质量和关键问题，从而进行更高效的数据驱动决策。

---

#### **1.2 界面布局设计 (UI Layout)**

本报表推荐采用经典的 **"顶部筛选器 + 网格化布局"** 的驾驶舱设计。

*   **A区 (顶部): 筛选器区域 (Filters Area)**
    *   横向贯穿整个报表页面顶部，提供全局的数据筛选能力。
*   **B区 (中上): 核心指标卡区域 (KPI Cards Area)**
    *   紧随筛选器下方，以醒目的卡片形式展示最重要的宏观指标。
*   **C区 (中下): 核心图表区域 (Main Charts Area)**
    *   占据页面的核心位置，以图表形式展示多维度分析和趋势洞察。此区域可进一步划分为2-4个网格，容纳不同的图表。
*   **D区 (底部): 问题排行榜区域 (Top Lists Area)**
    *   位于页面底部，以列表或排行榜的形式，直接揭示最突出的问题点。

```
+-------------------------------------------------------------+
| A: [时间范围选择]  [班组选择]  [质检方案选择]  [查询] [重置]  |
+-------------------------------------------------------------+
| B: [ KPI 卡片 1 ] [ KPI 卡片 2 ] [ KPI 卡片 3 ]               |
|    [ KPI 卡片 4 ] [ KPI 卡片 5 ] [ KPI 卡片 6 ]               |
+-------------------------------------------------------------+
| C: [ 趋势分析图 1 ]             [ 趋势分析图 2 ]            |
|                                                             |
|    [ 多维度对比图 1 ]            [ 多维度对比图 2 ]            |
+-------------------------------------------------------------+
| D: [ 问题排行榜 1 ]             [ 问题排行榜 2 ]            |
+-------------------------------------------------------------+
```

---

#### **1.3 组件详细说明 (Component Details)**

##### **A. 筛选器区域 (Filters Area)**

*   **设计原则:** 筛选器区域应始终保持简洁直观，默认展示最常用的筛选条件，并提供清晰的操作按钮。所有筛选条件的变更，都需要用户主动点击【查询】按钮后才会生效，以避免不必要的重复数据加载。

*   **组件1: 时间范围选择器**
    *   **UI控件:** `DateRangePicker`
    *   **功能描述:**
        *   提供多个预设的快速选项，如："最近7天", "最近30天", "本月", "上月"。
        *   **必须支持自定义时间范围选择**，允许用户手动选择任意的开始和结束日期。
    *   **默认值:** "最近30天"。

*   **组件2: 班组选择器**
    *   **UI控件:** `Select` (支持多选和搜索功能)。
    *   **功能描述:**
        *   下拉列表中展示所有可选班组。
        *   支持多选，用户可以选择一个或多个班组进行分析。
        *   当班组数量较多时，应支持在下拉框内进行搜索，以快速定位。
    *   **数据源:** 系统内的班组列表。
    *   **默认值:** "全部班组"。

*   **组件3: 质检方案选择器**
    *   **UI控件:** `Select` (支持多选和搜索功能)。
    *   **功能描述:**
        *   下拉列表中展示所有已启用的质检方案。
        *   支持多选。
        *   同样应支持搜索功能。
    *   **数据源:** 系统内的质检方案列表。
    *   **默认值:** "全部方案"。

*   **组件4: 操作按钮区**
    *   **UI控件:** `Button` 组
    *   **按钮1: 【查询】**
        *   **类型:** 主按钮 (Primary)
        *   **交互:** 点击后，根据当前所有筛选器的选定值，刷新整个报表页面的数据。
    *   **按钮2: 【重置】**
        *   **类型:** 次按钮 (Secondary/Default)
        *   **交互:** 点击后，将所有筛选器恢复到其默认值，并**不会**自动触发查询，需要用户再次点击【查询】。

##### **B. 核心指标卡区域 (KPI Cards Area)**

*   **设计要求:** 每个卡片都应包含主指标、指标名称，并附带一个与上一周期的对比百分比（例如："较上周 ‎+5%"）。

1.  **`总质检量`**
    *   **计算逻辑:** AI质检量 + 人工复核量
2.  **`整体复核率`**
    *   **计算逻辑:** (人工复核量 / AI质检量) * 100%
3.  **`质检覆盖率`**
    *   **计算逻辑:** (AI质检录音数 / 任务范围内总录音数) * 100%
4.  **`整体平均分`**
    *   **计算逻辑:** 所有已出分质检记录的总分 / 总质检记录数
5.  **`整体合格率`**
    *   **计算逻辑:** (质检分数达到合格标准的记录数 / 总质检记录数) * 100%
6.  **`总申诉率`**
    *   **计算逻辑:** (发起申诉的记录数 / 总质检记录数) * 100%

##### **C. 核心图表区域 (Main Charts Area)**

*   **图表1: `质检分数/合格率趋势图`**
    *   **图表类型:** 双Y轴组合图 (Dual-axis Combo Chart)
    *   **X轴:** 时间（根据时间范围筛选器动态聚合，如按"日"或"周"）
    *   **左Y轴 (折线):** `整体平均分`
    *   **右Y轴 (折线):** `整体合格率`
    *   **交互:** 鼠标悬浮(Hover)在数据点上时，显示该时间点的具体数值。

*   **图表2: `质检量趋势图`**
    *   **图表类型:** 堆叠柱状图 (Stacked Column Chart)
    *   **X轴:** 时间
    *   **Y轴:** 质检数量
    *   **堆叠项:** `AI质检量`, `人工复核量` (用不同颜色区分)
    *   **交互:** 鼠标悬浮(Hover)在柱子上时，显示AI、人工及总量的具体数值。

*   **图表3: `按班组对比`**
    *   **图表类型:** 分组条形图 (Grouped Bar Chart)
    *   **Y轴:** 班组名称
    *   **X轴:** 数值
    *   **分组指标:** `平均分`, `合格率`, `申诉率` (用不同颜色区分)
    *   **交互:** 点击某个班组的条形，可以实现下钻（Drill-Down），跳转至该班组的团队绩效报表。

*   **图表4: `按质检方案对比`**
    *   **图表类型:** 分组条形图 (Grouped Bar Chart)
    *   **Y轴:** 质检方案名称
    *   **X轴:** 数值
    *   **分组指标:** `平均分`, `合格率` (用不同颜色区分)
    *   **交互:** 点击某个方案的条形，可以进一步筛选页面内的其他图表（联动筛选）。

##### **D. 问题排行榜区域 (Top Lists Area)**

*   **排行榜1: `高频失分项 TOP 10`**
    *   **展现形式:** 水平条形图 (Horizontal Bar Chart) 或有序列表。
    *   **左侧:** 具体的失分项名称（如："未完整复述客户问题"）
    *   **右侧 (条形长度):** 该项被扣分的总次数
    *   **交互:** 点击任一失分项，可弹出窗口展示导致该项失分的典型通话录音列表。

*   **排行榜2: `重大违规项分布`**
    *   **展现形式:** 环形图 (Donut Chart)。
    *   **分块:** 不同的严重违规项类型（如："辱骂客户"、"违规承诺"、"信息泄露"等）。
    *   **数值:** 相应违规项的发生次数。
    *   **交互:** 鼠标悬浮(Hover)在分块上，显示该项的具体名称、次数和占比。

---

#### **1.4 数据刷新机制 (Data Refresh)**

*   **刷新策略:** T+1。
*   **说明:** 考虑到数据统计的复杂性和对系统性能的影响，本报表数据每日凌晨进行一次批量计算和更新，用户查看时无需实时查询，保证报表加载速度。

---
#### **1.5 附注 (Notes)**

*   所有图表和指标的计算都应严格遵守顶部筛选器所设定的范围。
*   "上一周期"的定义应与时间范围选择器挂钩。例如，若选择"最近7天"，则上一周期为"上上个7天"；若选择"本月"，则上一周期为"上月"。
*   在页面首次加载时，应有明确的加载中（Loading）状态提示。 