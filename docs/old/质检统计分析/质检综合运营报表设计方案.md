# 质检综合运营报表设计方案

## 1. 报表定位与目标用户

-   **目标用户**: **质检主管 (QA Supervisor)**
-   **核心目标**:
    1.  **全局监控**: 提供一个360度的视角，全面了解质检体系的运营健康度。
    2.  **效率评估**: 量化质检团队及AI模型的运作效率与准确性。
    3.  **质量洞察**: 快速发现服务质量的短板、亮点与趋势。
    4.  **决策支持**: 为优化质检策略、调整模型、管理团队提供数据依据。
    5.  **向上汇报**: 形成向管理层汇报工作的核心数据材料。

---

## 2. 页面布局与核心模块

报表页面将采用自上而下的信息架构，从宏观概览到细节分析，分为四个核心区域。

```
+------------------------------------------------------+
| 1. 全局筛选区 与 核心数据总览                        |
+------------------------------------------------------+
| 2. 核心KPI指标卡片区 (4-6个最关键指标)               |
+------------------------------------------------------+
| 3. 图表分析区 (多个图表，可切换Tab)                   |
|    [ 服务质量分析 | AI效能分析 | 运营效率分析 ]       |
+------------------------------------------------------+
| 4. 业务与风险洞察区 (列表形式)                       |
+------------------------------------------------------+
```

---

## 3. 模块详细设计

### 3.1 全局筛选区 与 核心数据总览

此区域位于报表最顶部，提供全局的数据筛选能力和最重要的数据总览。

**布局:**
-   **第一行**: `[时间范围选择器]`
-   **第二行**: 在选定筛选条件后，此处展示本次查询范围内的核心量级指标。
    -   **总质检量**: `1,234,567` (示例数值)
    -   **总复核量**: `56,789`
    -   **总申诉量**: `1,234`
    -   **重度违规会话数**: `50`

### 3.2 核心KPI指标卡片区

此区域提供对整体质检绩效的高度概括性视图，通常包含4-6个最重要的比率或平均值指标。

#### 3.2.1 指标详解

1.  **总质检覆盖率 (%)**
    -   **来源KPI**: `质检运营效率.1`
    -   **业务价值**: 衡量质检工作的广度是否达标。
    -   **计算公式**: `(被质检的会话总数 / 系统接收的应质检总会话数) * 100`
    -   **计算示例**: 某月，呼叫中心共产生10000通录音，都推送给了质检系统。但由于质检任务只设定了对"退货咨询"相关的会话进行质检，最终系统分析了其中的2500通。则总质检覆盖率为 `(2500 / 10000) * 100 = 25%`。
2.  **机审-人复结果一致率 (%)**
    -   **来源KPI**: `AI模型效能.1` (也称复核准确率)
    -   **业务价值**: AI模型判断有多准？这是评估AI效能的最核心指标。
    -   **计算公式**: `(人工复核后未做任何修改的会话数 / 总人工复核会话数) * 100`
    -   **计算示例**: 复核员张三完成了对100通AI已评分会话的复核。其中，有85通会话他认为AI打分完全准确，未做任何修改就提交了。另外15通他修改了分数或标签。则这批复核任务的机审-人复结果一致率为 `(85 / 100) * 100 = 85%`。
3.  **平均质检得分 (分)**
    -   **来源KPI**: `服务质量结果.1`
    -   **业务价值**: 当前整体的服务质量水平如何？
    -   **计算公式**: `SUM(所有被质检会话的最终得分) / COUNT(所有被质检会话)`
    -   **计算示例**: 在筛选的时间范围内，系统共质检了3个会话。会话A的最终得分是90，会话B是75，会话C是100。则平均质检得分为 `(90 + 75 + 100) / 3 = 88.33`分。
4.  **重度违规率 (%)**
    -   **来源KPI**: `服务质量结果.3`
    -   **业务价值**: 发生了多少严重的、不可接受的服务失误？用于风险监控。
    -   **计算公式**: `(最终结果包含至少一条"重度违规"的会话数 / 被质检的会话总数) * 100`
    -   **计算示例**: 某周，系统质检了500个会话。分析发现，其中有10个会话触碰了"辱骂客户"或"泄露隐私"等重度违规质检项（即使某个会话触碰了多条，也只算一个违规会话）。则重度违规率为 `(10 / 500) * 100 = 2%`。

### 3.3 图表分析区

通过多组图表，对核心问题进行下钻分析。采用Tab页切换，保持界面整洁。

#### Tab 1: 服务质量分析

-   **图表一: 服务质量得分趋势图 (线图)**
    -   **X轴**: 时间 (按天/周)
    -   **Y轴**: 平均质检得分
    -   **来源KPI**: `服务质量结果.1`
    -   **说明**: 展示在选定时间范围内，整体服务质量的变化趋势。
-   **图表二: TOP 5 违规规则排行榜 (横向柱状图)**
    -   **X轴**: 违规会话数 或 违规坐席数 (可切换)
    -   **Y轴**: 规则名称
    -   **来源KPI**: `业务与客户洞察.1`
    -   **说明**: 定位当前最普遍的服务短板是哪些。

#### Tab 2: AI效能分析

-   **图表一: 机检与人复后命中率对比 (分组柱状图)**
    -   **X轴**: 规则名称 (例如，取违规TOP 5的规则)
    -   **Y轴**: 命中率 (%)
    -   **数据系列**: [系列1: 机检命中率, 系列2: 人复后命中率]
    -   **来源KPI**: `AI模型效能.4`, `AI模型效能.5`
    -   **说明**: 直观对比AI的判断与人工的最终判断之间的差异，用于评估具体规则的AI准确性。
-   **图表二: AI质检与人工质检量趋势 (堆叠面积图)**
    -   **X轴**: 时间
    -   **Y轴**: 质检通话量
    -   **数据系列**: [系列1: AI质检量, 系列2: 人工质检量]
    -   **来源KPI**: `质检运营效率.3`, `质检运营效率.4`
    -   **说明**: 展示AI在质检工作中承担的工作量比例及其变化。

#### Tab 3: 运营效率分析

-   **图表一: 申诉情况分析 (环图)**
    -   **数据**: [申诉成功数, 申诉驳回数, 待处理申诉数]
    -   **中央**: 显示总申诉率 (`质检运营效率.9`)
    -   **来源KPI**: `质检运营效率.10` 等申诉相关指标。
    -   **说明**: 快速了解申诉处理的概况。
-   **图表二: 质检员复核准确率排名 (列表)**
    -   **列**: 排名、质检员名称、复核任务数、复核准确率(%)
    -   **来源KPI**: `AI模型效能.1` (按质检员下钻)
    -   **说明**: 用于评估质检团队内部的工作质量。

### 3.4 业务与风险洞察区

将非结构化和半结构化的分析结果以列表形式呈现，体现质检的业务价值。

-   **模块一: 客户热点话题**
    -   **形式**: 词云图或TOP 10列表。
    -   **来源KPI**: `业务与客户洞察.2`
    -   **说明**: 客户最近在关心什么？
-   **模块二: 新兴问题趋势**
    -   **形式**: 列表，包含问题描述、首次出现时间、涉及通话量等。
    -   **来源KPI**: `业务与客户洞察.8`
    -   **说明**: 提前预警新的业务风险点或产品问题。 