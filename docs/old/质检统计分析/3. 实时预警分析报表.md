### **3. 实时预警分析报表 - 详细设计文档**

---

#### **3.1 报表概述**

*   **报表ID:** RPT_03
*   **报表名称:** 实时预警分析报表
*   **面向用户:** 质检主管、现场管理人员、班组长
*   **核心目的:**
    1.  分析实时风险事件的发生规律与分布，识别高风险时段、坐席与业务场景。
    2.  评估预警规则的有效性与预警事件的处理效率。
    3.  优化事中干预策略，将管理资源向风险最高点倾斜。
*   **核心价值:** 帮助呼叫中心从"事后追溯"转向"事中干预"，主动规避业务风险、降低客户投诉、提升服务体验，并为现场管理者提供有效的实时管理抓手。

---

#### **3.2 界面布局设计 (UI Layout)**

采用与前序报表一致的四区域布局，确保用户体验的连贯性。

*   **A区 (顶部): 筛选器区域 (Filters Area)**
    *   提供多维度、精细化的数据筛选能力。
*   **B区 (中上): 核心指标卡区域 (KPI Cards Area)**
    *   直观展示预警事件的整体概况和处理效率。
*   **C区 (中部): 分析图表区域 (Analysis Charts Area)**
    *   分为 **"趋势与分布"** 和 **"热点与效能"** 两个子区域，深入洞察预警规律。
*   **D区 (底部): 预警事件明细列表 (Alert Details List)**
    *   提供所有预警事件的详细清单，支持钻取分析。

```
+----------------------------------------------------------------------------------+
| A: [时间范围] [预警规则] [预警等级] [处理状态] [班组] [坐席] [查询]...              |
+----------------------------------------------------------------------------------+
| B: [ 总预警次数 ] [ 预警处理及时率 ] [ 预警有效率 ] [ 危急预警占比 ]             |
+----------------------------------------------------------------------------------+
| C: [ 预警时段分布 (热力图) ]                 [ 预警类型趋势 (面积图) ]           |
|                                                                                  |
|    [ 预警高发坐席 TOP 10 (条形图) ]          [ 预警规则效能分析 (条形图) ]         |
+----------------------------------------------------------------------------------+
| D: [ 预警事件明细列表 (可滚动、可排序) ]                                         |
+----------------------------------------------------------------------------------+
```

---

#### **3.3 组件详细说明 (Component Details)**

##### **A. 筛选器区域 (Filters Area)**

*   **设计原则:** 实时报表的特殊性要求部分组件（如时间）可触发自动刷新。

*   **组件1: 时间范围选择器**
    *   **UI控件:** `DateRangePicker`，需包含时间选择。
    *   **功能描述:** 支持预设选项 ("今天", "最近1小时", "最近6小时") 和自定义时间范围。
    *   **默认值:** "今天"。

*   **组件2: 预警规则选择器**
    *   **UI控件:** `Select` (支持多选和搜索)。
    *   **数据源:** 系统内配置的所有实时预警规则。
    *   **默认值:** "全部规则"。

*   **组件3: 预警等级选择器**
    *   **UI控件:** `Select` (支持多选和搜索)。
    *   **数据源:** `危急`、`高`、`中`、`低`。
    *   **默认值:** "全部等级"。

*   **组件4: 处理状态选择器**
    *   **UI控件:** `Select` (支持多选和搜索)。
    *   **数据源:** `待处理`、`已处理`、`误报`。
    *   **默认值:** "全部状态"。

*   **组件5 & 6: 班组与坐席选择器**
    *   **UI控件:** `Select` (级联或独立，均支持多选和搜索)。
    *   **默认值:** "全部"。

*   **组件7: 操作按钮区**
    *   **UI控件:** `Button` 组，包含【查询】、【重置】和一个带自动刷新开关的【刷新】按钮。

##### **B. 核心指标卡区域 (KPI Cards Area)**

1.  **`总预警次数`**
    *   **计算逻辑:** 在筛选条件下，触发的预警总数。
    *   **辅助信息:** 与上一周期对比的环比变化率。
2.  **`预警处理及时率`**
    *   **计算逻辑:** `(在规定SLA内处理的预警数 / 已处理的总预警数) * 100%`。
    *   **解读:** 衡量团队对风险事件的响应速度。
3.  **`预警有效率`**
    *   **计算逻辑:** `((总预警数 - 误报数) / 总预警数) * 100%`。
    *   **解读:** 衡量预警规则的准确性。
4.  **`危急预警占比`**
    *   **计算逻辑:** `(危急等级的预警数 / 总预警数) * 100%`。
    *   **解读:** 直接反映最严重风险事件的比例。

##### **C. 分析图表区域 (Analysis Charts Area)**

**子区域1: 趋势与分布**

*   **图表1: `预警时段分布`**
    *   **图表类型:** 热力图 (Heatmap)。
    *   **X轴:** 一天中的小时 (0-23时)。
    *   **Y轴:** 日期或星期。
    *   **颜色:** 从冷色到暖色（如蓝到红）代表该时段内预警数量的多少。
    *   **解读:** 快速定位一天中的"魔鬼时刻"，便于安排管理资源。

*   **图表2: `预警类型趋势`**
    *   **图表类型:** 堆叠面积图 (Stacked Area Chart)。
    *   **X轴:** 时间（按小时或天聚合）。
    *   **Y轴:** 预警数量。
    *   **维度:** 按预警规则类型（如：客户投诉、坐席违规、情绪激动等）堆叠。
    *   **解读:** 展示各类风险事件在一段时间内的消长变化。

**子区域2: 热点与效能**

*   **图表3: `预警高发坐席/团队 TOP 10`**
    *   **图表类型:** 条形图 (Bar Chart)。
    *   **Y轴:** 坐席或团队名称。
    *   **X轴:** 预警总次数。可提供按`危急`、`高`等不同等级预警次数排序的选项。
    *   **解读:** 精准定位需要重点关注和辅导的个人或团队。

*   **图表4: `预警规则效能分析`**
    *   **图表类型:** 分组条形图 (Grouped Bar Chart)。
    *   **Y轴:** 预警规则名称。
    *   **X轴:** 次数/百分比。
    *   **分组指标:**
        *   `触发次数`: 该规则被触发的总次数。
        *   `有效率`: 该规则的有效率（`1 - 误报率`）。
    *   **解读:** 用于评估哪些规则最"活跃"且最"有效"，为规则的优化或启停提供数据依据。

##### **D. 预警事件明细列表 (Alert Details List)**

*   **展现形式:** 可滚动、可排序的高级表格。
*   **排序:** 默认按"预警时间"降序排列。
*   **列定义:**
    1.  `预警时间`: 格式 YYYY-MM-DD HH:mm:ss。
    2.  `通话ID`: 提供链接可下钻至通话详情页。
    3.  `坐席/班组`。
    4.  `预警规则`。
    5.  `预警等级`: 使用不同颜色的Tag标识（如危急-红色）。
    6.  `处理状态`: 使用Tag标识。
    7.  `处理人`。
    8.  `处理时长`: 从预警发生到处理完成的时间。
    9.  `操作`: [标记为误报], [创建复核任务], [查看详情]。

---

#### **3.4 数据刷新机制 (Data Refresh)**

*   **刷新策略:** 准实时 (Near Real-time)。
*   **说明:** 预警数据应在事件发生后 **1分钟** 内反映在报表中。页面应支持 **自动刷新** 机制（如每分钟刷新一次），并提供手动暂停开关。

---
#### **3.5 附注 (Notes)**

*   **公式定义:**
    *   `预警处理及时率`: SLA（服务等级协议）时间（如5分钟）需要在后台配置。
    *   `预警有效率`: "误报"状态由人工标记。
*   所有图表均应支持下钻分析。例如，点击热力图的某个高亮时段，下方D区列表应自动筛选出该时段的预警事件。点击坐席TOP 10中的某个坐席，列表同样联动筛选。
*   对于超过SLA仍未处理的预警，应在列表中进行高亮（如红色字体）提醒。
*   权限控制：普通坐席可能无法查看此报表，或仅能查看与自己相关的数据。 