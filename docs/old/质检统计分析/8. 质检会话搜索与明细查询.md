# 8. 质检会话搜索与明细查询

## 1. 功能目标

本功能旨在提供一个全局的、统一的质检记录查询入口，打破以"质检任务"为单位的数据壁垒。用户可以基于多种维度自由组合筛选条件，快速、精确地定位到系统内任何一次被质检过的会话（Session），并查看其详细报告。

此功能是连接质检执行与数据分析的关键枢纽，将系统从一个任务驱动的操作平台，提升为数据驱动的分析与洞察平台。

## 2. 核心设计

### 2.1. 页面定位

- **位置**: 顶级导航菜单，"统计报表"分类下。
- **名称**: "会话搜索" 或 "质检明细查询"。
- **目的**: 成为所有质检明细数据的中央查询中心。

### 2.2. 核心组件

#### 2.2.1. 强大的筛选器 (Filter Pane)

这是页面的核心，允许用户通过以下一个或多个条件的"与(AND)"逻辑组合进行搜索：

- **基础信息**:
    - `坐席ID/姓名`: 输入框，支持模糊搜索。
    - `通话唯一ID (Session ID)`: 精确匹配。
    - `客户信息`: (可选) 如客户电话号码。
    - `通话时间范围`: 日期范围选择器。

- **质检结果**:
    - `使用的质检方案`: 下拉选择器，列出所有可用方案。
    - `最终得分范围`: 例如，查询分数在 50 到 70 之间的记录。
    - `是否合格`: 单选按钮或下拉框（是 / 否 / 全部），基于方案的合格线判断。
    - `触发的具体规则`: 下拉选择器（支持多选），列出所有质检规则。
    - `有无人工批注`: 单选按钮（是 / 否 / 全部）。

- **来源信息**:
    - `来源任务ID`: 精确匹配。
    - `来源计划ID`: 精确匹配。
    - `数据来源方式`: 下拉选择器（API同步 / 目录监控 / 手动上传 / 全部）。

- **申诉状态**:
    - `申诉状态`: 下拉选择器（未申诉 / 申诉中 / 申诉成功 / 申诉驳回 / 全部）。

#### 2.2.2. 搜索结果列表 (Results List)

搜索结果以表格形式展示，支持分页和排序。

- **关键列**:
    - `通话唯一ID`
    - `通话时间`
    - `坐席姓名`
    - `质检方案`
    - `最终得分`
    - `是否合格` (用不同颜色标识，如绿色表示合格，红色表示不合格)
    - `申诉状态`
    - `来源任务/计划`
    - `操作`:
        - `查看详情`: 链接到该次会话的详细质检报告页面 (`SessionDetailPage.tsx`)。

## 3. 与现有功能的关系

- **数据统一**: 本页面将成为所有质检记录的唯一入口。
- **流程打通**: 原有的"任务详情页"中显示的质检记录列表，其"查看详情"按钮应统一跳转到与本页面相同的会话详情页 (`SessionDetailPage.tsx`)，确保用户体验和数据展现的一致性。

## 4. 功能价值

- **对质检主管**: 快速定位典型案例（高分/低分），进行专题分析（如某个规则的触发情况），或追踪处理客户投诉对应的通话。
- **对坐席/团队负责人**: 方便地查找自己或团队的历史通话记录和质检结果，用于自查、辅导和学习。
- **对系统架构**: 解决了因"任务"容器导致的数据孤岛问题，为后续所有高级统计报表（如高频失分项分析、坐席成长报告等）提供了坚实、灵活的数据基础。 