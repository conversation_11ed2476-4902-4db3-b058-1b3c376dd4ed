
---

### 页面十二：质检任务管理 (FinalTaskListPage.tsx)

#### 1. 核心定位与目标
该页面是**所有具体质检批次**的管理中心。其核心目标是让管理者能够创建、监控和追溯**每一次**质检任务的执行情况。这些任务既可以是由“质检计划”自动生成的，也可以是管理者根据临时需求手动创建的。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "质检任务管理"
*   **副标题**: "管理所有一次性的和由计划生成的质检任务。"
*   **图标**: `CheckSquare` (带对勾的方框)，象征着具体的、可执行的任务项。
*   **核心操作**: `新建任务` 按钮，点击后弹出创建任务的抽屉表单 (`FinalCreateTaskPage.tsx`)。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 在众多历史和当前任务中进行精确查找。
*   **筛选字段**:
    *   `任务名称`
    *   `来源`: 筛选“手动创建”的任务或“由计划生成”的任务。
    *   `质检方案`: 按所使用的方案筛选。
    *   `创建人`
    *   `创建时间`

**c. Tab切换**
*   **功能**: 根据任务的**执行状态**对列表进行分类，方便监控和管理。
*   **分类**:
    *   `全部`
    *   `待执行`: 已创建但尚未开始执行的任务（通常是定时任务）。
    *   `执行中`: 正在进行质检处理的任务。
    *   `已完成`: 所有通话记录都已处理完毕的任务。
    *   `执行失败`: 因数据源、引擎等问题导致执行中断或失败的任务。

**d. 任务列表表格 (`Table`)**
*   **布局**: 以表格形式展示所有质检任务。
*   **表格列**:
    *   `序号`
    *   `任务名称`: 显示任务的名称。
    *   `任务状态`: 用不同颜色的徽章显示“待执行”、“执行中”、“已完成”、“执行失败”等状态。
    *   `任务进度`: **核心监控信息**。
        *   对于“执行中”的任务，会显示一个**进度条**，并附有具体数字（如 `8500 / 10000`），直观展示任务完成度。
        *   对于“已完成”的任务，进度条为100%绿色。
        *   对于“执行失败”的任务，会显示红色错误提示，并可悬浮查看详细错误信息（如“数据源连接超时”）。
    *   `质检方案`: 显示该任务所采用的评分方案。
    *   `来源`: 标明任务是“手动创建”还是“由计划生成”。
    *   `创建人`
    *   `创建时间`
    *   `操作`:
        *   **查看详情 (`Eye`图标)**: **关键链接**，点击后会跳转到 **[质检明细查询]** 页面 (`QualityDetailQueryPage` 或 `FinalTaskDetailPage`)，并自动筛选出该任务包含的所有通话的质检成绩明细。
        *   **编辑 (`Edit`图标)**: 仅对“待执行”状态的任务可见，允许修改任务配置。
        *   **删除 (`Trash2`图标)**: 仅对非“执行中”的任务可见，用于删除任务。

**e. 创建/编辑任务的抽屉表单 (`FinalCreateTaskPage.tsx`)**
这是定义一个一次性手动任务的核心界面。它与“创建计划”的表单非常相似，但关键区别在于时间范围。

*   **表单内容**:
    *   **第一部分：基本信息**: `任务名称` 和 `任务描述`。
    *   **第二部分：质检范围**:
        *   `数据来源`: 选择数据源。
        *   `通话时间范围`: **核心配置**。这里必须选择一个**固定的、绝对的时间范围**，例如 “从 2023-11-01 00:00:00 到 2023-11-10 23:59:59”。这是“任务”与“计划”（使用动态时间）的本质区别。
        *   `质检对象`: 与计划类似，可选择“全部坐席”或“按组织架构选择”。
    *   **第三部分：质检规则与模式**: 与计划完全相同，选择`质检方案`和`质检模式`（全量/抽样）。
    *   **第四部分：执行计划**:
        *   `执行方式`:
            *   **立即执行**: 保存后任务马上开始。
            *   **定时执行**: 设置一个未来的具体时间点来启动任务。

#### 3. 核心交互与操作
*   **手动与一次性**: 任务管理的核心是处理**一次性**的质检需求，与计划的周期性形成互补。这对于专项检查、事后复盘等场景非常重要。
*   **任务与明细的关联**: 通过“查看详情”按钮，将宏观的任务（一个批次）与微观的质检结果（每一通电话的成绩）关联起来，实现了从批次到个体的下钻查询。
*   **进度监控**: 进度条的设计让管理者可以实时监控长耗时任务的执行情况，对系统负载和任务完成时间有直观的预期。
*   **计划与任务的关系**: 在这个系统中，“计划”是“任务”的“母亲”。计划（Plan）负责按规则**生出**任务（Task），而任务管理页面则是所有这些**新生儿**（自动生成的和手动创建的）的“户口本”和“成长记录”，统一进行管理和查看。

---