
---

### 页面二十三：质检任务详情页 (FinalTaskDetailPage.tsx)

#### 1. 核心定位与目标
该页面是**单个质检任务**的执行结果汇总和明细列表。其核心目标是为管理者提供一个清晰的界面，来查看某一个特定任务（无论是手动创建还由计划生成）的**整体统计数据**和其包含的**每一条通话的质检结果**。它是连接“宏观任务”与“微观记录”的关键桥梁。

#### 2. 主要功能模块与内容

**a. 页面头部 (自定义，非`UnifiedPageHeader`)**
*   **返回按钮 (`ArrowLeft`)**: 提供一个清晰的返回入口，通常返回到 **[质检任务管理]** 列表页。
*   **任务标题**: 醒目地显示当前任务的名称，例如“11月营销活动通话质检”。
*   **任务ID**: 显示任务的唯一标识符。

**b. 任务信息总览卡片**
*   **功能**: 概览当前任务的核心配置信息，让用户了解这个任务的“前因”。
*   **内容**:
    *   `质检方案`: 使用的评分方案。
    *   `质检范围`: 如“坐席组A、B、C”。
    *   `通话时段`: 本次任务处理的通话记录的时间范围。
    *   `创建人` 和 `创建时间`
    *   `完成时间`

**c. 任务统计数据卡片 (`Statistics Cards`)**
*   **功能**: 以KPI卡片的形式，快速展示该任务的**核心产出结果**。
*   **指标内容**:
    *   `任务记录总数`: 该任务总共处理了多少条通话记录。
    *   `任务平均分`: 该任务下所有记录的最终平均分。
    *   `合格率`: 该任务的整体合格率。
    *   `进入人工复核`: 该任务中有多少条记录触发了人工复核流程。

**d. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 在当前任务的**结果内部**进行二次筛选和查询。
*   **筛选字段**:
    *   **与全局查询页类似，但范围限定在本任务内**。例如：`记录编号`、`坐席`、`客户号码`、`最终得分范围`、`质检结果`、`申诉状态`等。

**e. 质检明细表格 (`Table`)**
这是页面的核心，展示了该任务包含的所有通话记录的详细质检结果。

*   **表格列**:
    *   `序号`
    *   `记录编号`: 可点击，跳转到该通话的 **[多模式会话详情页]**。
    *   `坐席`
    *   `所属班组`
    *   `客户号码`
    *   `通话开始时间`
    *   `通话时长`
    *   `最终得分`: **核心指标**，通过Tooltip展示分数演进过程（AI -> 复核 -> 申诉）。
    *   `质检结果`: “合格”/“不合格”徽章。
    *   `申诉信息`: 复合信息列，展示申诉状态、结果和时间。
    *   `操作`: 提供一个**查看详情 (`Eye`图标)**的按钮，功能同点击记录编号。

*   **表格特点**:
    *   **聚焦任务**: 表格内所有数据都属于当前这一个任务批次，上下文非常清晰。
    *   **交互性**: 提供了直接下钻到最微观的会话详情页的入口。

**f. 分页组件 (`UnifiedPagination`)**
*   用于浏览任务内的大量明细记录。

#### 3. 核心交互与操作
*   **从宏观到微观**: 用户的体验是从 **[质检任务管理]** 页面的一个宏观任务项，点击进入本页面，看到了该任务的**中观统计数据**（顶部的KPI卡片），然后可以进一步在表格中下钻到任意一条**微观记录**的详情页。这个逐层下钻的路径非常清晰。
*   **任务成果评估**: 该页面是对单个质检任务成败得失进行复盘的核心依据。管理者可以快速看到这个任务的整体效果（平均分、合格率），并能方便地查看其中任何一条记录的具体情况。
*   - **问题定位**: 通过在任务内部进行筛选，可以快速定位特定问题。例如，在一个“新人考核”任务中，可以筛选出所有“不合格”的记录，集中进行分析和辅导。

---