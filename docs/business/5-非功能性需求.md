# 第五章：非功能性需求

本章节旨在定义系统在**性能、安全、可用性**等方面的质量属性要求。这些非功能性需求是确保产品能够稳定、高效、安全地运行，并提供良好用户体验的基础。

## 5.1. 性能需求

性能是用户对产品最直观的感受之一。优秀的性能表现能带来流畅的用户体验，而性能不佳则会直接导致用户流失和工作效率下降。本系统需满足以下性能要求：

### 页面加载与响应时间
* **核心操作页面**：所有核心操作页面，如各角色首页、任务列表页、详情页等，在正常网络条件下，首次加载时间应不超过3秒，后续访问应不超过1.5秒。
* **复杂报表页面**：对于数据密集型的分析报表页面，在默认筛选条件下，页面加载和图表渲染完成时间应不超过5秒。
* **用户操作响应**：所有用户交互操作，如点击按钮、切换Tab、打开抽屉/模态框等，界面响应应在200毫秒内，用户应能感受到即时反馈。
* **列表与筛选响应**：对于列表页的筛选、排序、分页操作，结果刷新时间应不超过2秒。

### 数据处理能力
* **语音转文本处理**：系统应能支持每日处理至少10,000通平均时长为5分钟的通话录音。单通录音的STT转换处理时间应与录音时长大致相当。
* **AI质检处理**：对于已完成STT的通话文本，AI自动质检的处理速度应满足：单条通话记录的处理时间不超过10秒。
* **实时预警延迟**：从通话中出现预警关键词到管理者在"实时预警中心"看到警报，端到端的延迟时间应不超过30秒。

### 并发用户数
* **系统应能支持至少500个用户同时在线进行常规操作。**
* **在高峰时段，系统应能稳定支持100个用户并发进行数据密集型操作。**

### 资源占用
* **前端资源**：前端应用应进行合理优化，避免过高的浏览器内存和CPU占用，防止页面卡顿或崩溃。
* **后端资源**：后端服务应具备水平扩展能力，可以通过增加服务器节点来线性提升数据处理能力，以应对未来业务量的增长。数据库查询应建立合理的索引，避免全表扫描等低效操作。

### 压力与负载测试要求
* **在产品正式上线前，必须进行压力测试，模拟上述并发用户数和数据处理量的场景，持续运行至少24小时，确保系统在高负载下CPU、内存、IO等关键指标稳定，无内存泄漏，且响应时间满足要求。**
* **应针对核心API接口进行专项的基准测试，明确其吞吐量和响应时间的基线。**

## 5.2. 安全性需求

安全性是企业级应用的生命线，尤其本系统会接触到包含客户隐私的通话数据，因此必须建立全面的安全防护体系。

### 数据安全
* **数据传输安全**：所有客户端与服务器之间的数据传输必须使用HTTPS/TLS协议进行加密，防止数据在传输过程中被窃听或篡改。
* **数据存储安全**：
  * 所有敏感信息，如用户密码、API密钥、数据库连接密码等，在数据库中必须经过强加密后存储，严禁明文存储。
  * 通话录音文件和客户个人身份信息在存储时应有额外的访问控制和加密保护措施。
* **数据脱敏**：在非必要场景中，所有敏感数据必须进行脱敏处理。

### 访问控制
* **身份认证**：用户登录必须采用安全的认证机制，密码输入时应有防暴力破解策略。
* **权限管理**：系统必须具备严格的、基于角色的访问控制。不同角色的用户只能访问和操作其权限范围内的功能和数据。例如，坐席只能看到自己的数据，班组长只能看到本团队的数据。
* **会话管理**：用户会话应有合理的过期时间，并提供安全退出功能。

### 应用安全
* **输入验证**：所有来自用户的输入都必须在服务器端进行严格的校验，以防止常见的Web攻击，如SQL注入、跨站脚本攻击、命令注入等。
* **依赖库安全**：定期扫描项目使用的所有第三方依赖库，及时修复已知的安全漏洞。
* **操作审计**：对所有关键的、敏感的操作都应记录详细的审计日志，以便于事后追溯和排查。

## 5.3. 可用性需求

可用性衡量系统能够正常提供服务的程度，是系统稳定可靠的直接体现。

* **系统可用性**：核心系统服务的年度可用性目标应达到99.9%。这意味着全年的非计划停机时间不得超过8.76小时。
* **故障恢复**：
  * 系统应具备有效的监控和告警机制，能够在关键服务出现故障时，第一时间通知运维人员。
  * 应制定灾难恢复计划，定期进行数据备份。在发生严重故障时，能够从备份中快速恢复数据和服务，RTO（恢复时间目标）应小于4小时，RPO（恢复点目标）应小于24小时。
* **用户体验友好**：
  * 系统应提供清晰、有效的错误提示。当用户操作失败或系统出错时，应给出用户能理解的错误信息和下一步操作建议，而非模糊的技术错误码。
  * 对于耗时较长的操作，应提供明确的进度指示和后台任务完成后的通知机制。

## 5.4. 可扩展性需求

可扩展性决定了系统应对未来业务增长和技术演进的能力。

* **业务扩展性**：系统架构应设计为模块化，便于未来增加新的功能模块或扩展现有功能，而不影响现有核心逻辑。
* **技术架构扩展性**：后端服务应采用微服务架构或面向服务的架构，支持关键组件的独立部署和水平扩展。
* **配置驱动**：系统的业务逻辑应高度可配置，允许用户通过界面操作来适应业务变化，而非依赖代码修改。

## 5.5. 兼容性需求

* **浏览器兼容性**：前端应用应兼容主流现代浏览器的最新两个版本，包括 Google Chrome, Mozilla Firefox, Microsoft Edge。暂不要求兼容IE浏览器。
* **移动端兼容性**：当前版本主要面向PC端使用，但关键的查看页面应在移动端浏览器上有基本的响应式布局，保证可读性，无需专门的移动端App。
* **数据格式兼容性**：数据导入导出功能应支持通用的数据格式，如CSV, JSON，以方便与Excel等外部工具进行数据交换。 