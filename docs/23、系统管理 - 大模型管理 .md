
---

### 页面二十一：大模型管理 (FinalModelManagementPage.tsx)

#### 1. 核心定位与目标
该页面是系统**高级认知能力**的配置中心。其核心目标是让管理员能够统一管理和配置用于**大语言模型 (LLM)** 的服务。这些LLM主要用于更智能、更复杂的任务，例如：
*   **自然语言创编规则** (`FinalCreateRuleProForm`)
*   开放式的问题判断（如“坐席是否有效安抚了客户情绪？”）
*   对话总结、意图识别等

该页面与“语音识别引擎管理”页面的区别在于，它专注于处理**文本理解和生成**的LLM，而后者专注于**语音转文本 (STT)**。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "大模型管理"
*   **副标题**: "配置和管理云端及本地大语言模型"
*   **图标**: `Cpu` (CPU图标，常用于代表计算或智能核心)。
*   **核心操作**: `添加模型` 按钮，点击后弹出添加模型的抽屉表单。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **目的**: 在多个模型配置中进行快速查找。
*   **筛选字段**: `模型名称`, `模型类型`, `服务商`, `状态`。

**c. 模型列表表格 (`Table`)**
*   **布局**: 以表格形式展示所有已配置的LLM服务。
*   **表格列**:
    *   `序号`
    *   `模型名称`: 用户自定义的名称，下方是该模型的官方标识（如 `deepseek-chat`, `llama3:8b`）。
    *   `类型`: 用徽章显示“云端”或“本地”。
    *   `服务商`: 如 DeepSeek, 火山引擎, 阿里云, Ollama等。
    *   `API Key`: **安全显示**。默认显示为部分打码的字符串（如 `sk-********`），旁边有一个“眼睛”图标，点击后才显示完整密钥，防止信息泄露。
    *   `状态`: 用带图标的徽章显示“运行中”、“未启用”或“错误”。
    *   `最后使用`: 记录该模型最近一次被调用的时间。
    *   `操作`:
        *   **编辑 (`Edit`图标)**: 修改模型配置。
        *   **测试连接 (`Globe`图标)**: 验证API Key和端点是否可用。
        *   **启用/禁用 (`Power`/`PowerOff`图标)**: 快速切换模型是否在系统中可用。
        *   **删除 (`Trash2`图标)**: 删除该模型配置。

**d. 分页组件 (`UnifiedPagination`)**
*   用于浏览和导航模型列表。

**e. 添加/编辑模型的抽屉表单 (`Drawer`)**
*   **功能**: 提供一个结构化的界面来配置一个新的LLM服务。
*   **表单字段**:
    *   `模型名称`: 用户自定义的别名。
    *   `模型类型`: 选择“云端模型”或“本地模型”。
    *   `服务商`: 下拉选择主流的云服务商或本地部署方案（如Ollama）。
    *   `模型标识`: 填写服务商要求的具体模型ID。
    *   `API端点`: 模型的服务地址。
    *   `API密钥`: 访问服务的凭证。
    *   `描述`: 对该模型用途的说明。
    *   **高级参数**:
        *   `最大Token数`: 限制模型的输入输出长度。
        *   `温度参数 (Temperature)`: 控制模型输出的创造性/随机性。

#### 3. 核心交互与操作
*   **统一接入层**: 此页面为系统提供了一个统一的LLM接入层。无论后端模型来自哪个厂商或如何部署，上层应用（如规则引擎）都通过这个统一的配置来调用，实现了与具体模型的解耦。
*   **灵活性和可扩展性**: 同时支持**云端API**和**本地部署（通过Ollama）**，这为企业提供了极大的灵活性。对于追求数据私密性和低成本的企业，可以选择本地部署开源模型；对于追求高性能和便利性的企业，可以选择主流的云服务。系统可以轻松地在这两者之间切换或共存。
*   **安全与监控**: API Key的打码显示是必要的安全措施。状态监控和连接测试功能保证了管理员可以随时掌握所有AI服务的可用性，及时发现和处理问题。
*   **成本与性能控制**: 通过配置最大Token数和温度等参数，管理员可以在成本、响应速度和输出质量之间进行权衡和微调。

---