
---

### 页面十七：服务质量深度分析 (FinalServiceQualityDeepAnalysisPage.tsx)

#### 1. 核心定位与目标
该页面是一个**专题分析报表**，其核心目标是让管理者能够**围绕任意一个具体的“质检规则”**进行深度下钻分析。它旨在回答“某个特定的服务质量问题（例如‘客户不满未道歉’）在我们的组织中表现如何？根源在哪里？”这类具体问题，为精准的培训和辅导提供直接依据。

#### 2. 主要功能模块与内容

**a. 统一页面头部 (`UnifiedPageHeader`)**
*   **标题**: "服务质量深度分析"
*   **副标题**: "下钻到具体的质检规则维度，分析服务质量的短板，并定位到具体的团队和个人..."
*   **图标**: `Target` (靶心图标)，象征着精准定位和深度分析。
*   **核心操作**: `刷新数据` 和 `导出报表`。

**b. 统一搜索筛选器 (`UnifiedSearchFilter`)**
*   **功能**: 与总览页面类似，用于定义分析的数据范围。
*   **筛选字段**: `时间范围`, `质检方案`, `班组/团队`, `坐席`。

**c. 规则表现总览表格 (`RulePerformanceTable`)**
*   **功能**: 这是页面的**起点和入口**。它以表格形式列出了在筛选范围内所有被触发过的质检规则，并展示了每条规则的综合表现。
*   **表格列**:
    *   `规则名称`: 附带其“严重程度”徽章。
    *   `规则类型`: 业务分类。
    *   `平均得分率`: **核心指标**，指所有与该规则相关的通话，在该规则项上的平均得分率（例如，满分-5分，实际扣-1分，则得分率80%）。它比简单的触发次数更能反映问题的严重程度。
    *   `触发次数`: 该规则被命中的总次数。
    *   `申诉次数`: 针对该规则的申诉数量。
    *   `申诉成功率`: 针对该规则的申诉成功比例。高成功率可能意味着规则本身或AI判断存在问题。
    *   `操作`: 提供一个 **“下钻分析”** 的链接/按钮。

*   **交互**:
    *   用户首先在此表格中**选择一个他感兴趣或表现异常的规则**（例如，平均得分率最低的规则），然后点击“下钻分析”。
    *   点击后，该行会高亮，并且下方会**动态展开**一个详细的分析区域 (`RuleDrillDownAnalysis`)。

**d. 规则下钻分析区域 (`RuleDrillDownAnalysis` - 动态展开)**
这是页面的核心内容，只有在用户点击了某条规则后才会出现。它包含针对**所选定规则**的深入分析图表和列表。

*   **趋势分析 (左右双图表布局)**:
    *   **左侧图表 - 规则得分趋势 (`LineChart`)**: 展示在选定时间范围内，该规则的**每日/周平均得分率**的变化趋势。用于判断该问题是持续存在、正在改善还是正在恶化。
    *   **右侧图表 - 触发次数趋势 (`BarChart`)**: 展示该规则的**每日/周触发次数**的变化趋势。可以与得分率趋势结合分析，例如，触发次数下降但得分率没变，可能说明问题仍未解决，只是发生频率降低。

*   **表现排名 (左右双列表布局)**:
    *   **左侧列表 - 团队表现排名**: **按团队**对该规则的平均得分率进行排名，快速定位哪个团队在该问题上做得最好（可作为标杆），哪个团队最差（需要重点辅导）。
    *   **右侧列表 - 坐席表现排名**: **按坐席**对该规则的平均得分率进行排名，直接定位到具体的个人。

*   **典型问题录音 (`ProblemCase` 列表)**:
    *   **功能**: 系统会自动筛选出几条因为该规则而被严重扣分的**典型通话案例**。
    *   **内容**: 每条案例会显示记录ID、坐席、分数、通话信息，以及**问题描述**（例如，“客户在3分15秒表达不满，坐席未道歉”）。
    *   **交互**: 提供“播放录音”按钮，让管理者可以身临其境地感受问题发生的具体场景。

**e. 成员表现分布图 (`PerformanceDistributionChart`)**
*   **功能**: 这是一个独立的、辅助性的分析图表，通常位于页面底部。
*   **内容**: 以柱状图形式，展示在筛选范围内，所有成员的最终得分分布情况（例如，90-100分有多少人，80-89分有多少人等）。
*   **价值**: 帮助管理者了解团队整体表现的正态分布情况，是“纺锤形”（大部分人居中）还是“哑铃型”（两极分化严重）。

#### 3. 核心交互与操作
*   **逐层下钻**: 页面的核心交互是**“总览 -> 选择 -> 下钻”**。用户从规则总览表格中发现问题线索，然后点击进入针对该问题的深度分析，再从团队排名下钻到个人，最后通过典型案例触达问题的原始场景。这是一个非常强大和高效的分析路径。
*   **问题定位**: 整个页面的设计都是为了帮助管理者**精准定位问题**：不仅知道“是什么问题”（规则本身），还知道“问题有多严重”（得分率和趋势），以及“问题出在谁身上”（团队和个人排名）。
*   **场景还原**: “典型问题录音”功能是点睛之笔，它将抽象的数据与具体的服务场景联系起来，让分析和辅导更有说服力。

---